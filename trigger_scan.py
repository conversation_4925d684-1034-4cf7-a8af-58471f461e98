#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
触发文件夹扫描以测试图像特征提取
"""

import os
import sys
import requests
import json

def trigger_folder_scan():
    """触发文件夹扫描"""
    
    print("🔄 触发文件夹扫描")
    print("=" * 50)
    
    try:
        # API基础URL
        base_url = "http://localhost:8086/api"
        
        # 1. 获取现有文件夹列表
        print("📋 获取现有文件夹列表...")
        response = requests.get(f"{base_url}/folders")
        
        if response.status_code == 200:
            folders = response.json().get('folders', [])
            print(f"✅ 找到 {len(folders)} 个文件夹")
            
            for folder in folders:
                print(f"  - 文件夹: {folder['name']} (ID: {folder['id']}, 路径: {folder['path']})")
        else:
            print(f"❌ 获取文件夹列表失败: {response.status_code}")
            return False
        
        # 2. 删除现有文件夹（如果存在）
        if folders:
            folder_to_delete = folders[0]  # 删除第一个文件夹
            folder_id = folder_to_delete['id']
            
            print(f"\n🗑️ 删除文件夹 '{folder_to_delete['name']}' (ID: {folder_id})...")
            delete_response = requests.delete(f"{base_url}/folders/{folder_id}")
            
            if delete_response.status_code == 200:
                print("✅ 文件夹删除成功")
            else:
                print(f"❌ 文件夹删除失败: {delete_response.status_code}")
                print(f"响应: {delete_response.text}")
        
        # 3. 重新添加文件夹
        print(f"\n➕ 重新添加文件夹...")
        folder_data = {
            "name": "2",
            "path": "C:/321/2",
            "description": "测试文件夹",
            "allow_read": True,
            "allow_write": True,
            "allow_upload": True,
            "allow_download": True,
            "allow_delete": False
        }
        
        add_response = requests.post(
            f"{base_url}/folders",
            json=folder_data,
            headers={"Content-Type": "application/json"}
        )
        
        if add_response.status_code == 200:
            result = add_response.json()
            print("✅ 文件夹添加成功")
            print(f"  - 新文件夹ID: {result.get('folder_id')}")
            print(f"  - 扫描结果: {result.get('scan_result', {})}")
            return True
        else:
            print(f"❌ 文件夹添加失败: {add_response.status_code}")
            print(f"响应: {add_response.text}")
            return False
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_image_features():
    """检查图像特征提取结果"""
    
    print("\n" + "=" * 50)
    print("🔍 检查图像特征提取结果")
    print("=" * 50)
    
    try:
        base_url = "http://localhost:8086/api"
        
        # 获取文件列表
        print("📋 获取文件列表...")
        response = requests.get(f"{base_url}/folders")
        
        if response.status_code == 200:
            folders = response.json().get('folders', [])
            
            for folder in folders:
                folder_id = folder['id']
                print(f"\n📁 文件夹: {folder['name']} (ID: {folder_id})")
                
                # 获取文件夹中的文件
                files_response = requests.get(f"{base_url}/folders/{folder_id}/files")
                
                if files_response.status_code == 200:
                    files_data = files_response.json()
                    files = files_data.get('files', [])
                    
                    print(f"  📊 文件数量: {len(files)}")
                    
                    for file_info in files:
                        print(f"    📄 {file_info['filename']}")
                        print(f"      - 大小: {file_info['size']} 字节")
                        print(f"      - 类型: {file_info.get('type', 'unknown')}")
                        print(f"      - 是否为图像: {file_info.get('is_image', False)}")
                        
                        # 检查是否有缩略图
                        if file_info.get('has_thumbnail'):
                            print(f"      - 缩略图: ✅ 有")
                        else:
                            print(f"      - 缩略图: ❌ 无")
                else:
                    print(f"    ❌ 获取文件列表失败: {files_response.status_code}")
        else:
            print(f"❌ 获取文件夹列表失败: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始触发文件夹扫描测试")
    
    # 触发扫描
    success1 = trigger_folder_scan()
    
    # 等待一下让扫描完成
    if success1:
        import time
        print("\n⏳ 等待扫描完成...")
        time.sleep(3)
        
        # 检查结果
        success2 = check_image_features()
    else:
        success2 = False
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  - 文件夹扫描: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  - 特征检查: {'✅ 成功' if success2 else '❌ 失败'}")
    
    input("\n按回车键退出...")
