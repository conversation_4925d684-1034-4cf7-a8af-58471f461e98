<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE colormap [
<!ELEMENT colormap (color)*>
<!ELEMENT color (#PCDATA)>
<!ATTLIST color name CDATA "0">
<!ATTLIST color color CDATA "rgb(0,0,0)">
<!ATTLIST color compliance CDATA "SVG">
]>
<!--
  Associate a color name with its red, green, blue, and alpha intensities.

  A number of methods and options require a color parameter. It is often
  convenient to refer to a color by name (e.g. white) rather than by hex
  value (e.g. #fff). This file maps a color name to its equivalent red,
  green, blue, and alpha intensities (e.g. for white, red = 255, green =
  255, blue = 255, and alpha = 0).
-->
<colormap>
  <!-- <color name="none" color="rgba(0,0,0,0)" compliance="SVG, XPM"/> -->
  <!-- <color name="black" color="rgb(0,0,0)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="red" color="rgb(255,0,0)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="magenta" color="rgb(255,0,255)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="green" color="rgb(0,128,0)" compliance="SVG"/> -->
  <!-- <color name="cyan" color="rgb(0,255,255)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="blue" color="rgb(0,0,255)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="yellow" color="rgb(255,255,0)" compliance="SVG, X11, XPM"/> -->
  <!-- <color name="white" color="rgb(255,255,255)" compliance="SVG, X11"/> -->
</colormap>
