#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置管理模块
"""

import os
import sys
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List

def is_frozen():
    """检查是否为打包环境"""
    return getattr(sys, 'frozen', False)

def get_base_dir():
    """获取应用程序基础目录"""
    if is_frozen():
        # 打包环境
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))

class SystemSettings:
    """系统设置管理类"""
    
    def __init__(self, config_file: str = "config.yaml"):
        # 在打包环境中，配置文件位于可执行文件目录
        if is_frozen():
            self.config_file = Path(get_base_dir()) / "config" / config_file
        else:
            self.config_file = Path(__file__).parent / config_file
            
        self.settings = self._load_default_settings()
        self.load_settings()
    
    def _load_default_settings(self) -> Dict[str, Any]:
        """加载默认设置"""
        base_dir = get_base_dir()
        
        return {
            # 服务器设置
            "server": {
                "host": "0.0.0.0",
                "port": 8080,
                "debug": False,
                "max_workers": 10,
                "timeout": 300  # 5分钟超时，适用于大文件处理
            },
            
            # 数据库设置
            "database": {
                "database_path": os.path.join(base_dir, "backend", "data", "file_share_system.db")
            },
            
            # 文件共享设置
            "file_share": {
                "shared_folders": [],
                "max_file_size": 1024 * 1024 * 1024,  # 1GB
                "allowed_extensions": [
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
                    ".psd", ".ai", ".eps", ".pdf", ".doc", ".docx", ".xls", 
                    ".xlsx", ".ppt", ".pptx", ".txt", ".zip", ".rar", ".7z"
                ],
                "thumbnail_sizes": {
                    "small": (150, 150),
                    "medium": (300, 300),
                    "large": (600, 600),
                    "xlarge": (1200, 1200)
                }
            },
            
            # 权限设置
            "permissions": {
                "default_user_permissions": ["read", "download"],
                "admin_permissions": ["read", "write", "delete", "admin", "download"],
                "guest_permissions": ["read"]  # 访客只有读取权限，没有下载权限
            },
            
            # 搜索引擎设置
            "search": {
                "enable_text_search": True,
                "enable_image_search": True,
                "enable_image_upload_search": True,  # 以图搜图功能开关
                "index_path": os.path.join(base_dir, "data", "search_index"),
                "max_search_results": 1000,
                "image_search_threshold": 0.7,  # 图像相似度阈值
                "max_upload_image_size": 10 * 1024 * 1024  # 10MB上传限制
            },
            
            # 下载设置
            "download": {
                "enable_single_download": True,
                "enable_batch_download": True,
                "enable_folder_download": True,
                "max_batch_files": 100,
                "max_package_size": 500 * 1024 * 1024,  # 500MB
                "encryption_after_downloads": 3,
                "password_request_limit": 5
            },
            
            # 监控设置
            "monitoring": {
                "enable_activity_log": True,
                "enable_real_time_monitor": True,
                "log_retention_days": 90,
                "alert_thresholds": {
                    "max_concurrent_users": 100,
                    "max_download_speed": 10 * 1024 * 1024,  # 10MB/s
                    "max_search_per_minute": 60
                }
            },
            
            # 安全设置
            "security": {
                "enable_registration": False,
                "require_license_key": True,
                "session_timeout": 3600,  # 1小时
                "max_login_attempts": 5,
                "ban_duration": 300,  # 5分钟
                "sensitive_file_patterns": ["*secret*", "*private*", "*confidential*"]
            },
            
            # 网络设置
            "network": {
                "enable_internal_access": True,
                "enable_external_access": False,
                "internal_networks": ["192.168.0.0/16", "10.0.0.0/8", "172.16.0.0/12"],
                "rate_limit": {
                    "requests_per_minute": 60,
                    "downloads_per_hour": 100
                }
            },
            
            # 通知设置
            "notifications": {
                "enable_rolling_notifications": True,
                "enable_screenshots": True,
                "notification_duration": 5000,  # 5秒
                "max_notifications": 10
            },
            
            # 滚动字幕设置
            "marquee": {
                "enabled": True,
                "message": "欢迎使用企业级文件共享系统！系统功能完善，支持多格式图片预览、智能搜索、安全下载等功能。",
                "theme": "",  # info, success, warning, error 或空字符串
                "hidden": False,
                "animation_duration": 20  # 滚动动画持续时间（秒）
            },
            
            # 系统设置
            "system": {
                "data_directory": os.path.join(base_dir, "data"),
                "log_directory": os.path.join(base_dir, "logs"),
                "temp_directory": os.path.join(base_dir, "temp"),
                "backup_directory": os.path.join(base_dir, "backup"),
                "language": "zh_CN",
                "timezone": "Asia/Shanghai"
            }
        }
    
    def load_settings(self):
        """从文件加载设置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_settings = yaml.safe_load(f)
                    if file_settings:
                        self._merge_settings(self.settings, file_settings)
            else:
                # 如果配置文件不存在，创建配置目录并保存默认配置
                self.save_settings()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def save_settings(self):
        """保存设置到文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.settings, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_settings(self, default: Dict, custom: Dict):
        """合并设置"""
        for key, value in custom.items():
            if key in default and isinstance(default[key], dict) and isinstance(value, dict):
                self._merge_settings(default[key], value)
            else:
                default[key] = value
    
    def get(self, key_path: str, default=None):
        """获取设置值"""
        keys = key_path.split('.')
        value = self.settings
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """设置值"""
        keys = key_path.split('.')
        setting = self.settings
        
        for key in keys[:-1]:
            if key not in setting:
                setting[key] = {}
            setting = setting[key]
        
        setting[keys[-1]] = value
    
    def get_shared_folders(self) -> List[Dict[str, Any]]:
        """获取共享文件夹列表"""
        return self.get("file_share.shared_folders", [])
    
    def add_shared_folder(self, folder_info: Dict[str, Any]):
        """添加共享文件夹"""
        folders = self.get_shared_folders()
        folders.append(folder_info)
        self.set("file_share.shared_folders", folders)
        self.save_settings()
    
    def remove_shared_folder(self, folder_id: str):
        """移除共享文件夹"""
        folders = self.get_shared_folders()
        folders = [f for f in folders if f.get('id') != folder_id]
        self.set("file_share.shared_folders", folders)
        self.save_settings()
    
    def update_shared_folder(self, folder_id: str, updates: Dict[str, Any]):
        """更新共享文件夹设置"""
        folders = self.get_shared_folders()
        for folder in folders:
            if folder.get('id') == folder_id:
                folder.update(updates)
                break
        self.set("file_share.shared_folders", folders)
        self.save_settings()
