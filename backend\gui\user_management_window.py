#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional
from datetime import datetime, timezone, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.time_utils import TimeUtils

class UserManagementWindow:
    """用户管理窗口类"""

    def __init__(self, parent, server_instance):
        self.parent = parent
        self.server = server_instance
        self.window = None
        self.users_tree = None
        self.current_users = []
        
    def show(self):
        """显示用户管理窗口"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("用户管理")
        self.window.geometry("1000x700")
        self.window.transient(self.parent)

        # 设置主题
        self.setup_theme()

        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.create_widgets()
        self.load_users()

    def setup_theme(self):
        """设置窗口主题"""
        try:
            # 尝试使用ttkthemes提供的更好主题
            from ttkthemes import ThemedStyle
            style = ThemedStyle(self.window)
            style.theme_use('vista')  # 使用与主窗口相同的主题
        except ImportError:
            # 如果ttkthemes不可用，使用标准ttk样式
            style = ttk.Style()
            available_themes = style.theme_names()
            if 'vista' in available_themes:
                style.theme_use('vista')
            elif 'winnative' in available_themes:
                style.theme_use('winnative')
            else:
                style.theme_use('default')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="用户管理", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="添加用户", command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="编辑用户", command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="删除用户", command=self.delete_user).pack(side=tk.LEFT, padx=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind('<Return>', lambda e: self.search_users())
        ttk.Button(search_frame, text="搜索", command=self.search_users).pack(side=tk.LEFT)
        
        # 用户列表
        list_frame = ttk.LabelFrame(main_frame, text="用户列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树形视图
        columns = ("ID", "用户名", "姓名", "邮箱", "用户组", "状态", "最后登录", "登录次数")
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"ID": 50, "用户名": 100, "姓名": 100, "邮箱": 150, 
                        "用户组": 80, "状态": 80, "最后登录": 150, "登录次数": 80}
        
        for col in columns:
            self.users_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.users_tree.column(col, width=column_widths.get(col, 100))
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定双击事件
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user())
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(status_frame, text="用户数: 0")
        self.count_label.pack(side=tk.RIGHT)

    def format_china_time(self, time_str):
        """格式化为中国时区时间"""
        return TimeUtils.format_china_datetime_display(time_str)

    def load_users(self):
        """加载用户列表"""
        try:
            self.status_label.config(text="正在加载用户...")

            # 清空现有数据
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # 获取用户服务
            user_service = self.server.services.get('user')

            # 如果用户服务不存在，尝试重新创建
            if not user_service:
                try:
                    from services.user_service import UserService
                    if hasattr(self.server, 'db_manager') and self.server.db_manager:
                        user_service = UserService(self.server.db_manager)
                        self.server.services['user'] = user_service
                        print("✅ 用户服务重新创建成功")
                    else:
                        self.status_label.config(text="数据库管理器不可用")
                        return
                except Exception as e:
                    print(f"❌ 重新创建用户服务失败: {e}")
                    self.status_label.config(text="用户服务不可用 - 尝试直接从数据库加载")
                    self.load_users_from_db()
                    return

            # 获取用户列表
            result = user_service.get_user_list(page=1, page_size=1000)

            if result.get('success', False):
                users = result.get('users', [])
                self.current_users = users

                if not users:
                    self.status_label.config(text="数据库中没有用户数据 - 尝试创建测试用户")
                    self.create_test_users()
                    return

                # 添加用户到树形视图
                for user in users:
                    status = "正常"
                    if user.get('is_banned', False):
                        status = "禁用"
                    elif not user.get('is_active', True):
                        status = "未激活"

                    last_login = user.get('last_login', '')
                    # 格式化为中国时区时间
                    last_login = self.format_china_time(last_login)

                    self.users_tree.insert('', 'end', values=(
                        user.get('id', ''),
                        user.get('username', ''),
                        user.get('full_name', ''),
                        user.get('email', ''),
                        user.get('user_group', ''),
                        status,
                        last_login,
                        user.get('login_count', 0)
                    ))

                self.count_label.config(text=f"用户数: {len(users)}")
                self.status_label.config(text="加载完成")
            else:
                self.status_label.config(text=f"加载失败: {result.get('error', '未知错误')} - 尝试直接从数据库加载")
                self.load_users_from_db()

        except Exception as e:
            self.status_label.config(text=f"加载失败: {e}")
            messagebox.showerror("错误", f"加载用户列表失败: {e}")
            # 尝试直接从数据库加载
            self.load_users_from_db()

    def load_users_from_db(self):
        """直接从数据库加载用户数据"""
        try:
            import sqlite3
            import os

            db_path = os.path.join('data', 'file_share_system.db')
            if not os.path.exists(db_path):
                self.status_label.config(text="数据库文件不存在")
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, username, email, full_name, user_group, is_active,
                       is_admin, is_banned, last_login, login_count
                FROM users
                ORDER BY id
            """)

            users = cursor.fetchall()
            conn.close()

            if not users:
                self.status_label.config(text="数据库中没有用户数据")
                return

            # 清空现有数据
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # 添加用户到树形视图
            for user in users:
                status = "正常"
                if user[7]:  # is_banned
                    status = "禁用"
                elif not user[5]:  # is_active
                    status = "未激活"

                last_login = user[8] if user[8] else ''
                # 格式化为中国时区时间
                last_login = self.format_china_time(last_login)

                self.users_tree.insert('', 'end', values=(
                    user[0],  # id
                    user[1],  # username
                    user[3] or '',  # full_name
                    user[2] or '',  # email
                    user[4],  # user_group
                    status,
                    last_login,
                    user[9] or 0  # login_count
                ))

            self.current_users = [
                {
                    'id': user[0],
                    'username': user[1],
                    'email': user[2],
                    'full_name': user[3],
                    'user_group': user[4],
                    'is_active': user[5],
                    'is_admin': user[6],
                    'is_banned': user[7],
                    'last_login': user[8],
                    'login_count': user[9]
                }
                for user in users
            ]

            self.count_label.config(text=f"用户数: {len(users)}")
            self.status_label.config(text="从数据库直接加载完成")

        except Exception as e:
            self.status_label.config(text=f"数据库加载失败: {e}")
            messagebox.showerror("错误", f"从数据库加载用户失败: {e}")

    def create_test_users(self):
        """创建测试用户"""
        try:
            import sqlite3
            import os
            import hashlib
            import secrets
            from datetime import datetime

            db_path = os.path.join('data', 'file_share_system.db')
            if not os.path.exists(db_path):
                self.status_label.config(text="数据库文件不存在")
                return

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 创建管理员用户
            salt = secrets.token_hex(16)
            password_hash = hashlib.sha256(('admin123' + salt).encode()).hexdigest()

            cursor.execute("""
                INSERT OR IGNORE INTO users
                (username, password_hash, salt, email, full_name, user_group, is_active, is_admin, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('admin', password_hash, salt, '<EMAIL>', '系统管理员', 'admin', 1, 1, datetime.now()))

            # 创建普通用户
            salt2 = secrets.token_hex(16)
            password_hash2 = hashlib.sha256(('user123' + salt2).encode()).hexdigest()

            cursor.execute("""
                INSERT OR IGNORE INTO users
                (username, password_hash, salt, email, full_name, user_group, is_active, is_admin, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, ('user1', password_hash2, salt2, '<EMAIL>', '测试用户1', 'user', 1, 0, datetime.now()))

            conn.commit()
            conn.close()

            self.status_label.config(text="测试用户创建完成，正在重新加载...")
            messagebox.showinfo("成功", "已创建测试用户:\n管理员: admin/admin123\n用户: user1/user123")

            # 重新加载用户列表
            self.load_users_from_db()

        except Exception as e:
            self.status_label.config(text=f"创建测试用户失败: {e}")
            messagebox.showerror("错误", f"创建测试用户失败: {e}")

    def search_users(self):
        """搜索用户"""
        try:
            search_query = self.search_var.get().strip()
            
            if not search_query:
                self.load_users()
                return
            
            self.status_label.config(text="正在搜索...")
            
            # 清空现有数据
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # 在当前用户列表中搜索
            filtered_users = []
            for user in self.current_users:
                if (search_query.lower() in user.get('username', '').lower() or
                    search_query.lower() in user.get('full_name', '').lower() or
                    search_query.lower() in user.get('email', '').lower()):
                    filtered_users.append(user)
            
            # 添加搜索结果到树形视图
            for user in filtered_users:
                status = "正常"
                if user.get('is_banned', False):
                    status = "禁用"
                elif not user.get('is_active', True):
                    status = "未激活"
                
                last_login = user.get('last_login', '')
                # 格式化为中国时区时间
                last_login = self.format_china_time(last_login)
                
                self.users_tree.insert('', 'end', values=(
                    user.get('id', ''),
                    user.get('username', ''),
                    user.get('full_name', ''),
                    user.get('email', ''),
                    user.get('user_group', ''),
                    status,
                    last_login,
                    user.get('login_count', 0)
                ))
            
            self.count_label.config(text=f"搜索结果: {len(filtered_users)}")
            self.status_label.config(text="搜索完成")
            
        except Exception as e:
            self.status_label.config(text=f"搜索失败: {e}")
    
    def add_user(self):
        """添加用户"""
        print("🔧 开始添加用户...")
        try:
            print("📋 创建UserEditDialog实例...")
            dialog = UserEditDialog(self.window, "添加用户")
            print("✅ UserEditDialog实例创建成功")
            print("📋 调用dialog.show()...")
            result = dialog.show()
        except Exception as e:
            print(f"❌ 创建或显示对话框时出现异常: {e}")
            import traceback
            traceback.print_exc()
            result = None

        print(f"📋 对话框返回结果: {result}")

        if result:
            try:
                print(f"📤 准备创建用户，参数: {result}")
                print(f"🔍 服务器实例类型: {type(self.server)}")
                print(f"🔍 服务器实例属性: {dir(self.server)}")
                print(f"🔍 服务器services: {self.server.services}")
                print(f"🔍 服务器services类型: {type(self.server.services)}")

                user_service = self.server.services.get('user')
                print(f"🔍 获取到的用户服务: {user_service}")

                # 如果用户服务不存在或无效，尝试重新创建
                if not user_service or not hasattr(user_service, 'create_user'):
                    print("⚠️ 用户服务无效，尝试重新创建...")
                    try:
                        from services.user_service import UserService
                        if hasattr(self.server, 'db_manager') and self.server.db_manager:
                            user_service = UserService(self.server.db_manager)
                            self.server.services['user'] = user_service
                            print("✅ 用户服务重新创建成功")
                        else:
                            print("❌ 数据库管理器不可用")
                            messagebox.showerror("错误", "数据库管理器不可用，无法创建用户")
                            return
                    except Exception as e:
                        print(f"❌ 重新创建用户服务失败: {e}")
                        messagebox.showerror("错误", f"重新创建用户服务失败: {e}")
                        return

                if user_service:
                    print("✅ 用户服务可用，开始创建用户...")
                    print(f"🔍 用户服务类型: {type(user_service)}")

                    # 检查是否有create_user方法
                    if hasattr(user_service, 'create_user'):
                        print("✅ create_user方法存在")
                        create_result = user_service.create_user(**result)
                        print(f"📋 创建结果: {create_result}")
                    else:
                        print("❌ create_user方法不存在")
                        messagebox.showerror("错误", "用户服务缺少create_user方法")
                        return

                    if create_result.get('success', False):
                        messagebox.showinfo("成功", "用户创建成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"创建用户失败: {create_result.get('error', '未知错误')}")
                else:
                    print("❌ 用户服务不可用")
                    messagebox.showerror("错误", "用户服务不可用")

            except Exception as e:
                print(f"❌ 创建用户异常: {e}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("错误", f"创建用户失败: {e}")
        else:
            print("对话框被取消或没有返回结果")
    
    def edit_user(self):
        """编辑用户"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的用户")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        
        # 查找用户数据
        user_data = None
        for user in self.current_users:
            if user.get('id') == user_id:
                user_data = user
                break
        
        if not user_data:
            messagebox.showerror("错误", "找不到用户数据")
            return
        
        dialog = UserEditDialog(self.window, "编辑用户", user_data)
        result = dialog.show()
        
        if result:
            try:
                user_service = self.server.services.get('user')
                if user_service:
                    update_result = user_service.update_user(user_id, result)
                    
                    if update_result.get('success', False):
                        messagebox.showinfo("成功", "用户更新成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"更新用户失败: {update_result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "用户服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"更新用户失败: {e}")
    
    def delete_user(self):
        """删除用户"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的用户")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]
        
        if messagebox.askyesno("确认", f"确定要删除用户 '{username}' 吗？\n此操作不可撤销！"):
            try:
                user_service = self.server.services.get('user')
                if user_service:
                    delete_result = user_service.delete_user(user_id)
                    
                    if delete_result.get('success', False):
                        messagebox.showinfo("成功", "用户删除成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"删除用户失败: {delete_result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "用户服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除用户失败: {e}")
    
    def sort_by_column(self, column):
        """按列排序"""
        # 这里可以实现排序功能
        pass
    











    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None

class UserEditDialog:
    """用户编辑对话框"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.title = title
        self.user_data = user_data or {}
        self.result = None
        self.dialog = None
    
    def show(self):
        """显示对话框"""
        print(f"🔧 显示对话框: {self.title}")
        try:
            self.dialog = tk.Toplevel(self.parent)
            self.dialog.title(self.title)
            self.dialog.geometry("400x500")
            self.dialog.transient(self.parent)
            self.dialog.grab_set()

            # 居中显示
            self.dialog.update_idletasks()
            x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
            self.dialog.geometry(f"400x500+{x}+{y}")

            print("📋 创建对话框组件...")
            self.create_widgets()

            print("⏳ 等待对话框关闭...")
            # 等待对话框关闭
            self.dialog.wait_window()

            print(f"✅ 对话框已关闭，返回结果: {self.result}")
            return self.result

        except Exception as e:
            print(f"❌ 对话框显示异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_widgets(self):
        """创建对话框组件"""
        print("🔧 开始创建对话框组件...")
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar(value=self.user_data.get('username', ''))
        username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 姓名
        ttk.Label(main_frame, text="姓名:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.full_name_var = tk.StringVar(value=self.user_data.get('full_name', ''))
        full_name_entry = ttk.Entry(main_frame, textvariable=self.full_name_var, width=30)
        full_name_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 邮箱
        ttk.Label(main_frame, text="邮箱:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar(value=self.user_data.get('email', ''))
        email_entry = ttk.Entry(main_frame, textvariable=self.email_var, width=30)
        email_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # 用户组
        ttk.Label(main_frame, text="用户组:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.user_group_var = tk.StringVar(value=self.user_data.get('user_group', 'user'))
        user_group_combo = ttk.Combobox(main_frame, textvariable=self.user_group_var, 
                                       values=['admin', 'user', 'guest'], width=27)
        user_group_combo.grid(row=4, column=1, sticky=tk.W, pady=5)
        
        # 是否管理员
        self.is_admin_var = tk.BooleanVar(value=self.user_data.get('is_admin', False))
        admin_check = ttk.Checkbutton(main_frame, text="管理员", variable=self.is_admin_var)
        admin_check.grid(row=5, column=1, sticky=tk.W, pady=5)
        
        # 是否激活
        self.is_active_var = tk.BooleanVar(value=self.user_data.get('is_active', True))
        active_check = ttk.Checkbutton(main_frame, text="激活", variable=self.is_active_var)
        active_check.grid(row=6, column=1, sticky=tk.W, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)

        print("✅ 对话框组件创建完成")
    
    def ok_clicked(self):
        """确定按钮点击"""
        print("🔧 用户点击了确定按钮")
        username = self.username_var.get().strip()
        password = self.password_var.get()

        print(f"📋 输入的用户名: '{username}', 密码长度: {len(password) if password else 0}")

        if not username:
            print("❌ 用户名为空")
            messagebox.showerror("错误", "用户名不能为空")
            return

        if not self.user_data and not password:  # 新用户必须有密码
            print("❌ 新用户密码为空")
            messagebox.showerror("错误", "密码不能为空")
            return

        self.result = {
            'username': username,
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip(),
            'user_group': self.user_group_var.get(),
            'is_admin': self.is_admin_var.get(),
            'is_active': self.is_active_var.get()
        }

        if password:  # 只有在输入密码时才更新
            self.result['password'] = password

        print(f"✅ 准备返回结果: {self.result}")
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        print("❌ 用户点击了取消按钮")
        self.result = None
        self.dialog.destroy()
