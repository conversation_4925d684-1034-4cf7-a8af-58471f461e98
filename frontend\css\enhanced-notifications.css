/* 增强通知样式 */

/* 图片通知图标 */
.notification-image-icon {
    color: #3498db;
    margin-left: 8px;
    font-size: 0.9em;
    opacity: 0.8;
}

/* 自定义Toast样式 */
.custom-toast {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
    animation: slideInRight 0.3s ease;
}

.custom-toast:hover {
    transform: translateX(-5px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.4);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 图片通知模态框样式 */
.image-notification-modal {
    backdrop-filter: blur(4px);
}

.image-notification-modal .close-btn:hover {
    color: #333;
    transform: scale(1.1);
}

/* 通知列表中的图片标识 */
.notification-item.has-image {
    border-left: 4px solid #3498db;
}

.notification-item.has-image .notification-title {
    position: relative;
}

/* 通知类型颜色增强 */
.notification-item.info {
    border-left-color: #3498db;
}

.notification-item.success {
    border-left-color: #2ecc71;
}

.notification-item.warning {
    border-left-color: #f39c12;
}

.notification-item.error {
    border-left-color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .custom-toast {
        max-width: 90%;
        margin: 0 5%;
    }
    
    .image-notification-modal div {
        max-width: 95%;
        max-height: 95%;
        padding: 15px;
    }
    
    .image-notification-modal img {
        max-height: 300px;
    }
}

/* 通知动画效果 */
.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.notification-item.unread {
    background-color: #fff3cd;
    border-left-width: 4px;
}

.notification-item.unread:hover {
    background-color: #ffeaa7;
}

/* 图片预览样式 */
.notification-image-preview {
    max-width: 100px;
    max-height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-top: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.notification-image-preview:hover {
    transform: scale(1.05);
}

/* 加载动画 */
.notification-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知计数器样式增强 */
.notification-badge {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* 通知面板增强 */
.notification-panel {
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

.notification-panel .notification-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.notification-panel .notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-panel .notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-panel .notification-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notification-panel .notification-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notification-panel .notification-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 空通知状态 */
.empty-notifications {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-notifications i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-notifications p {
    font-size: 16px;
    margin: 0;
}
