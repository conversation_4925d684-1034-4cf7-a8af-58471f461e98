#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件扫描功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.database.db_manager import DatabaseManager
from backend.services.file_service import FileService
from backend.models.file_share import SharedFolder

def test_scan_function():
    """测试扫描功能"""
    print("🔍 测试文件扫描功能...")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 初始化文件服务
        file_service = FileService(db_manager)
        
        # 获取共享文件夹列表
        with db_manager.get_session() as session:
            folders = session.query(SharedFolder).all()
            
            if not folders:
                print("❌ 没有找到共享文件夹")
                return False
            
            print(f"📁 找到 {len(folders)} 个共享文件夹:")
            for folder in folders:
                print(f"   - ID: {folder.id}, 名称: {folder.name}, 路径: {folder.path}")
            
            # 测试扫描第一个文件夹
            test_folder = folders[0]
            print(f"\n🔍 测试扫描文件夹: {test_folder.name} ({test_folder.path})")
            
            # 检查文件夹是否存在
            if not os.path.exists(test_folder.path):
                print(f"❌ 文件夹路径不存在: {test_folder.path}")
                return False
            
            # 执行扫描
            result = file_service.scan_folder(test_folder.id)
            
            print(f"\n📊 扫描结果:")
            print(f"   成功: {result.get('success', False)}")
            print(f"   文件数量: {result.get('files_count', 0)}")
            print(f"   扫描数量: {result.get('scanned_count', 0)}")
            print(f"   总大小: {result.get('total_size', 0)} 字节")
            print(f"   文件夹名称: {result.get('folder_name', '')}")
            
            if 'thumbnail_generated' in result:
                print(f"   缩略图生成: {result.get('thumbnail_generated', 0)}")
                print(f"   缩略图失败: {result.get('thumbnail_failed', 0)}")
                print(f"   缩略图总数: {result.get('thumbnail_total', 0)}")
            
            if 'feature_extracted' in result:
                print(f"   特征提取: {result.get('feature_extracted', 0)}")
                print(f"   特征失败: {result.get('feature_failed', 0)}")
                print(f"   特征总数: {result.get('feature_total', 0)}")
            
            if result.get('success'):
                print("✅ 扫描功能测试成功")
                return True
            else:
                print(f"❌ 扫描失败: {result.get('error', '未知错误')}")
                return False
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chinese_files():
    """测试中文文件处理"""
    print("\n🔤 测试中文文件处理...")
    
    test_folder = "C:/321/2"
    if not os.path.exists(test_folder):
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return False
    
    print(f"📁 扫描文件夹: {test_folder}")
    
    chinese_files = []
    for filename in os.listdir(test_folder):
        file_path = os.path.join(test_folder, filename)
        if os.path.isfile(file_path):
            # 检查是否包含中文字符
            if any('\u4e00' <= char <= '\u9fff' for char in filename):
                chinese_files.append((filename, file_path))
    
    print(f"🔍 找到 {len(chinese_files)} 个中文文件名的文件:")
    for filename, file_path in chinese_files:
        print(f"   - {filename}")
        print(f"     路径: {file_path}")
        print(f"     存在: {os.path.exists(file_path)}")
        
        # 测试文件读取
        try:
            with open(file_path, 'rb') as f:
                data = f.read(100)  # 读取前100字节
            print(f"     可读取: ✅ ({len(data)} 字节)")
        except Exception as e:
            print(f"     可读取: ❌ ({e})")
        print()
    
    return len(chinese_files) > 0

if __name__ == "__main__":
    print("🚀 开始测试文件扫描功能...")
    
    # 测试中文文件处理
    chinese_test = test_chinese_files()
    
    # 测试扫描功能
    scan_test = test_scan_function()
    
    if scan_test:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 测试失败")
