#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目录服务 - 处理文件夹层级结构
"""

import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from models.file_share import SharedFolder, SharedFile, SharedDirectory
from utils.logger import setup_logger

class DirectoryService:
    """目录服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
    
    def scan_folder_with_directories(self, folder_id: int) -> Dict[str, Any]:
        """扫描文件夹，创建目录结构"""
        try:
            with self.db_manager.get_session() as session:
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                if not folder.is_path_valid():
                    return {"success": False, "error": "文件夹路径无效"}
                
                # 清理现有的目录和文件记录
                session.query(SharedDirectory).filter_by(folder_id=folder_id).delete()
                session.query(SharedFile).filter_by(folder_id=folder_id).delete()
                
                # 扫描目录结构
                directories_created = 0
                files_created = 0
                total_size = 0
                
                # 创建目录映射：相对路径 -> SharedDirectory对象
                directory_map = {}
                
                # 遍历文件夹
                for root, dirs, files in os.walk(folder.path):
                    # 计算相对路径
                    relative_root = os.path.relpath(root, folder.path)
                    
                    # 如果不是根目录，创建目录记录
                    if relative_root != '.':
                        directory_obj = self._create_directory_record(
                            session, folder_id, relative_root, directory_map
                        )
                        if directory_obj:
                            directories_created += 1
                    
                    # 处理当前目录中的文件
                    for filename in files:
                        file_path = os.path.join(root, filename)
                        relative_file_path = os.path.relpath(file_path, folder.path)
                        
                        try:
                            # 获取文件信息
                            stat = os.stat(file_path)
                            file_size = stat.st_size
                            file_modified = datetime.fromtimestamp(stat.st_mtime)
                            
                            # 确定文件所属的目录ID
                            directory_id = None
                            if relative_root != '.':
                                directory_obj = directory_map.get(relative_root)
                                if directory_obj:
                                    directory_id = directory_obj.id
                            
                            # 创建文件记录
                            new_file = SharedFile(
                                folder_id=folder_id,
                                directory_id=directory_id,
                                filename=filename,
                                relative_path=relative_file_path,
                                file_size=file_size,
                                file_modified=file_modified
                            )
                            new_file.update_file_info()
                            session.add(new_file)
                            
                            files_created += 1
                            total_size += file_size
                            
                        except Exception as e:
                            self.logger.warning(f"处理文件失败 {file_path}: {e}")
                            continue
                
                # 更新目录统计信息
                self._update_directory_statistics(session, folder_id)
                
                # 更新文件夹统计信息
                folder.file_count = files_created
                folder.total_size = total_size
                folder.last_scanned = datetime.now()
                
                session.commit()
                
                self.logger.info(f"扫描完成 - 目录: {directories_created}, 文件: {files_created}")
                
                return {
                    "success": True,
                    "directories_created": directories_created,
                    "files_created": files_created,
                    "total_size": total_size
                }
                
        except Exception as e:
            self.logger.error(f"扫描文件夹失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _create_directory_record(self, session, folder_id: int, relative_path: str, 
                                directory_map: Dict[str, SharedDirectory]) -> Optional[SharedDirectory]:
        """创建目录记录"""
        try:
            # 如果已经创建过，直接返回
            if relative_path in directory_map:
                return directory_map[relative_path]
            
            # 分析路径层级
            path_parts = relative_path.split(os.sep)
            directory_name = path_parts[-1]
            
            # 确定父目录
            parent_id = None
            if len(path_parts) > 1:
                parent_path = os.sep.join(path_parts[:-1])
                parent_obj = self._create_directory_record(session, folder_id, parent_path, directory_map)
                if parent_obj:
                    parent_id = parent_obj.id
            
            # 创建目录记录
            directory = SharedDirectory(
                folder_id=folder_id,
                parent_id=parent_id,
                name=directory_name,
                relative_path=relative_path
            )
            
            session.add(directory)
            session.flush()  # 获取ID
            
            directory_map[relative_path] = directory
            
            return directory
            
        except Exception as e:
            self.logger.error(f"创建目录记录失败 {relative_path}: {e}")
            return None
    
    def _update_directory_statistics(self, session, folder_id: int):
        """更新目录统计信息"""
        try:
            directories = session.query(SharedDirectory).filter_by(folder_id=folder_id).all()
            
            for directory in directories:
                # 统计直接文件数量
                direct_files = session.query(SharedFile).filter_by(
                    folder_id=folder_id, 
                    directory_id=directory.id
                ).count()
                
                # 统计子目录数量
                child_dirs = session.query(SharedDirectory).filter_by(
                    folder_id=folder_id,
                    parent_id=directory.id
                ).count()
                
                # 计算总文件数量（包括子目录）
                total_files = self._count_total_files(session, folder_id, directory.id)
                
                # 计算总大小
                total_size = self._calculate_total_size(session, folder_id, directory.id)
                
                # 更新统计信息
                directory.file_count = direct_files
                directory.directory_count = child_dirs
                directory.total_file_count = total_files
                directory.total_size = total_size
                directory.last_scanned = datetime.now()
                
        except Exception as e:
            self.logger.error(f"更新目录统计失败: {e}")
    
    def _count_total_files(self, session, folder_id: int, directory_id: int) -> int:
        """递归计算目录及其子目录的总文件数量"""
        try:
            # 直接文件数量
            direct_files = session.query(SharedFile).filter_by(
                folder_id=folder_id,
                directory_id=directory_id
            ).count()
            
            # 子目录文件数量
            child_dirs = session.query(SharedDirectory).filter_by(
                folder_id=folder_id,
                parent_id=directory_id
            ).all()
            
            total_files = direct_files
            for child_dir in child_dirs:
                total_files += self._count_total_files(session, folder_id, child_dir.id)
            
            return total_files
            
        except Exception as e:
            self.logger.error(f"计算总文件数量失败: {e}")
            return 0
    
    def _calculate_total_size(self, session, folder_id: int, directory_id: int) -> int:
        """递归计算目录及其子目录的总大小"""
        try:
            # 直接文件大小
            direct_files = session.query(SharedFile).filter_by(
                folder_id=folder_id,
                directory_id=directory_id
            ).all()
            
            direct_size = sum(file.file_size for file in direct_files)
            
            # 子目录大小
            child_dirs = session.query(SharedDirectory).filter_by(
                folder_id=folder_id,
                parent_id=directory_id
            ).all()
            
            total_size = direct_size
            for child_dir in child_dirs:
                total_size += self._calculate_total_size(session, folder_id, child_dir.id)
            
            return total_size
            
        except Exception as e:
            self.logger.error(f"计算总大小失败: {e}")
            return 0
    
    def get_directory_contents(self, folder_id: int, directory_id: int = None, 
                              page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """获取目录内容（子目录和文件）"""
        try:
            with self.db_manager.get_session() as session:
                # 检查文件夹权限
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if not folder:
                    return {"success": False, "error": "文件夹不存在"}
                
                if not folder.allow_read:
                    return {"success": False, "error": "没有访问权限"}
                
                contents = []
                
                # 获取子目录
                directories = session.query(SharedDirectory).filter_by(
                    folder_id=folder_id,
                    parent_id=directory_id
                ).all()
                
                for directory in directories:
                    contents.append(directory.to_dict())
                
                # 获取文件
                files_query = session.query(SharedFile).filter_by(
                    folder_id=folder_id,
                    directory_id=directory_id
                )
                
                files = files_query.all()
                for file in files:
                    contents.append(file.to_dict())
                
                # 分页处理
                total_count = len(contents)
                start_idx = (page - 1) * page_size
                end_idx = start_idx + page_size
                paginated_contents = contents[start_idx:end_idx]
                
                return {
                    "success": True,
                    "contents": paginated_contents,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size,
                    "current_directory": directory_id
                }
                
        except Exception as e:
            self.logger.error(f"获取目录内容失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_directory_breadcrumb(self, folder_id: int, directory_id: int = None) -> List[Dict[str, Any]]:
        """获取目录面包屑导航"""
        try:
            with self.db_manager.get_session() as session:
                breadcrumb = []
                
                # 添加根目录
                folder = session.query(SharedFolder).filter_by(id=folder_id).first()
                if folder:
                    breadcrumb.append({
                        'id': None,
                        'name': folder.name,
                        'type': 'root'
                    })
                
                # 如果有指定目录，构建路径
                if directory_id:
                    directory = session.query(SharedDirectory).filter_by(id=directory_id).first()
                    if directory:
                        path = []
                        current = directory
                        while current:
                            path.insert(0, {
                                'id': current.id,
                                'name': current.name,
                                'type': 'directory'
                            })
                            current = current.parent
                        
                        breadcrumb.extend(path)
                
                return breadcrumb
                
        except Exception as e:
            self.logger.error(f"获取面包屑导航失败: {e}")
            return []
