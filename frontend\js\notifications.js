/**
 * 通知管理模块
 * 处理系统通知、实时通知、通知历史等功能
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.init();
    }
    
    /**
     * 初始化通知管理器
     */
    init() {
        this.bindEvents();
        this.loadNotifications();
        // 连接SocketIO
        this.connectSocketIO();
        this.startPeriodicCheck();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 通知按钮
        const notificationBtn = Utils.dom.$('#notifications-btn');
        if (notificationBtn) {
            Utils.event.on(notificationBtn, 'click', () => {
                this.toggleNotificationPanel();
            });
        }
        
        // 关闭通知面板
        const closePanel = Utils.dom.$('.close-panel');
        if (closePanel) {
            Utils.event.on(closePanel, 'click', () => {
                this.hideNotificationPanel();
            });
        }
        
        // 点击外部关闭面板
        Utils.event.on(document, 'click', (e) => {
            const panel = Utils.dom.$('#notification-panel');
            const btn = Utils.dom.$('#notifications-btn');
            
            if (panel && Utils.dom.hasClass(panel, 'show') && 
                !panel.contains(e.target) && !btn.contains(e.target)) {
                this.hideNotificationPanel();
            }
        });
    }
    
    /**
     * 加载通知
     */
    async loadNotifications() {
        try {
            // 检查SystemAPI是否可用
            if (typeof SystemAPI === 'undefined') {
                CONFIG.log('warn', 'SystemAPI not available, using mock notifications');
                this.notifications = this.getMockNotifications();
                this.updateNotificationDisplay();
                return;
            }

            // 检查getNotifications方法是否存在
            if (typeof SystemAPI.getNotifications !== 'function') {
                CONFIG.log('warn', 'SystemAPI.getNotifications not available, using mock notifications');
                this.notifications = this.getMockNotifications();
                this.updateNotificationDisplay();
                return;
            }

            const response = await SystemAPI.getNotifications();
            this.notifications = response.notifications || [];
            this.updateNotificationDisplay();
        } catch (error) {
            CONFIG.log('error', 'Failed to load notifications:', error);
            // 使用模拟通知作为后备
            this.notifications = this.getMockNotifications();
            this.updateNotificationDisplay();
        }
    }

    /**
     * 获取模拟通知（用于测试和后备）
     */
    getMockNotifications() {
        return [
            {
                id: 'welcome',
                title: '欢迎使用',
                message: '欢迎使用文件共享系统！',
                type: 'info',
                timestamp: new Date().toISOString(),
                read: false
            }
        ];
    }
    
    /**
     * 连接SocketIO
     */
    connectSocketIO() {
        try {
            // 检查SocketIO是否可用
            if (typeof io === 'undefined') {
                CONFIG.log('warn', 'SocketIO not available, using polling instead');
                return;
            }

            // 连接SocketIO - 连接到API服务器端口
            const apiPort = 8086; // API服务器端口
            const socketUrl = `${window.location.protocol}//${window.location.hostname}:${apiPort}`;
            this.socket = io(socketUrl, {
                transports: ['websocket', 'polling'],
                timeout: 5000,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });

            // 连接成功
            this.socket.on('connect', () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                CONFIG.log('info', 'SocketIO connected');
            });

            // 监听通知事件
            this.socket.on('notification', (data) => {
                CONFIG.log('info', 'Received notification:', data);
                this.handleRealtimeNotification(data);
            });

            // 监听增强通知事件
            this.socket.on('enhanced_notification', (data) => {
                CONFIG.log('info', 'Received enhanced notification:', data);
                this.handleEnhancedNotification(data);
            });

            // 连接断开
            this.socket.on('disconnect', (reason) => {
                this.isConnected = false;
                CONFIG.log('warn', 'SocketIO disconnected:', reason);
            });

            // 连接错误
            this.socket.on('connect_error', (error) => {
                CONFIG.log('error', 'SocketIO connection error:', error);
            });

        } catch (error) {
            CONFIG.log('error', 'Failed to connect SocketIO:', error);
        }
    }

    /**
     * 连接WebSocket (备用方法)
     */
    connectWebSocket() {
        try {
            const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${wsProtocol}//${window.location.host}/ws/notifications`;

            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                CONFIG.log('info', 'WebSocket connected');
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);

                    // 检查是否是增强通知
                    if (data.has_image !== undefined || data.scope !== undefined) {
                        this.handleEnhancedNotification(data);
                    } else {
                        this.handleRealtimeNotification(data);
                    }
                } catch (error) {
                    CONFIG.log('error', 'Failed to parse notification:', error);
                }
            };

            this.ws.onclose = () => {
                this.isConnected = false;
                CONFIG.log('warn', 'WebSocket disconnected');
                this.attemptReconnect();
            };

            this.ws.onerror = (error) => {
                CONFIG.log('error', 'WebSocket error:', error);
            };

        } catch (error) {
            CONFIG.log('error', 'Failed to connect WebSocket:', error);
        }
    }
    
    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            CONFIG.log('info', `Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
            
            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            CONFIG.log('error', 'Max reconnection attempts reached');
        }
    }
    
    /**
     * 处理实时通知
     */
    handleRealtimeNotification(notification) {
        // 添加到通知列表
        this.addNotification(notification);

        // 显示Toast通知
        this.showToastNotification(notification);

        // 播放通知声音
        this.playNotificationSound();

        // 更新显示
        this.updateNotificationDisplay();
    }

    /**
     * 处理增强通知（支持图片）
     */
    handleEnhancedNotification(notification) {
        // 添加到通知列表
        this.addNotification(notification);

        // 显示增强Toast通知
        this.showEnhancedToastNotification(notification);

        // 播放通知声音
        this.playNotificationSound();

        // 更新显示
        this.updateNotificationDisplay();

        // 如果有图片，显示图片通知
        if (notification.has_image && notification.image_data) {
            this.showImageNotification(notification);
        }
    }
    
    /**
     * 添加通知
     */
    addNotification(notification) {
        const notificationItem = {
            id: notification.id || Utils.generateId('notification'),
            title: notification.title || '系统通知',
            message: notification.message || '',
            type: notification.type || 'info',
            timestamp: notification.timestamp || new Date().toISOString(),
            read: false,
            ...notification
        };
        
        this.notifications.unshift(notificationItem);
        
        // 限制通知数量
        if (this.notifications.length > CONFIG.UI.NOTIFICATION.MAX_COUNT * 2) {
            this.notifications = this.notifications.slice(0, CONFIG.UI.NOTIFICATION.MAX_COUNT * 2);
        }
        
        this.unreadCount++;
    }
    
    /**
     * 显示Toast通知
     */
    showToastNotification(notification) {
        const type = notification.type || 'info';
        const message = notification.message || notification.title || '新通知';

        Components.Toast.show(message, type, CONFIG.UI.NOTIFICATION.DURATION);
    }

    /**
     * 显示增强Toast通知
     */
    showEnhancedToastNotification(notification) {
        const type = notification.type || 'info';
        const title = notification.title || '系统通知';
        const content = notification.content || notification.message || '';

        // 创建增强的Toast内容
        let toastContent = `<strong>${title}</strong>`;
        if (content && content !== title) {
            toastContent += `<br><span style="font-size: 0.9em;">${content}</span>`;
        }
        if (notification.has_image) {
            toastContent += `<br><small style="color: #666;"><i class="fas fa-image"></i> 包含图片</small>`;
        }

        // 显示增强Toast
        this.showCustomToast(toastContent, type, CONFIG.UI.NOTIFICATION.DURATION * 2);
    }

    /**
     * 显示自定义Toast
     */
    showCustomToast(content, type = 'info', duration = 5000) {
        // 创建Toast容器
        let toastContainer = document.getElementById('custom-toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'custom-toast-container';
            toastContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(toastContainer);
        }

        // 创建Toast元素
        const toast = document.createElement('div');
        toast.className = `custom-toast toast-${type}`;
        toast.innerHTML = content;
        toast.style.cssText = `
            background: ${this.getToastColor(type)};
            color: white;
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 350px;
            word-wrap: break-word;
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        toastContainer.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 点击关闭
        toast.addEventListener('click', () => {
            this.removeToast(toast);
        });

        // 自动关闭
        setTimeout(() => {
            this.removeToast(toast);
        }, duration);
    }

    /**
     * 获取Toast颜色
     */
    getToastColor(type) {
        const colors = {
            'info': '#3498db',
            'success': '#2ecc71',
            'warning': '#f39c12',
            'error': '#e74c3c'
        };
        return colors[type] || colors.info;
    }

    /**
     * 移除Toast
     */
    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    /**
     * 显示图片通知
     */
    showImageNotification(notification) {
        // 创建图片通知模态框
        const modal = document.createElement('div');
        modal.className = 'image-notification-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
            position: relative;
        `;

        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        `;
        closeBtn.addEventListener('click', () => {
            this.closeImageNotification(modal);
        });

        // 通知标题
        const title = document.createElement('h3');
        title.textContent = notification.title || '系统通知';
        title.style.marginTop = '0';

        // 通知内容
        const contentText = document.createElement('p');
        contentText.textContent = notification.content || notification.message || '';

        // 图片
        const img = document.createElement('img');
        img.src = `data:image/jpeg;base64,${notification.image_data}`;
        img.style.cssText = `
            max-width: 100%;
            max-height: 400px;
            object-fit: contain;
            border-radius: 4px;
            margin-top: 10px;
        `;
        img.alt = notification.image_filename || '通知图片';

        content.appendChild(closeBtn);
        content.appendChild(title);
        if (notification.content && notification.content !== notification.title) {
            content.appendChild(contentText);
        }
        content.appendChild(img);

        modal.appendChild(content);
        document.body.appendChild(modal);

        // 显示动画
        setTimeout(() => {
            modal.style.opacity = '1';
        }, 10);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeImageNotification(modal);
            }
        });

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeImageNotification(modal);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    /**
     * 关闭图片通知
     */
    closeImageNotification(modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
    
    /**
     * 播放通知声音
     */
    playNotificationSound() {
        try {
            // 创建音频上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建简单的提示音
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
            
        } catch (error) {
            // 静默失败，不影响功能
            CONFIG.log('warn', 'Failed to play notification sound:', error);
        }
    }
    
    /**
     * 更新通知显示
     */
    updateNotificationDisplay() {
        this.updateNotificationBadge();
        this.renderNotificationList();
    }
    
    /**
     * 更新通知徽章
     */
    updateNotificationBadge() {
        const badge = Utils.dom.$('.notification-badge');
        if (badge) {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                Utils.dom.show(badge);
            } else {
                Utils.dom.hide(badge);
            }
        }
    }
    
    /**
     * 渲染通知列表
     */
    renderNotificationList() {
        const container = Utils.dom.$('#notification-list');
        if (!container) return;
        
        if (this.notifications.length === 0) {
            container.innerHTML = `
                <div class="empty-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>暂无通知</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.notifications.map(notification => 
            this.createNotificationHTML(notification)
        ).join('');
        
        // 绑定通知点击事件
        Utils.event.on(container, 'click', (e) => {
            const notificationItem = e.target.closest('.notification-item');
            if (notificationItem) {
                const notificationId = notificationItem.dataset.notificationId;
                this.markAsRead(notificationId);
            }
        });
    }
    
    /**
     * 创建通知HTML
     */
    createNotificationHTML(notification) {
        const timeAgo = Utils.formatDate(notification.timestamp);
        const readClass = notification.read ? 'read' : 'unread';
        const title = notification.title || '系统通知';
        const message = notification.content || notification.message || '';

        // 图片标识
        const imageIndicator = notification.has_image ?
            '<i class="fas fa-image notification-image-icon" title="包含图片"></i>' : '';

        return `
            <div class="notification-item ${notification.type} ${readClass}"
                 data-notification-id="${notification.id}">
                <div class="notification-icon">
                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">
                        ${title}
                        ${imageIndicator}
                    </div>
                    <div class="notification-message">${message}</div>
                    <div class="notification-time">${timeAgo}</div>
                </div>
                ${!notification.read ? '<div class="notification-dot"></div>' : ''}
            </div>
        `;
    }
    
    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const iconMap = {
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            upload: 'fas fa-upload',
            download: 'fas fa-download',
            share: 'fas fa-share',
            system: 'fas fa-cog'
        };
        return iconMap[type] || 'fas fa-bell';
    }
    
    /**
     * 标记为已读
     */
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            this.unreadCount = Math.max(0, this.unreadCount - 1);
            this.updateNotificationDisplay();
        }
    }
    
    /**
     * 标记全部为已读
     */
    markAllAsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        this.unreadCount = 0;
        this.updateNotificationDisplay();
    }
    
    /**
     * 清除通知
     */
    clearNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.updateNotificationDisplay();
    }
    
    /**
     * 清除所有通知
     */
    clearAllNotifications() {
        this.notifications = [];
        this.unreadCount = 0;
        this.updateNotificationDisplay();
    }
    
    /**
     * 切换通知面板
     */
    toggleNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            if (Utils.dom.hasClass(panel, 'show')) {
                this.hideNotificationPanel();
            } else {
                this.showNotificationPanel();
            }
        }
    }
    
    /**
     * 显示通知面板
     */
    showNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            Utils.dom.addClass(panel, 'show');
            
            // 标记所有通知为已读
            setTimeout(() => {
                this.markAllAsRead();
            }, 1000);
        }
    }
    
    /**
     * 隐藏通知面板
     */
    hideNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            Utils.dom.removeClass(panel, 'show');
        }
    }
    
    /**
     * 开始定期检查
     */
    startPeriodicCheck() {
        // 每5秒检查一次新通知（更频繁的轮询）
        setInterval(() => {
            this.loadNotifications();
        }, 5000);

        // 每30秒检查SocketIO连接状态
        setInterval(() => {
            if (!this.isConnected) {
                CONFIG.log('warn', 'SocketIO not connected, trying to reconnect...');
                this.connectSocketIO();
            }
        }, 30000);
    }
    
    /**
     * 发送通知
     */
    async sendNotification(title, message, type = 'info') {
        try {
            // 如果有WebSocket连接，通过WebSocket发送
            if (this.isConnected && this.ws) {
                this.ws.send(JSON.stringify({
                    action: 'send_notification',
                    title,
                    message,
                    type
                }));
            } else {
                // 检查SystemAPI是否可用
                if (typeof SystemAPI !== 'undefined' && typeof SystemAPI.sendNotification === 'function') {
                    // 通过API发送
                    await SystemAPI.sendNotification({ title, message, type });
                } else {
                    // 如果API不可用，直接添加到本地通知
                    this.addNotification({ title, message, type });
                }
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to send notification:', error);
            // 作为后备，直接添加到本地通知
            this.addNotification({ title, message, type });
        }
    }
    
    /**
     * 请求通知权限
     */
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            return permission === 'granted';
        }
        return false;
    }
    
    /**
     * 显示浏览器通知
     */
    showBrowserNotification(title, message, icon = null) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: icon || '/assets/images/logo.png',
                badge: '/assets/images/badge.png'
            });
            
            // 点击通知时聚焦窗口
            notification.onclick = () => {
                window.focus();
                notification.close();
            };
            
            // 自动关闭
            setTimeout(() => {
                notification.close();
            }, 5000);
        }
    }
}

// 创建全局通知管理器实例
let notificationManager;

document.addEventListener('DOMContentLoaded', () => {
    notificationManager = new NotificationManager();
});

// 全局可用
window.NotificationManager = NotificationManager;
window.notificationManager = null;
