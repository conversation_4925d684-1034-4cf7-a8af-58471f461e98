#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移：添加图像搜索功能相关表
"""

import sqlite3
import os
from datetime import datetime

def migrate_database(db_path: str):
    """执行数据库迁移"""
    print(f"开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 创建图像特征索引表
        print("创建 image_features 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS image_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                feature_hash VARCHAR(64) NOT NULL,
                color_histogram BLOB,
                texture_features BLOB,
                edge_features BLOB,
                keypoint_count INTEGER DEFAULT 0,
                descriptors_path TEXT,
                image_hash VARCHAR(64),
                extraction_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                feature_version VARCHAR(20) DEFAULT '1.0',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE
            )
        ''')
        
        # 2. 创建搜索历史表
        print("创建 image_search_history 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS image_search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_image_hash VARCHAR(64),
                query_image_path TEXT,
                search_options TEXT,
                result_count INTEGER DEFAULT 0,
                search_time FLOAT DEFAULT 0.0,
                user_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 3. 创建搜索结果缓存表
        print("创建 image_search_cache 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS image_search_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                query_hash VARCHAR(64) NOT NULL,
                options_hash VARCHAR(64) NOT NULL,
                results TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                UNIQUE(query_hash, options_hash)
            )
        ''')
        
        # 4. 创建索引
        print("创建索引...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_features_file_id 
            ON image_features(file_id)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_features_hash 
            ON image_features(feature_hash)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_features_image_hash 
            ON image_features(image_hash)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_search_history_created_at 
            ON image_search_history(created_at)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_search_cache_query_hash 
            ON image_search_cache(query_hash)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_image_search_cache_expires_at 
            ON image_search_cache(expires_at)
        ''')
        
        # 5. 创建触发器来维护 updated_at 字段
        print("创建触发器...")
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS update_image_features_updated_at
            AFTER UPDATE ON image_features
            FOR EACH ROW
            BEGIN
                UPDATE image_features 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
        ''')
        
        # 6. 检查并添加 shared_files 表的图像搜索相关字段
        cursor.execute("PRAGMA table_info(shared_files)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'has_image_features' not in columns:
            print("添加 has_image_features 字段到 shared_files 表...")
            cursor.execute('''
                ALTER TABLE shared_files 
                ADD COLUMN has_image_features BOOLEAN DEFAULT FALSE
            ''')
        else:
            print("has_image_features 字段已存在，跳过添加")
        
        if 'image_features_extracted_at' not in columns:
            print("添加 image_features_extracted_at 字段到 shared_files 表...")
            cursor.execute('''
                ALTER TABLE shared_files 
                ADD COLUMN image_features_extracted_at DATETIME
            ''')
        else:
            print("image_features_extracted_at 字段已存在，跳过添加")
        
        conn.commit()
        print("数据库迁移完成！")
        
        # 7. 验证迁移结果
        print("\n验证迁移结果:")
        
        # 检查表是否创建成功
        tables_to_check = ['image_features', 'image_search_history', 'image_search_cache']
        for table_name in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if cursor.fetchone():
                print(f"✅ {table_name} 表创建成功")
            else:
                print(f"❌ {table_name} 表创建失败")
        
        # 检查新字段是否添加成功
        cursor.execute("PRAGMA table_info(shared_files)")
        columns = [column[1] for column in cursor.fetchall()]
        
        new_fields = ['has_image_features', 'image_features_extracted_at']
        for field in new_fields:
            if field in columns:
                print(f"✅ shared_files.{field} 字段添加成功")
            else:
                print(f"❌ shared_files.{field} 字段添加失败")
        
        # 显示新表结构
        print("\n新创建的表结构:")
        for table_name in tables_to_check:
            cursor.execute(f"PRAGMA table_info({table_name})")
            print(f"\n{table_name} 表字段:")
            for column in cursor.fetchall():
                print(f"  - {column[1]} ({column[2]})")
        
        # 检查索引
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_image_%'")
        indexes = cursor.fetchall()
        print(f"\n创建的索引数量: {len(indexes)}")
        for index in indexes:
            print(f"  - {index[0]}")
            
    except Exception as e:
        print(f"迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def main():
    """主函数"""
    # 数据库路径 - 使用绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    db_path = os.path.join(project_root, "data", "file_share_system.db")

    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"数据库路径: {db_path}")

    if not os.path.exists(os.path.dirname(db_path)):
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        print(f"创建数据目录: {os.path.dirname(db_path)}")

    migrate_database(db_path)

if __name__ == "__main__":
    main()