#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索记录模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from config.database import Base
from datetime import datetime
from typing import Dict, Any, Optional

class SearchRecord(Base):
    """搜索记录模型"""
    
    __tablename__ = 'search_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID，NULL表示匿名用户')
    session_id = Column(String(100), nullable=True, comment='会话ID')
    
    # 搜索信息
    search_query = Column(Text, nullable=False, comment='搜索关键词')
    search_type = Column(String(20), default='general', comment='搜索类型: general/file/folder')
    folder_id = Column(Integer, nullable=True, comment='搜索范围文件夹ID')
    
    # 结果信息
    results_count = Column(Integer, default=0, comment='搜索结果数量')
    is_sensitive = Column(Boolean, default=False, comment='是否包含敏感内容')
    is_blocked = Column(Boolean, default=False, comment='是否被屏蔽')
    block_reason = Column(String(255), nullable=True, comment='屏蔽原因')
    
    # 网络信息
    ip_address = Column(String(45), nullable=True, comment='IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='搜索时间')
    
    # 关联关系
    user = relationship("User", backref="search_records")
    
    def __init__(self, search_query: str, **kwargs):
        self.search_query = search_query
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'search_query': self.search_query,
            'search_type': self.search_type,
            'folder_id': self.folder_id,
            'results_count': self.results_count,
            'is_sensitive': self.is_sensitive,
            'is_blocked': self.is_blocked,
            'block_reason': self.block_reason,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class SensitiveFile(Base):
    """敏感文件配置模型"""
    
    __tablename__ = 'sensitive_files'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('shared_files.id'), nullable=True, comment='文件ID')
    folder_id = Column(Integer, ForeignKey('shared_folders.id'), nullable=True, comment='文件夹ID')
    
    # 敏感信息
    sensitivity_level = Column(String(20), default='medium', comment='敏感级别: low/medium/high/critical')
    keywords = Column(JSON, nullable=True, comment='敏感关键词列表')
    description = Column(Text, nullable=True, comment='敏感描述')
    
    # 处理方式
    action_type = Column(String(20), default='warn', comment='处理方式: warn/block/log')
    alert_admin = Column(Boolean, default=False, comment='是否警告管理员')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'folder_id': self.folder_id,
            'sensitivity_level': self.sensitivity_level,
            'keywords': self.keywords,
            'description': self.description,
            'action_type': self.action_type,
            'alert_admin': self.alert_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class BlockedKeyword(Base):
    """屏蔽关键词模型"""
    
    __tablename__ = 'blocked_keywords'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    keyword = Column(String(255), nullable=False, unique=True, comment='屏蔽关键词')
    keyword_type = Column(String(20), default='exact', comment='匹配类型: exact/partial/regex')
    
    # 配置信息
    is_active = Column(Boolean, default=True, comment='是否启用')
    is_regex = Column(Boolean, default=False, comment='是否为正则表达式')
    severity = Column(String(20), default='medium', comment='严重程度: low/medium/high/critical')
    block_search = Column(Boolean, default=True, comment='是否屏蔽搜索')
    block_download = Column(Boolean, default=False, comment='是否屏蔽下载')
    description = Column(Text, nullable=True, comment='描述信息')
    
    # 统计信息
    hit_count = Column(Integer, default=0, comment='命中次数')
    last_hit = Column(DateTime, nullable=True, comment='最后命中时间')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    def __init__(self, keyword: str, **kwargs):
        self.keyword = keyword
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def increment_hit(self):
        """增加命中次数"""
        if self.hit_count is None:
            self.hit_count = 0
        self.hit_count += 1
        self.last_hit = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'keyword': self.keyword,
            'keyword_type': self.keyword_type,
            'is_active': self.is_active,
            'is_regex': self.is_regex,
            'severity': self.severity,
            'block_search': self.block_search,
            'block_download': self.block_download,
            'description': self.description,
            'hit_count': self.hit_count,
            'last_hit': self.last_hit.isoformat() if self.last_hit else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
