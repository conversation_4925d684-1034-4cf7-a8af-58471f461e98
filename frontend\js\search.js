/**
 * 搜索功能模块
 * 处理文件搜索、搜索建议、搜索历史等功能
 */

class SearchManager {
    constructor() {
        this.searchInput = null;
        this.searchFilters = [];
        this.currentQuery = '';
        this.currentType = 'image'; // 专注于图片搜索
        this.searchResults = [];
        this.isSearching = false;
        this.isInSearchMode = false; // 标识是否处于搜索模式

        this.init();
    }
    
    /**
     * 初始化搜索管理器
     */
    init() {
        this.bindEvents();
        this.setupSearchFilters();
        this.initImageSearch();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        this.searchInput = Utils.dom.$('#search-input');
        if (!this.searchInput) return;
        
        // 搜索输入防抖
        const debouncedSearch = Utils.debounce(
            (query) => this.performSearch(query),
            CONFIG.UI.SEARCH.DEBOUNCE_DELAY
        );
        
        Utils.event.on(this.searchInput, 'input', (e) => {
            const query = e.target.value.trim();
            this.currentQuery = query;

            if (query.length >= CONFIG.UI.SEARCH.MIN_QUERY_LENGTH) {
                debouncedSearch(query);
            } else if (query.length === 0) {
                // 只有完全清空时才恢复原始列表
                this.clearSearchResults();
            }
            // 对于长度不足但不为空的查询，不做任何操作，保持当前状态
        });
        
        // 回车搜索
        Utils.event.on(this.searchInput, 'keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(this.currentQuery);
            }
            
            // ESC 清除搜索
            if (e.key === 'Escape') {
                this.clearSearch();
            }
        });
        
        // 搜索焦点 - 简化处理，不显示历史记录
        Utils.event.on(this.searchInput, 'focus', () => {
            // 专注搜索，不显示历史记录
        });

        Utils.event.on(this.searchInput, 'blur', () => {
            // 简化失焦处理
        });
    }
    
    /**
     * 设置搜索过滤器
     */
    setupSearchFilters() {
        this.searchFilters = Utils.dom.$$('.filter-btn');
        
        this.searchFilters.forEach(filter => {
            Utils.event.on(filter, 'click', (e) => {
                const type = e.target.dataset.type;
                this.setSearchType(type);
                
                // 如果有搜索内容，重新搜索
                if (this.currentQuery) {
                    this.performSearch(this.currentQuery);
                }
            });
        });
    }
    
    /**
     * 设置搜索类型
     */
    setSearchType(type) {
        this.currentType = type;
        
        // 更新过滤器按钮状态
        this.searchFilters.forEach(filter => {
            Utils.dom.removeClass(filter, 'active');
            if (filter.dataset.type === type) {
                Utils.dom.addClass(filter, 'active');
            }
        });
    }
    
    /**
     * 执行搜索
     */
    async performSearch(query) {
        if (!query || query.length < CONFIG.UI.SEARCH.MIN_QUERY_LENGTH) {
            return;
        }
        
        try {
            this.isSearching = true;
            this.showSearchLoading();
            
            const results = await SearchAPI.search(query, this.currentType, {
                limit: CONFIG.UI.SEARCH.MAX_RESULTS
            });
            
            this.searchResults = results.files || [];
            this.renderSearchResults();

            // 移除历史记录功能
            
        } catch (error) {
            CONFIG.log('error', 'Search failed:', error);
            Components.Toast.error('搜索失败，请重试');
            this.clearSearchResults();
        } finally {
            this.isSearching = false;
            this.hideSearchLoading();
        }
    }
    
    /**
     * 渲染搜索结果
     */
    renderSearchResults() {
        if (!fileManager) return;

        // 过滤搜索结果，确保只显示图片文件
        const filteredResults = this.filterImageFiles(this.searchResults);

        // 设置搜索模式标识
        this.isInSearchMode = true;

        // 更新文件管理器显示搜索结果
        fileManager.files = filteredResults;
        fileManager.isInSearchMode = true; // 通知文件管理器当前处于搜索模式
        fileManager.searchResults = filteredResults; // 保存搜索结果
        fileManager.renderFiles();

        // 更新面包屑显示搜索状态
        this.updateSearchBreadcrumb();

        // 显示搜索结果统计
        this.showSearchStats(filteredResults.length);
    }

    /**
     * 过滤图片文件
     */
    filterImageFiles(files) {
        const allowedExts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg'];

        return files.filter(file => {
            // 排除文件夹
            if (file.type === 'folder') {
                return false;
            }

            // 检查文件扩展名
            const filename = file.name || file.filename || '';
            if (!filename) return false;

            const ext = filename.toLowerCase().split('.').pop();
            return allowedExts.includes(ext);
        });
    }
    
    /**
     * 更新搜索面包屑
     */
    updateSearchBreadcrumb() {
        const breadcrumbNav = Utils.dom.$('.breadcrumb-nav');
        if (!breadcrumbNav) return;

        breadcrumbNav.innerHTML = `
            <a href="#" class="breadcrumb-item" data-action="clear-search">
                <i class="fas fa-home"></i>
                首页
            </a>
            <span class="breadcrumb-item">
                <i class="fas fa-search"></i>
                搜索: "${this.currentQuery}"
            </span>
        `;

        // 绑定清除搜索事件
        const clearSearchLink = breadcrumbNav.querySelector('[data-action="clear-search"]');
        if (clearSearchLink) {
            clearSearchLink.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.clearSearch();
            });
        }
    }
    
    /**
     * 显示搜索统计
     */
    showSearchStats(count = null) {
        const resultCount = count !== null ? count : this.searchResults.length;
        const typeText = this.getTypeText(this.currentType);

        if (resultCount === 0) {
            Components.Toast.warning(
                `未找到包含"${this.currentQuery}"的${typeText}文件`,
                3000
            );
        } else {
            Components.Toast.success(
                `找到 ${resultCount} 个包含"${this.currentQuery}"的${typeText}文件`,
                2000
            );
        }
    }
    
    /**
     * 获取类型文本
     */
    getTypeText(type) {
        const typeMap = {
            all: '',
            image: '图片',
            document: '文档',
            video: '视频',
            audio: '音频'
        };
        return typeMap[type] || '';
    }
    
    /**
     * 显示搜索加载状态
     */
    showSearchLoading() {
        const searchContainer = this.searchInput.parentElement;
        if (searchContainer) {
            Utils.dom.addClass(searchContainer, 'searching');
        }
    }
    
    /**
     * 隐藏搜索加载状态
     */
    hideSearchLoading() {
        const searchContainer = this.searchInput.parentElement;
        if (searchContainer) {
            Utils.dom.removeClass(searchContainer, 'searching');
        }
    }
    
    /**
     * 清除搜索结果
     */
    clearSearchResults() {
        this.searchResults = [];
        this.isInSearchMode = false;

        if (fileManager) {
            // 清除文件管理器的搜索状态
            fileManager.isInSearchMode = false;
            fileManager.searchResults = [];
            // 恢复原始文件列表
            fileManager.loadFiles(fileManager.currentFolder?.id);
        }
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        this.currentQuery = '';
        this.searchInput.value = '';
        this.clearSearchResults();
    }
    
    // 历史记录功能已移除，专注于简洁的搜索体验
    
    /**
     * 高级搜索
     */
    showAdvancedSearch() {
        // TODO: 实现高级搜索界面
        Components.Toast.info('高级搜索功能开发中...');
    }
    
    /**
     * 搜索建议
     */
    async getSearchSuggestions(query) {
        try {
            // TODO: 实现搜索建议API
            return [];
        } catch (error) {
            CONFIG.log('error', 'Failed to get search suggestions:', error);
            return [];
        }
    }
    
    /**
     * 保存搜索
     */
    saveSearch(name, query, type) {
        const savedSearches = Utils.storage.get('saved_searches', []);
        
        const searchItem = {
            id: Utils.generateId('search'),
            name,
            query,
            type,
            createdAt: new Date().toISOString()
        };
        
        savedSearches.push(searchItem);
        Utils.storage.set('saved_searches', savedSearches);
        
        Components.Toast.success('搜索已保存');
    }
    
    /**
     * 获取保存的搜索
     */
    getSavedSearches() {
        return Utils.storage.get('saved_searches', []);
    }
    
    /**
     * 删除保存的搜索
     */
    deleteSavedSearch(searchId) {
        const savedSearches = this.getSavedSearches();
        const filteredSearches = savedSearches.filter(search => search.id !== searchId);
        Utils.storage.set('saved_searches', filteredSearches);
        
        Components.Toast.success('搜索已删除');
    }
    
    /**
     * 执行保存的搜索
     */
    executeSavedSearch(searchId) {
        const savedSearches = this.getSavedSearches();
        const search = savedSearches.find(s => s.id === searchId);

        if (search) {
            this.searchInput.value = search.query;
            this.currentQuery = search.query;
            this.setSearchType(search.type);
            this.performSearch(search.query);
        }
    }

    /**
     * 初始化以图搜图功能
     */
    initImageSearch() {
        this.imageSearchBtn = Utils.dom.$('#image-search-btn');
        this.imageUploadArea = Utils.dom.$('#image-upload-area');
        this.imageUploadInput = Utils.dom.$('#image-upload-input');
        this.uploadZone = Utils.dom.$('#upload-zone');
        this.cancelUploadBtn = Utils.dom.$('#cancel-upload-btn');

        // 进度显示元素
        this.uploadProgress = Utils.dom.$('#upload-progress');
        this.progressMainText = Utils.dom.$('#progress-main-text');
        this.progressSubText = Utils.dom.$('#progress-sub-text');
        this.progressFill = Utils.dom.$('#progress-fill');
        this.progressPercentage = Utils.dom.$('#progress-percentage');

        if (!this.imageSearchBtn || !this.imageUploadArea) return;

        // 点击图片搜索按钮
        Utils.event.on(this.imageSearchBtn, 'click', (e) => {
            e.stopPropagation();
            this.toggleImageUploadArea();
        });

        // 点击上传区域
        Utils.event.on(this.uploadZone, 'click', () => {
            this.imageUploadInput.click();
        });

        // 文件选择
        Utils.event.on(this.imageUploadInput, 'change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImageUpload(e.target.files[0]);
            }
        });

        // 取消上传
        Utils.event.on(this.cancelUploadBtn, 'click', () => {
            this.cancelImageUpload();
        });

        // 拖拽上传
        this.setupDragAndDrop();

        // 点击其他地方关闭上传区域
        document.addEventListener('click', (e) => {
            if (!this.imageUploadArea.contains(e.target) &&
                !this.imageSearchBtn.contains(e.target)) {
                this.hideImageUploadArea();
            }
        });
    }

    /**
     * 切换图片上传区域显示状态
     */
    toggleImageUploadArea() {
        if (this.imageUploadArea.style.display === 'none') {
            this.showImageUploadArea();
        } else {
            this.hideImageUploadArea();
        }
    }

    /**
     * 显示图片上传区域
     */
    showImageUploadArea() {
        this.imageUploadArea.style.display = 'block';
        // 重置文件输入
        this.imageUploadInput.value = '';
    }

    /**
     * 隐藏图片上传区域
     */
    hideImageUploadArea() {
        this.imageUploadArea.style.display = 'none';
        this.hideUploadProgress();
    }

    /**
     * 显示上传进度
     */
    showUploadProgress() {
        if (this.uploadZone) {
            this.uploadZone.style.display = 'none';
        }
        if (this.uploadProgress) {
            this.uploadProgress.style.display = 'block';
        }
    }

    /**
     * 隐藏上传进度
     */
    hideUploadProgress() {
        if (this.uploadProgress) {
            this.uploadProgress.style.display = 'none';
        }
        if (this.uploadZone) {
            this.uploadZone.style.display = 'flex';
        }
    }

    /**
     * 更新进度显示
     */
    updateProgress(percentage, mainText, subText) {
        if (this.progressFill) {
            this.progressFill.style.width = `${percentage}%`;
        }
        if (this.progressPercentage) {
            this.progressPercentage.textContent = `${percentage}%`;
        }
        if (this.progressMainText && mainText) {
            this.progressMainText.textContent = mainText;
        }
        if (this.progressSubText && subText) {
            this.progressSubText.textContent = subText;
        }
    }

    /**
     * 取消图片上传
     */
    cancelImageUpload() {
        this.isSearching = false;
        this.hideImageUploadArea();
        this.imageUploadInput.value = '';
        Components.Toast.info('已取消图片上传');
    }

    /**
     * 设置拖拽上传
     */
    setupDragAndDrop() {
        // 防止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            Utils.event.on(this.uploadZone, eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // 拖拽进入
        Utils.event.on(this.uploadZone, 'dragenter', () => {
            Utils.dom.addClass(this.uploadZone, 'drag-over');
        });

        // 拖拽离开
        Utils.event.on(this.uploadZone, 'dragleave', () => {
            Utils.dom.removeClass(this.uploadZone, 'drag-over');
        });

        // 拖拽放下
        Utils.event.on(this.uploadZone, 'drop', (e) => {
            Utils.dom.removeClass(this.uploadZone, 'drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageUpload(files[0]);
            }
        });
    }

    /**
     * 处理图片上传
     */
    async handleImageUpload(file) {
        try {
            // 验证文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                Components.Toast.error('不支持的图片格式，请选择 JPG、PNG、GIF、BMP、TIFF、WEBP 格式的图片');
                return;
            }

            // 验证文件大小 (10MB)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                Components.Toast.error('图片文件过大，最大允许 10MB');
                return;
            }

            // 显示进度
            this.showUploadProgress();
            this.updateProgress(0, '正在准备上传...', '正在验证图片文件');

            // 模拟准备阶段
            await new Promise(resolve => setTimeout(resolve, 500));
            this.updateProgress(20, '正在上传图片...', `文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB`);

            // 执行图片搜索
            await this.performImageSearch(file);

        } catch (error) {
            CONFIG.log('error', 'Image upload failed:', error);
            Components.Toast.error('图片上传失败，请重试');
            this.hideImageUploadArea();
        }
    }

    /**
     * 执行图片搜索
     */
    async performImageSearch(imageFile) {
        try {
            this.isSearching = true;

            // 更新进度：上传阶段
            this.updateProgress(40, '正在上传图片...', '正在将图片发送到服务器');
            await new Promise(resolve => setTimeout(resolve, 300));

            // 创建FormData
            const formData = new FormData();
            formData.append('image', imageFile);

            // 更新进度：处理阶段
            this.updateProgress(60, '正在分析图片...', '正在提取图片特征信息');
            await new Promise(resolve => setTimeout(resolve, 500));

            // 更新进度：搜索阶段
            this.updateProgress(80, '正在搜索相似图片...', '正在数据库中查找相似图片');

            // 调用API
            const results = await SearchAPI.searchByImage(formData, {
                limit: CONFIG.UI.SEARCH.MAX_RESULTS,
                threshold: 0.7
            });

            // 更新进度：完成阶段
            this.updateProgress(100, '搜索完成！', `找到 ${results.files?.length || 0} 个相似图片`);
            await new Promise(resolve => setTimeout(resolve, 500));

            this.searchResults = results.files || [];
            this.renderSearchResults();

            // 隐藏进度，显示结果
            this.hideImageUploadArea();

            // 显示搜索信息
            Components.Toast.success(`找到 ${this.searchResults.length} 个相似图片`);

            // 更新搜索输入框显示
            this.searchInput.value = `以图搜图: ${imageFile.name}`;

        } catch (error) {
            CONFIG.log('error', 'Image search failed:', error);
            Components.Toast.error('图片搜索失败，请重试');
            this.clearSearchResults();
            this.hideImageUploadArea();
        } finally {
            this.isSearching = false;
        }
    }
}

// 创建全局搜索管理器实例
let searchManager;

document.addEventListener('DOMContentLoaded', () => {
    searchManager = new SearchManager();
});

// 全局可用
window.SearchManager = SearchManager;
window.searchManager = null;
