#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务器
"""

import sys
import os
# 添加backend目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask, request, jsonify, send_file, abort
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional

from utils.logger import setup_logger

# 可选导入服务模块
try:
    from services.user_service import UserService
    HAS_USER_SERVICE = True
except ImportError:
    HAS_USER_SERVICE = False

try:
    from services.thumbnail_service import ThumbnailService
    HAS_THUMBNAIL_SERVICE = True
except ImportError:
    HAS_THUMBNAIL_SERVICE = False

try:
    from services.encryption_service import EncryptionService
    HAS_ENCRYPTION_SERVICE = True
except ImportError:
    HAS_ENCRYPTION_SERVICE = False

class APIServer:
    """API服务器类"""

    def __init__(self, services: Dict[str, Any], settings):
        self.services = services
        self.settings = settings
        self.logger = setup_logger("APIServer")

        # 初始化额外服务
        self._initialize_additional_services()

        # 初始化网络访问控制服务
        try:
            from services.network_access_service import NetworkAccessService
            self.network_access_service = NetworkAccessService(self.settings)
            # 将网络访问控制服务添加到services字典中
            self.services['network_access'] = self.network_access_service
            self.logger.info("网络访问控制服务初始化成功")
        except ImportError as e:
            self.logger.warning(f"网络访问控制服务不可用: {e}")
            self.network_access_service = None

        # 初始化用户行为监控服务
        try:
            from services.user_behavior_service import UserBehaviorService
            db_manager = self.services.get('database')
            if db_manager:
                self.user_behavior_service = UserBehaviorService(db_manager)
                self.services['user_behavior'] = self.user_behavior_service
                self.logger.info("用户行为监控服务初始化成功")
            else:
                self.logger.warning("数据库管理器不可用，无法初始化用户行为监控服务")
                self.user_behavior_service = None
        except ImportError as e:
            self.logger.warning(f"用户行为监控服务不可用: {e}")
            self.user_behavior_service = None

        # 创建Flask应用
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'your-secret-key-here'

        # 启用CORS - 允许局域网访问
        try:
            # 获取局域网访问配置
            enable_lan_access = self.settings.get('network.enable_internal_access', True)

            if enable_lan_access:
                # 允许所有来源访问（局域网模式）
                CORS(self.app,
                     origins="*",  # 允许所有来源
                     allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
                     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                     supports_credentials=True,
                     expose_headers=["Content-Type", "Authorization"])
                self.logger.info("CORS已配置为允许局域网访问")
            else:
                # 仅允许本地访问
                CORS(self.app,
                     origins=["http://localhost:8084", "http://127.0.0.1:8084", "http://localhost:8082", "http://127.0.0.1:8082", "https://localhost:8084", "https://127.0.0.1:8084"],
                     allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
                     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                     supports_credentials=True,
                     expose_headers=["Content-Type", "Authorization"])
                self.logger.info("CORS已配置为仅允许本地访问")
        except:
            self.logger.warning("CORS模块不可用")

        # 启用SocketIO
        try:
            self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        except:
            self.logger.warning("SocketIO模块不可用")
            self.socketio = None

        # 注册路由
        self.register_routes()
        if self.socketio:
            self.register_socketio_events()

        self.running = False

    def _initialize_additional_services(self):
        """初始化额外服务"""
        try:
            # 初始化用户服务
            if HAS_USER_SERVICE and 'user' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['user'] = UserService(db_manager)
                self.logger.info("用户服务初始化成功")

            # 初始化缩略图服务
            if HAS_THUMBNAIL_SERVICE and 'thumbnail' not in self.services:
                self.services['thumbnail'] = ThumbnailService()
                self.logger.info("缩略图服务初始化成功")

            # 初始化加密服务
            if HAS_ENCRYPTION_SERVICE and 'encryption' not in self.services:
                db_manager = getattr(self, 'db_manager', None)
                if hasattr(self.services.get('file', {}), 'db_manager'):
                    db_manager = self.services['file'].db_manager

                self.services['encryption'] = EncryptionService(db_manager=db_manager)
                self.logger.info("加密服务初始化成功")

        except Exception as e:
            self.logger.error(f"初始化额外服务失败: {e}")

    def check_network_access(self, folder_id=None):
        """网络访问检查装饰器 - 已禁用所有网络权限检查"""
        def decorator(f):
            def wrapper(*args, **kwargs):
                # 直接允许所有访问，不进行任何网络权限检查
                return f(*args, **kwargs)

            wrapper.__name__ = f.__name__
            return wrapper
        return decorator

    def register_routes(self):
        """注册API路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'ok',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/server/status', methods=['GET'])
        def server_status():
            """获取服务器状态"""
            return jsonify({
                'running': self.running,
                'uptime': time.time() - getattr(self, 'start_time', time.time()),
                'services': list(self.services.keys())
            })
        
        @self.app.route('/api/auth/login', methods=['POST'])
        def login():
            """用户登录"""
            try:
                data = request.get_json()
                username = data.get('username')
                password = data.get('password')

                if not username or not password:
                    return jsonify({'error': '用户名和密码不能为空'}), 400

                # 获取客户端信息
                ip_address = request.remote_addr
                user_agent = request.headers.get('User-Agent')

                # 使用用户服务进行验证
                user_service = self.services.get('user')
                if user_service:
                    result = user_service.authenticate_user(
                        username, password, ip_address, user_agent
                    )

                    if result['success']:
                        # 记录用户登录活动到监控服务
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                result['user']['id'],
                                'login',
                                {'username': username, 'success': True},
                                ip_address
                            )

                        return jsonify({
                            'success': True,
                            'token': result['session_token'],
                            'user': result['user']
                        })
                    else:
                        # 记录登录失败
                        monitoring_service = self.services.get('monitoring')
                        if monitoring_service:
                            monitoring_service.record_user_activity(
                                0,  # 未知用户ID
                                'login_failed',
                                {'username': username, 'error': result['error']},
                                ip_address
                            )
                        return jsonify({'error': result['error']}), 401
                else:
                    # 临时返回成功响应（用户服务不可用时）
                    return jsonify({
                        'success': True,
                        'token': 'temp-token',
                        'user': {
                            'id': 1,
                            'username': username,
                            'permissions': ['read', 'download']
                        }
                    })

            except Exception as e:
                self.logger.error(f"登录失败: {e}")
                return jsonify({'error': '登录失败'}), 500

        @self.app.route('/api/auth/logout', methods=['POST'])
        def logout():
            """用户登出"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                ip_address = request.remote_addr

                # 获取用户信息用于记录
                user_info = self._validate_token(token)

                user_service = self.services.get('user')
                if user_service and token:
                    user_service.logout_user(token)

                # 记录登出活动
                if user_info:
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_info.get('user_id'),
                            'logout',
                            {'username': user_info.get('username')},
                            ip_address
                        )

                return jsonify({'success': True})

            except Exception as e:
                self.logger.error(f"登出失败: {e}")
                return jsonify({'error': '登出失败'}), 500

        @self.app.route('/api/auth/verify', methods=['GET'])
        def verify_token():
            """验证token有效性"""
            try:
                token = request.headers.get('Authorization', '').replace('Bearer ', '')

                if not token:
                    return jsonify({'valid': False, 'error': '缺少token'}), 401

                user_info = self._validate_token(token)
                if user_info:
                    return jsonify({
                        'valid': True,
                        'user': {
                            'id': user_info.get('user_id'),
                            'username': user_info.get('username'),
                            'permissions': user_info.get('permissions', []),
                            'is_admin': user_info.get('is_admin', False)
                        }
                    })
                else:
                    return jsonify({'valid': False, 'error': 'token无效或已过期'}), 401

            except Exception as e:
                self.logger.error(f"token验证失败: {e}")
                return jsonify({'valid': False, 'error': 'token验证失败'}), 500
        
        @self.app.route('/api/files/folders', methods=['GET'])
        def get_shared_folders():
            """获取共享文件夹列表"""
            try:
                self.logger.info("收到获取共享文件夹列表请求")

                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    self.logger.error("文件服务不可用")
                    return jsonify({'error': '文件服务不可用'}), 503

                result = file_service.get_shared_folders()
                self.logger.info(f"文件服务返回结果: {result}")

                # 检查结果格式并提取文件夹列表
                if result.get('success', True) and 'folders' in result:
                    folders = result['folders']
                    self.logger.info(f"返回 {len(folders)} 个有权限的文件夹")
                    return jsonify(folders)
                else:
                    self.logger.error(f"获取文件夹列表失败: {result.get('error', '未知错误')}")
                    return jsonify([])  # 返回空数组而不是错误

            except Exception as e:
                self.logger.error(f"获取文件夹列表失败: {e}")
                import traceback
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                return jsonify([])  # 返回空数组而不是错误
        
        @self.app.route('/api/files', methods=['GET'])
        def get_files():
            """获取文件列表"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                folder_id = request.args.get('folder_id', type=int)
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')

                # 获取文件列表
                if folder_id:
                    # 检查文件夹读取权限
                    if not file_service.check_folder_permission(folder_id, 'read'):
                        return jsonify({'error': '没有访问权限'}), 403

                    # 检查文件夹级别的网络访问权限
                    folder_permissions_result = file_service.get_folder_permissions(folder_id)
                    if folder_permissions_result.get('success', False):
                        folder_permissions = folder_permissions_result.get('permissions', {})

                        # 检查网络访问权限
                        network_service = self.services.get('network_access')
                        if network_service:
                            ip_address = request.remote_addr
                            access_check = network_service.check_folder_access_permission(ip_address, folder_permissions)

                            if not access_check.get('allowed', False):
                                # 返回403错误，前端会静默处理
                                return jsonify({
                                    'error': access_check.get('reason', '访问被拒绝'),
                                    'access_type': access_check.get('access_type', 'unknown')
                                }), 403

                    result = file_service.get_folder_files(
                        folder_id, page, page_size, search_query
                    )
                else:
                    # 获取根目录文件
                    result = file_service.get_root_files(page, page_size, search_query)

                # 检查结果格式
                if not result.get('success', True):
                    return jsonify({'error': result.get('error', '获取文件失败')}), 500

                # 过滤只显示图片文件
                if 'files' in result:
                    filtered_files = []
                    for file_item in result['files']:
                        # 文件夹总是显示
                        if file_item.get('type') == 'folder':
                            filtered_files.append(file_item)
                        else:
                            # 只显示图片格式文件
                            filename = file_item.get('filename', file_item.get('name', ''))
                            if filename:
                                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                                allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                                if ext in allowed_exts:
                                    # 标准化文件数据格式（移除时间信息）
                                    file_data = {
                                        'id': file_item.get('id'),
                                        'name': filename,
                                        'type': 'file',
                                        'size': file_item.get('file_size', 0),
                                        'folder_name': file_item.get('folder_name', ''),
                                        'relative_path': file_item.get('relative_path', '')
                                    }
                                    filtered_files.append(file_data)

                    result['files'] = filtered_files
                    result['total'] = len(filtered_files)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500

        @self.app.route('/api/files/folders/<int:folder_id>/files', methods=['GET'])
        @self.check_network_access()
        def get_folder_files(folder_id):
            """获取文件夹中的文件"""
            try:
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)
                search_query = request.args.get('search', '')

                result = file_service.get_folder_files(
                    folder_id, page, page_size, search_query
                )

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取文件列表失败: {e}")
                return jsonify({'error': '获取文件列表失败'}), 500

        @self.app.route('/api/files/directories', methods=['GET'])
        def get_directory_contents():
            """获取目录内容（支持层级结构）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                folder_id = request.args.get('folder_id', type=int)
                directory_id = request.args.get('directory_id', type=int)
                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 50, type=int)

                if not folder_id:
                    return jsonify({'error': '缺少folder_id参数'}), 400

                # 检查文件夹读取权限
                if not file_service.check_folder_permission(folder_id, 'read'):
                    return jsonify({'error': '没有访问权限'}), 403

                # 检查网络访问权限
                folder_permissions_result = file_service.get_folder_permissions(folder_id)
                if folder_permissions_result.get('success', False):
                    folder_permissions = folder_permissions_result.get('permissions', {})
                    network_service = self.services.get('network_access')
                    if network_service:
                        ip_address = request.remote_addr
                        access_check = network_service.check_folder_access_permission(ip_address, folder_permissions)
                        if not access_check.get('allowed', False):
                            return jsonify({
                                'error': access_check.get('reason', '访问被拒绝'),
                                'access_type': access_check.get('access_type', 'unknown')
                            }), 403

                # 获取目录内容
                result = file_service.get_directory_contents(folder_id, directory_id, page, page_size)

                # 获取面包屑导航
                breadcrumb = file_service.get_directory_breadcrumb(folder_id, directory_id)
                result['breadcrumb'] = breadcrumb

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取目录内容失败: {e}")
                return jsonify({'error': '获取目录内容失败'}), 500

        @self.app.route('/api/files/folders/<int:folder_id>/scan-directories', methods=['POST'])
        def scan_folder_with_directories(folder_id):
            """扫描文件夹并创建目录结构"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 检查文件夹权限
                if not file_service.check_folder_permission(folder_id, 'read'):
                    return jsonify({'error': '没有访问权限'}), 403

                # 执行扫描
                result = file_service.scan_shared_folder_with_directories(folder_id)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"扫描文件夹失败: {e}")
                return jsonify({'error': '扫描文件夹失败'}), 500

        @self.app.route('/api/files/folders/<int:folder_id>/scan', methods=['POST'])
        def scan_folder(folder_id):
            """扫描文件夹并预生成缩略图"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 检查文件夹权限
                if not file_service.check_folder_permission(folder_id, 'read'):
                    return jsonify({'error': '没有访问权限'}), 403

                # 获取请求参数
                data = request.get_json() or {}
                generate_thumbnails = data.get('generate_thumbnails', True)

                # 执行扫描
                result = file_service.scan_shared_folder(folder_id, generate_thumbnails)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"扫描文件夹失败: {e}")
                return jsonify({'error': '扫描文件夹失败'}), 500

        @self.app.route('/api/files/folders/<int:folder_id>/thumbnails', methods=['POST'])
        def generate_folder_thumbnails(folder_id):
            """为文件夹生成缩略图"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 检查文件夹权限
                if not file_service.check_folder_permission(folder_id, 'read'):
                    return jsonify({'error': '没有访问权限'}), 403

                # 获取请求参数
                data = request.get_json() or {}
                force_regenerate = data.get('force_regenerate', False)

                # 生成缩略图
                result = file_service.generate_thumbnails_for_folder(folder_id, force_regenerate)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"生成缩略图失败: {e}")
                return jsonify({'error': '生成缩略图失败'}), 500

        @self.app.route('/api/files/thumbnails/generate-all', methods=['POST'])
        def generate_all_thumbnails():
            """为所有文件夹生成缩略图"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 检查管理员权限
                if not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 获取请求参数
                data = request.get_json() or {}
                force_regenerate = data.get('force_regenerate', False)

                # 生成所有缩略图
                result = file_service.generate_thumbnails_for_all_folders(force_regenerate)

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"批量生成缩略图失败: {e}")
                return jsonify({'error': '批量生成缩略图失败'}), 500

        @self.app.route('/api/download/async/folder/<int:folder_id>', methods=['POST'])
        def create_async_folder_download(folder_id):
            """创建异步文件夹下载任务"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 获取异步下载服务
                async_download_service = self.services.get('async_download')
                if not async_download_service:
                    return jsonify({'error': '异步下载服务不可用'}), 503

                # 获取文件夹信息
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 检查文件夹权限
                if not file_service.check_folder_permission(folder_id, 'download'):
                    return jsonify({'error': '没有下载权限'}), 403

                # 获取文件夹名称
                folder_info = file_service.get_folder_info(folder_id)
                if not folder_info:
                    return jsonify({'error': '文件夹不存在'}), 404

                folder_name = folder_info.get('name', f'folder_{folder_id}')

                # 创建异步下载任务
                result = async_download_service.create_async_download_task(
                    download_type='folder',
                    target_id=folder_id,
                    target_name=folder_name,
                    user_id=user_info.get('user_id'),
                    request_context={
                        'ip_address': request.remote_addr,
                        'user_agent': request.headers.get('User-Agent'),
                        'download_source': 'web'
                    }
                )

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"创建异步文件夹下载失败: {e}")
                return jsonify({'error': '创建下载任务失败'}), 500

        @self.app.route('/api/download/async/<task_id>/status', methods=['GET'])
        def get_async_download_status(task_id):
            """获取异步下载任务状态"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 获取异步下载服务
                async_download_service = self.services.get('async_download')
                if not async_download_service:
                    return jsonify({'error': '异步下载服务不可用'}), 503

                # 获取任务状态
                result = async_download_service.get_task_status(task_id)

                # 检查任务所有权
                if result.get('success') and result.get('task'):
                    task = result['task']
                    if task.get('user_id') != user_info.get('user_id'):
                        return jsonify({'error': '无权访问此任务'}), 403

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取异步下载状态失败: {e}")
                return jsonify({'error': '获取任务状态失败'}), 500

        @self.app.route('/api/download/async/<task_id>', methods=['GET'])
        def download_async_file(task_id):
            """下载异步生成的文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 获取异步下载服务
                async_download_service = self.services.get('async_download')
                if not async_download_service:
                    return jsonify({'error': '异步下载服务不可用'}), 503

                # 获取文件信息
                result = async_download_service.download_async_file(task_id)

                if not result.get('success'):
                    return jsonify({'error': result.get('error', '下载失败')}), 400

                file_path = result['file_path']
                filename = result['filename']

                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/zip'
                )

            except Exception as e:
                self.logger.error(f"异步文件下载失败: {e}")
                return jsonify({'error': '文件下载失败'}), 500

        @self.app.route('/api/download/async/tasks', methods=['GET'])
        def get_user_async_tasks():
            """获取用户的异步下载任务列表"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 获取异步下载服务
                async_download_service = self.services.get('async_download')
                if not async_download_service:
                    return jsonify({'error': '异步下载服务不可用'}), 503

                # 获取查询参数
                limit = request.args.get('limit', 50, type=int)

                # 获取用户任务列表
                result = async_download_service.get_user_tasks(
                    user_id=user_info.get('user_id'),
                    limit=limit
                )

                return jsonify(result)

            except Exception as e:
                self.logger.error(f"获取用户异步任务失败: {e}")
                return jsonify({'error': '获取任务列表失败'}), 500

        @self.app.route('/api/files/<int:file_id>/download', methods=['GET'])
        @self.check_network_access()
        def download_file(file_id):
            """下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 使用下载服务处理下载
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('user_id') or user_info.get('id')
                ip_address = request.remote_addr

                self.logger.info(f"单文件下载: user_info={user_info}, user_id={user_id}")

                # 准备下载并记录
                request_context = {
                    'session_id': request.headers.get('X-Session-ID'),
                    'ip_address': ip_address,
                    'user_agent': request.headers.get('User-Agent'),
                    'download_source': 'web'
                }

                result = download_service.prepare_single_file_download(
                    file_id, user_id, request_context=request_context
                )

                if result.get('success'):
                    # 记录下载到数据库
                    download_service.record_download(
                        file_id=file_id,
                        user_id=user_id,
                        download_type='single',
                        zip_path=result.get('zip_path'),
                        is_encrypted=result.get('is_encrypted'),
                        password=result.get('password'),
                        batch_id=result.get('batch_id'),
                        session_id=request_context['session_id'],
                        ip_address=request_context['ip_address'],
                        user_agent=request_context['user_agent'],
                        download_source=request_context['download_source'],
                        request_context=request_context
                    )

                    if result.get('is_encrypted'):
                        # 文件已加密，返回423状态码要求申请密码
                        return jsonify({
                            'error': '文件已加密，需要申请解压密码',
                            'encrypted': True,
                            'filename': result.get('filename'),
                            'file_id': file_id,
                            'message': '该文件下载次数已达到限制，已自动加密。请申请解压密码。'
                        }), 423
                    else:
                        # 文件未加密，直接返回下载
                        zip_path = result.get('zip_path')
                        if zip_path and os.path.exists(zip_path):
                            # 记录下载活动到监控系统
                            monitoring_service = self.services.get('monitoring')
                            if monitoring_service:
                                monitoring_service.record_user_activity(
                                    user_id,
                                    'download',
                                    {
                                        'file_id': file_id,
                                        'filename': result.get('filename'),
                                        'file_size': result.get('file_size'),
                                        'is_encrypted': False
                                    },
                                    ip_address
                                )

                            # 更新在线用户下载统计
                            behavior_service = self.services.get('user_behavior')
                            if behavior_service and token:
                                behavior_service.update_online_user_download(token)
                                behavior_service.update_online_user_activity(token, 'download')

                            return send_file(
                                zip_path,
                                as_attachment=True,
                                download_name=result.get('filename', f'file_{file_id}.zip'),
                                mimetype='application/zip'
                            )
                        else:
                            return jsonify({'error': '下载文件生成失败'}), 500
                else:
                    return jsonify({'error': result.get('error', '下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"下载文件失败: {e}")
                return jsonify({'error': '下载失败'}), 500

        @self.app.route('/api/files/batch/download', methods=['POST'])
        def batch_download():
            """批量下载文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                # 获取文件路径
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                file_paths = []
                for file_id in file_ids:
                    file_info = file_service.get_file_info(file_id)
                    if file_info and file_info.get('exists', False):
                        file_paths.append(file_info['full_path'])

                if not file_paths:
                    return jsonify({'error': '没有有效的文件'}), 400

                # 创建批量下载包
                encryption_service = self.services.get('encryption')
                if encryption_service:
                    result = encryption_service.create_batch_package(
                        file_paths, user_info['user_id']
                    )

                    if result['success']:
                        if result.get('encrypted', False):
                            return jsonify({
                                'success': True,
                                'encrypted': True,
                                'package_id': os.path.basename(result['package_path']),
                                'file_count': result['file_count'],
                                'message': '文件包已加密，请申请解压密码'
                            })
                        else:
                            return jsonify({
                                'success': True,
                                'encrypted': False,
                                'download_url': f"/api/files/package/{os.path.basename(result['package_path'])}",
                                'file_count': result['file_count']
                            })
                    else:
                        return jsonify({'error': result['error']}), 500
                else:
                    return jsonify({'error': '加密服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '批量下载失败'}), 500
        
        @self.app.route('/api/search', methods=['POST'])
        def search_files():
            """搜索文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 检查用户搜索封禁状态
                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    ban_check = behavior_service.check_user_ban(user_info.get('user_id'), 'search')
                    if ban_check.get('is_banned'):
                        ban_info = ban_check.get('ban_info', {})
                        reason = ban_info.get('reason', '违规操作')
                        return jsonify({
                            'error': '搜索功能已被禁用',
                            'banned': True,
                            'reason': reason,
                            'ban_type': 'search'
                        }), 403

                data = request.get_json()
                query = data.get('query', '')
                search_type = data.get('type', 'image')  # 默认为图片搜索
                limit = data.get('limit', 100)
                ip_address = request.remote_addr

                # 记录搜索活动
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_service.record_user_activity(
                        user_info.get('user_id'),
                        'search',
                        {'query': query, 'search_type': search_type},
                        ip_address
                    )

                # 检查屏蔽关键词
                behavior_service = self.services.get('user_behavior')
                if behavior_service and query.strip():
                    is_blocked, block_reason = behavior_service.check_blocked_keywords(query)
                    if is_blocked:
                        # 记录被屏蔽的搜索
                        try:
                            user_agent = request.headers.get('User-Agent', '')
                            behavior_service.record_search(
                                user_id=user_info.get('user_id'),
                                search_query=query,
                                search_type=search_type,
                                results_count=0,
                                ip_address=ip_address,
                                user_agent=user_agent,
                                session_id=token
                            )
                            self.logger.warning(f"搜索被屏蔽: 用户={user_info.get('username')}, 查询='{query}', 原因={block_reason}")
                        except Exception as e:
                            self.logger.error(f"记录屏蔽搜索失败: {e}")

                        return jsonify({
                            'error': '搜索内容包含屏蔽关键词',
                            'blocked': True,
                            'reason': block_reason
                        }), 403

                # 更新在线用户搜索统计
                if behavior_service and token:
                    behavior_service.update_online_user_search(token)
                    behavior_service.update_online_user_activity(token, 'search')

                # 使用文件服务进行搜索
                file_service = self.services.get('file')
                if not file_service:
                    return jsonify({'error': '文件服务不可用'}), 503

                # 执行搜索
                search_results = []
                if query.strip():
                    # 获取所有文件并进行搜索
                    all_files_result = file_service.get_root_files(1, 1000, query)

                    if 'files' in all_files_result:
                        for file_item in all_files_result['files']:
                            # 只搜索图片文件，不包含文件夹
                            if file_item.get('type') != 'folder':
                                filename = file_item.get('filename', file_item.get('name', ''))
                                if filename and query.lower() in filename.lower():
                                    # 检查是否为图片格式
                                    ext = filename.lower().split('.')[-1] if '.' in filename else ''
                                    allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                                    if ext in allowed_exts:
                                        # 标准化文件数据格式
                                        file_data = {
                                            'id': file_item.get('id'),
                                            'name': filename,
                                            'type': 'file',
                                            'size': file_item.get('file_size', 0),
                                            'folder_name': file_item.get('folder_name', ''),
                                            'relative_path': file_item.get('relative_path', ''),
                                            'extension': f'.{ext}'
                                        }
                                        search_results.append(file_data)

                # 记录正常搜索行为到搜索记录表
                if behavior_service and query.strip():
                    try:
                        # 获取用户代理
                        user_agent = request.headers.get('User-Agent', '')

                        # 记录搜索（这里不会被屏蔽，因为前面已经检查过了）
                        search_result = behavior_service.record_search(
                            user_id=user_info.get('user_id'),
                            search_query=query,
                            search_type=search_type,
                            results_count=len(search_results),
                            ip_address=ip_address,
                            user_agent=user_agent,
                            session_id=token  # 使用token作为session_id
                        )

                        if search_result.get('success', True):
                            self.logger.info(f"搜索记录已保存: 用户={user_info.get('username')}, 查询='{query}', 结果数={len(search_results)}")
                        else:
                            self.logger.warning(f"搜索被屏蔽: 用户={user_info.get('username')}, 查询='{query}', 原因={search_result.get('reason')}")
                    except Exception as e:
                        self.logger.error(f"记录搜索行为失败: {e}")

                return jsonify({
                    'files': search_results,
                    'total': len(search_results),
                    'query': query,
                    'search_type': search_type
                })

            except Exception as e:
                self.logger.error(f"搜索失败: {e}")
                return jsonify({'error': '搜索失败'}), 500

        @self.app.route('/api/search/image-upload', methods=['POST'])
        def search_by_image_upload():
            """通过上传图片搜索相似图片"""
            try:
                self.logger.info("收到图片搜索请求")

                # 检查功能是否开启
                if not self.settings.get('search.enable_image_upload_search', True):
                    self.logger.warning("以图搜图功能已关闭")
                    return jsonify({'error': '以图搜图功能已关闭'}), 403

                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 检查用户搜索封禁状态
                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    ban_check = behavior_service.check_user_ban(user_info.get('user_id'), 'search')
                    if ban_check.get('is_banned'):
                        ban_info = ban_check.get('ban_info', {})
                        reason = ban_info.get('reason', '违规操作')
                        return jsonify({
                            'error': '搜索功能已被禁用',
                            'banned': True,
                            'reason': reason,
                            'ban_type': 'search'
                        }), 403

                # 检查是否有上传的图片文件
                if 'image' not in request.files:
                    return jsonify({'error': '未找到图片文件'}), 400

                image_file = request.files['image']
                if image_file.filename == '':
                    return jsonify({'error': '未选择图片文件'}), 400

                # 检查文件大小
                max_size = self.settings.get('search.max_upload_image_size', 10 * 1024 * 1024)  # 默认10MB
                if hasattr(image_file, 'content_length') and image_file.content_length > max_size:
                    return jsonify({'error': f'图片文件过大，最大允许{max_size // (1024*1024)}MB'}), 400

                # 检查文件类型
                allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'}
                file_ext = os.path.splitext(image_file.filename)[1].lower()
                if file_ext not in allowed_extensions:
                    return jsonify({'error': '不支持的图片格式'}), 400

                # 创建临时文件保存上传的图片
                import tempfile
                import uuid

                temp_dir = self.settings.get('system.temp_directory', './temp')
                os.makedirs(temp_dir, exist_ok=True)

                temp_filename = f"search_{uuid.uuid4().hex}{file_ext}"
                temp_filepath = os.path.join(temp_dir, temp_filename)

                try:
                    # 保存上传的图片到临时文件
                    image_file.save(temp_filepath)

                    # 获取搜索参数
                    limit = int(request.form.get('limit', 50))
                    threshold = float(request.form.get('threshold',
                                    self.settings.get('search.image_search_threshold', 0.7)))

                    # 记录搜索活动
                    ip_address = request.remote_addr
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_info.get('user_id'),
                            'image_search',
                            {'search_type': 'image_upload', 'filename': image_file.filename},
                            ip_address
                        )

                    # 使用搜索服务进行图像搜索
                    search_service = self.services.get('search')
                    if search_service:
                        # 使用图像搜索引擎
                        search_results = search_service.search_image(temp_filepath, limit, threshold)

                        # 转换搜索结果格式
                        formatted_results = []
                        for result in search_results:
                            formatted_result = {
                                'id': result.get('id'),
                                'name': result.get('filename', result.get('name', '')),
                                'type': 'file',
                                'size': result.get('size', 0),
                                'folder_name': result.get('folder_name', ''),
                                'relative_path': result.get('path', result.get('relative_path', '')),
                                'similarity': result.get('similarity', 0),
                                'extension': os.path.splitext(result.get('filename', ''))[1]
                            }
                            formatted_results.append(formatted_result)
                    else:
                        # 如果搜索服务不可用，返回空结果
                        formatted_results = []

                    # 记录搜索行为
                    if behavior_service:
                        try:
                            user_agent = request.headers.get('User-Agent', '')
                            behavior_service.record_search(
                                user_id=user_info.get('user_id'),
                                search_query=f"image_upload:{image_file.filename}",
                                search_type='image_upload',
                                results_count=len(formatted_results),
                                ip_address=ip_address,
                                user_agent=user_agent,
                                session_id=token
                            )
                        except Exception as e:
                            self.logger.error(f"记录图像搜索行为失败: {e}")

                    return jsonify({
                        'files': formatted_results,
                        'total': len(formatted_results),
                        'search_type': 'image_upload',
                        'threshold': threshold,
                        'uploaded_filename': image_file.filename
                    })

                finally:
                    # 清理临时文件
                    try:
                        if os.path.exists(temp_filepath):
                            os.remove(temp_filepath)
                    except Exception as e:
                        self.logger.warning(f"清理临时文件失败: {e}")

            except Exception as e:
                self.logger.error(f"图像搜索失败: {e}")
                return jsonify({'error': '图像搜索失败'}), 500

        @self.app.route('/api/search/test', methods=['GET'])
        def test_search_api():
            """测试搜索API是否可达"""
            return jsonify({
                'status': 'ok',
                'message': '搜索API正常',
                'image_upload_enabled': self.settings.get('search.enable_image_upload_search', True)
            })

        @self.app.route('/api/files/<int:file_id>/thumbnail', methods=['GET'])
        def get_file_thumbnail(file_id):
            """获取文件缩略图"""
            try:
                # 验证用户权限 - 支持从header或URL参数获取token
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                if not token:
                    # 如果header中没有token，尝试从URL参数获取
                    token = request.args.get('token', '')

                # 临时跳过认证检查，用于调试缩略图功能
                if token:
                    user_info = self._validate_token(token)
                    if not user_info:
                        return jsonify({'error': '未授权访问'}), 401
                else:
                    # 如果没有token，暂时允许访问（仅用于调试）
                    self.logger.warning(f"缩略图请求没有token，允许访问用于调试: {file_id}")
                    pass

                size = request.args.get('size', 'medium')  # small, medium, large

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                self.logger.info(f"缩略图请求 - 文件ID: {file_id}, 文件信息: {file_info}")
                if not file_info:
                    self.logger.warning(f"缩略图请求失败 - 文件不存在: {file_id}")
                    abort(404)

                # 检查文件夹级别的网络访问权限 - 优先检查权限
                folder_id = file_info.get('folder_id')
                self.logger.info(f"缩略图权限检查 - 文件夹ID: {folder_id}")
                if folder_id:
                    folder_permissions_result = file_service.get_folder_permissions(folder_id)
                    self.logger.info(f"缩略图权限检查 - 文件夹权限结果: {folder_permissions_result}")
                    if folder_permissions_result.get('success', False):
                        folder_permissions = folder_permissions_result.get('permissions', {})
                        self.logger.info(f"缩略图权限检查 - 文件夹权限: {folder_permissions}")

                        # 检查网络访问权限
                        network_service = self.services.get('network_access')
                        self.logger.info(f"缩略图权限检查 - 网络服务可用: {network_service is not None}")
                        if network_service:
                            ip_address = request.remote_addr
                            self.logger.info(f"缩略图权限检查 - IP地址: {ip_address}")
                            access_check = network_service.check_folder_access_permission(ip_address, folder_permissions)
                            self.logger.info(f"缩略图权限检查 - 访问检查结果: {access_check}")

                            if not access_check.get('allowed', False):
                                # 静默返回404，不暴露权限信息
                                self.logger.info(f"缩略图访问被拒绝: {access_check.get('reason', '访问被拒绝')}")
                                abort(404)
                        else:
                            self.logger.warning(f"缩略图权限检查 - 网络访问控制服务未找到，跳过权限检查")

                # 检查是否为图片文件
                filename = file_info.get('filename', '') or file_info.get('name', '') or file_info.get('relative_path', '')
                self.logger.info(f"缩略图请求 - 文件名: {filename}")
                self.logger.info(f"缩略图请求 - 文件信息字段: {list(file_info.keys())}")

                # 如果还是没有文件名，尝试从路径中提取
                if not filename:
                    full_path = file_info.get('full_path', '')
                    if full_path:
                        filename = os.path.basename(full_path)
                        self.logger.info(f"从路径提取文件名: {filename}")

                if not filename:
                    self.logger.warning(f"缩略图请求失败 - 文件名为空: {file_id}")
                    abort(404)

                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                allowed_exts = ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
                self.logger.info(f"缩略图请求 - 文件扩展名: {ext}, 允许的扩展名: {allowed_exts}")
                if ext not in allowed_exts:
                    self.logger.warning(f"缩略图请求失败 - 不支持的文件格式: {ext}")
                    abort(404)

                # 检查文件路径
                full_path = file_info.get('full_path', '')
                self.logger.info(f"缩略图请求 - 完整路径: {full_path}")

                if not full_path or not os.path.exists(full_path):
                    self.logger.warning(f"缩略图请求失败 - 文件路径不存在: {full_path}")
                    abort(404)

                # 生成缩略图
                thumbnail_service = self.services.get('thumbnail')
                self.logger.info(f"缩略图服务可用: {thumbnail_service is not None}")

                if thumbnail_service:
                    self.logger.info(f"开始生成缩略图: {full_path}, 尺寸: {size}")
                    thumbnail_path = thumbnail_service.get_thumbnail(full_path, size)
                    self.logger.info(f"缩略图路径: {thumbnail_path}")

                    if thumbnail_path:
                        # 如果是相对路径，转换为绝对路径
                        if not os.path.isabs(thumbnail_path):
                            # 相对于backend目录
                            backend_dir = os.path.dirname(os.path.dirname(__file__))
                            thumbnail_path = os.path.join(backend_dir, thumbnail_path)
                            self.logger.info(f"转换为绝对路径: {thumbnail_path}")

                        if os.path.exists(thumbnail_path):
                            self.logger.info(f"缩略图生成成功，返回文件: {thumbnail_path}")
                            return send_file(thumbnail_path, mimetype='image/jpeg')
                        else:
                            self.logger.warning(f"缩略图文件不存在: {thumbnail_path}")

                    # 缩略图生成失败，尝试返回原文件（如果是小图片）
                    self.logger.warning(f"缩略图生成失败，尝试返回原文件")
                    if file_info.get('file_size', 0) < 1024 * 1024:  # 小于1MB
                        try:
                            self.logger.info(f"返回原文件: {full_path}")
                            return send_file(full_path)
                        except Exception as e:
                            self.logger.error(f"返回原文件失败: {e}")
                            abort(404)
                    else:
                        self.logger.warning(f"文件过大，无法返回原文件: {file_info.get('file_size', 0)} bytes")
                        abort(404)
                else:
                    self.logger.warning("缩略图服务不可用")
                    # 缩略图服务不可用，返回原文件（如果是小图片）
                    if file_info.get('file_size', 0) < 1024 * 1024:  # 小于1MB
                        try:
                            self.logger.info(f"缩略图服务不可用，返回原文件: {full_path}")
                            return send_file(full_path)
                        except Exception as e:
                            self.logger.error(f"返回原文件失败: {e}")
                            abort(404)
                    else:
                        abort(503)

            except Exception as e:
                self.logger.error(f"获取缩略图失败: {e}")
                abort(500)

        @self.app.route('/api/files/<int:file_id>/preview', methods=['GET'])
        def get_file_preview(file_id):
            """获取文件预览"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                file_service = self.services.get('file')
                if not file_service:
                    abort(503)

                file_info = file_service.get_file_info(file_id)
                if not file_info:
                    abort(404)

                # 检查文件夹级别的网络访问权限
                folder_id = file_info.get('folder_id')
                if folder_id:
                    folder_permissions_result = file_service.get_folder_permissions(folder_id)
                    if folder_permissions_result.get('success', False):
                        folder_permissions = folder_permissions_result.get('permissions', {})

                        # 检查网络访问权限
                        network_service = self.services.get('network_access')
                        if network_service:
                            ip_address = request.remote_addr
                            access_check = network_service.check_folder_access_permission(ip_address, folder_permissions)

                            if not access_check.get('allowed', False):
                                # 静默返回404，不暴露权限信息
                                self.logger.info(f"文件预览访问被拒绝: {access_check.get('reason', '访问被拒绝')}")
                                abort(404)

                # 检查文件是否存在
                if not file_info.get('exists', False):
                    abort(404)

                # 检查是否为图片文件
                filename = file_info.get('name', '')
                if not filename:
                    abort(404)

                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                web_safe_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']

                if ext in web_safe_exts:
                    # 直接返回图片文件
                    return send_file(file_info['full_path'])
                else:
                    # 对于其他格式，尝试生成预览
                    thumbnail_service = self.services.get('thumbnail')
                    if thumbnail_service:
                        preview_path = thumbnail_service.get_thumbnail(
                            file_info['full_path'], 'large'
                        )

                        if preview_path and os.path.exists(preview_path):
                            return send_file(preview_path, mimetype='image/jpeg')
                        else:
                            abort(404)
                    else:
                        abort(503)

            except Exception as e:
                self.logger.error(f"获取文件预览失败: {e}")
                abort(500)

        # 新的下载接口
        @self.app.route('/api/download/single/<int:file_id>', methods=['POST'])
        def download_single_file(file_id):
            """单文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401



                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('user_id') or user_info.get('id')
                ip_address = request.remote_addr

                self.logger.info(f"新单文件下载: user_info={user_info}, user_id={user_id}, file_id={file_id}")

                # 准备请求上下文
                request_context = {
                    'session_id': request.headers.get('X-Session-ID'),
                    'ip_address': ip_address,
                    'user_agent': request.headers.get('User-Agent'),
                    'download_source': 'web'
                }

                result = download_service.prepare_single_file_download(
                    file_id, user_id, request_context=request_context
                )

                if result.get('success'):
                    # 记录下载到数据库
                    download_service.record_download(
                        file_id=file_id,
                        user_id=user_id,
                        download_type='single',
                        zip_path=result.get('zip_path'),
                        is_encrypted=result.get('is_encrypted'),
                        password=result.get('password'),
                        batch_id=result.get('batch_id'),
                        session_id=request_context['session_id'],
                        ip_address=request_context['ip_address'],
                        user_agent=request_context['user_agent'],
                        download_source=request_context['download_source'],
                        request_context=request_context
                    )

                    # 记录下载活动到监控系统
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'download',
                            {
                                'file_id': file_id,
                                'filename': result.get('filename'),
                                'file_size': result.get('file_size'),
                                'is_encrypted': result.get('is_encrypted')
                            },
                            ip_address
                        )

                    return jsonify({
                        'success': True,
                        'data': {
                            'download_id': result.get('file_id'),
                            'filename': result.get('filename'),
                            'file_size': result.get('file_size'),
                            'is_encrypted': result.get('is_encrypted'),
                            'download_url': f"/api/download/file/{os.path.basename(result.get('zip_path'))}",
                            'password_hint': '如需解压密码，请使用密码申请接口' if result.get('is_encrypted') else None
                        }
                    })
                else:
                    error_msg = result.get('error', '下载准备失败')
                    self.logger.warning(f"单文件下载准备失败: {error_msg}, file_id={file_id}, user_id={user_id}")

                    # 根据错误类型返回更准确的错误信息
                    if '文件不存在' in error_msg:
                        return jsonify({'error': '⚠️ 文件不存在 - 请刷新页面后重试'}), 404
                    elif '文件夹不存在' in error_msg:
                        return jsonify({'error': '⚠️ 文件夹不存在 - 文件可能已被移动或删除'}), 404
                    elif '没有下载权限' in error_msg:
                        return jsonify({'error': '⚠️ 下载权限不足 - 当前文件夹不允许下载'}), 403
                    elif '文件不存在于磁盘' in error_msg:
                        return jsonify({'error': '⚠️ 文件已损坏 - 文件在磁盘上不存在，请联系管理员'}), 404
                    else:
                        return jsonify({'error': f'⚠️ 下载准备失败 - {error_msg}'}), 400

            except Exception as e:
                self.logger.error(f"单文件下载失败: {e}")
                return jsonify({'error': '⚠️ 服务器内部错误 - 下载服务暂时不可用，请稍后重试'}), 500

        @self.app.route('/api/download/batch', methods=['POST'])
        @self.check_network_access()
        def download_batch_files():
            """批量文件下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_ids = data.get('file_ids', [])

                if not file_ids:
                    return jsonify({'error': '文件列表为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('user_id') or user_info.get('id')
                ip_address = request.remote_addr

                self.logger.info(f"批量下载: user_info={user_info}, user_id={user_id}, file_ids={file_ids}")

                # 准备请求上下文
                request_context = {
                    'session_id': request.headers.get('X-Session-ID'),
                    'ip_address': ip_address,
                    'user_agent': request.headers.get('User-Agent'),
                    'download_source': 'web'
                }

                result = download_service.prepare_batch_download(
                    file_ids, user_id, request_context=request_context
                )

                if result.get('success'):
                    # 记录批量下载活动到监控系统
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'batch_download',
                            {
                                'file_ids': file_ids,
                                'file_count': len(file_ids),
                                'is_encrypted': result.get('is_encrypted', False)
                            },
                            ip_address
                        )

                    # 直接返回文件流
                    zip_path = result.get('zip_path')
                    if os.path.exists(zip_path):
                        return send_file(
                            zip_path,
                            as_attachment=True,
                            download_name=f"batch_download_{int(time.time())}.zip",
                            mimetype='application/zip'
                        )
                    else:
                        return jsonify({'error': '压缩包生成失败'}), 500
                else:
                    error_msg = result.get('error', '批量下载准备失败')
                    self.logger.warning(f"批量下载准备失败: {error_msg}, file_ids={file_ids}, user_id={user_id}")

                    # 根据错误类型返回更准确的错误信息
                    if '文件数量超过限制' in error_msg:
                        return jsonify({'error': f'⚠️ 批量下载限制 - {error_msg}'}), 400
                    elif '没有有效的文件' in error_msg:
                        return jsonify({'error': '⚠️ 文件无效 - 所选文件不存在或已被删除'}), 404
                    elif '没有下载权限' in error_msg:
                        return jsonify({'error': '⚠️ 下载权限不足 - 部分文件不允许下载'}), 403
                    else:
                        return jsonify({'error': f'⚠️ 批量下载准备失败 - {error_msg}'}), 400

            except Exception as e:
                self.logger.error(f"批量下载失败: {e}")
                return jsonify({'error': '⚠️ 服务器内部错误 - 批量下载服务暂时不可用，请稍后重试'}), 500
        
        @self.app.route('/api/download/folder/<int:folder_id>', methods=['GET'])
        def download_folder(folder_id):
            """文件夹下载（压缩包形式）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401



                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('user_id') or user_info.get('id')
                ip_address = request.remote_addr

                # 准备请求上下文
                request_context = {
                    'session_id': request.headers.get('X-Session-ID'),
                    'ip_address': ip_address,
                    'user_agent': request.headers.get('User-Agent'),
                    'download_source': 'web'
                }

                result = download_service.prepare_folder_download(
                    folder_id, user_id, request_context=request_context
                )

                if result.get('success'):
                    # 记录文件夹下载活动到监控系统
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'folder_download',
                            {
                                'folder_id': folder_id,
                                'file_count': result.get('file_count', 0),
                                'is_encrypted': result.get('is_encrypted', False)
                            },
                            ip_address
                        )

                    # 直接返回文件流
                    zip_path = result.get('zip_path')
                    if os.path.exists(zip_path):
                        return send_file(
                            zip_path,
                            as_attachment=True,
                            download_name=f"folder_{folder_id}_{int(time.time())}.zip",
                            mimetype='application/zip'
                        )
                    else:
                        return jsonify({'error': '压缩包生成失败'}), 500
                else:
                    return jsonify({'error': result.get('error', '文件夹下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"文件夹下载失败: {e}")
                return jsonify({'error': '文件夹下载失败'}), 500

        @self.app.route('/api/download/file/<path:filename>', methods=['GET'])
        @self.check_network_access()
        def serve_download_file(filename):
            """提供下载文件服务"""
            try:
                download_service = self.services.get('download')
                if not download_service:
                    abort(503)

                file_path = os.path.join(download_service.temp_dir, filename)
                if not os.path.exists(file_path):
                    abort(404)

                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/zip'
                )

            except Exception as e:
                self.logger.error(f"文件下载服务失败: {e}")
                abort(500)

        @self.app.route('/api/download/password/request', methods=['POST'])
        def request_download_password():
            """申请下载密码"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                file_id = data.get('file_id')
                reason = data.get('reason', '')

                if not file_id:
                    return jsonify({'error': '文件ID不能为空'}), 400

                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                user_id = user_info.get('user_id') or user_info.get('id')
                result = download_service.request_password(file_id, user_id)

                if result.get('success'):
                    # 记录密码申请活动到监控系统
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'password_request',
                            {
                                'file_id': file_id,
                                'reason': reason,
                                'remaining_requests': result.get('remaining_requests')
                            },
                            request.remote_addr
                        )

                    return jsonify({
                        'success': True,
                        'data': {
                            'password': result.get('password'),
                            'remaining_requests': result.get('remaining_requests'),
                            'message': '密码申请成功'
                        }
                    })
                else:
                    return jsonify({'error': result.get('error', '密码申请失败')}), 400

            except Exception as e:
                self.logger.error(f"密码申请失败: {e}")
                return jsonify({'error': '密码申请失败'}), 500

        @self.app.route('/api/download/password/request', methods=['POST'])
        def request_package_password():
            """申请压缩包解压密码"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                data = request.get_json()
                record_id = data.get('record_id')
                reason = data.get('reason', '')

                if not record_id:
                    return jsonify({'error': '记录ID不能为空'}), 400

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取下载记录
                db_manager = self.services.get('database')
                if not db_manager:
                    return jsonify({'error': '数据库服务不可用'}), 503

                with db_manager.get_session() as session:
                    from models.download_record import DownloadRecord

                    record = session.query(DownloadRecord).filter_by(
                        id=record_id,
                        user_id=user_id
                    ).first()

                    if not record:
                        return jsonify({'error': '下载记录不存在'}), 404

                    if not record.is_encrypted:
                        return jsonify({'error': '该文件未加密，无需密码'}), 400

                    if not record.password:
                        return jsonify({'error': '密码信息不可用'}), 400

                    # 记录密码申请活动到监控系统
                    monitoring_service = self.services.get('monitoring')
                    if monitoring_service:
                        monitoring_service.record_user_activity(
                            user_id,
                            'package_password_request',
                            {
                                'record_id': record_id,
                                'download_type': record.download_type,
                                'reason': reason
                            },
                            request.remote_addr
                        )

                    return jsonify({
                        'success': True,
                        'data': {
                            'password': record.password,
                            'message': '密码获取成功'
                        }
                    })

            except Exception as e:
                self.logger.error(f"申请压缩包密码失败: {e}")
                return jsonify({'error': '申请密码失败'}), 500

        @self.app.route('/api/download/redownload/batch/<int:record_id>', methods=['POST'])
        def redownload_batch(record_id):
            """重新下载批量文件"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取下载记录
                db_manager = self.services.get('database')
                if not db_manager:
                    return jsonify({'error': '数据库服务不可用'}), 503

                with db_manager.get_session() as session:
                    from models.download_record import DownloadRecord
                    from models.download_batch import DownloadBatch

                    record = session.query(DownloadRecord).filter_by(
                        id=record_id,
                        user_id=user_id,
                        download_type='batch'
                    ).first()

                    if not record:
                        return jsonify({'error': '批量下载记录不存在'}), 404

                    # 获取批次信息
                    batch = session.query(DownloadBatch).filter_by(id=record.batch_id).first()
                    if not batch:
                        return jsonify({'error': '批次信息不存在'}), 404

                    # 重新准备批量下载
                    download_service = self.services.get('download')
                    if not download_service:
                        return jsonify({'error': '下载服务不可用'}), 503

                    file_ids = batch.file_ids if hasattr(batch, 'file_ids') else []
                    if not file_ids:
                        return jsonify({'error': '批次文件列表为空'}), 400

                    result = download_service.prepare_batch_download(
                        file_ids, user_id, request_context={
                            'ip_address': request.remote_addr,
                            'user_agent': request.headers.get('User-Agent'),
                            'download_source': 'web'
                        }
                    )

                    if result.get('success'):
                        return jsonify({
                            'success': True,
                            'message': '重新下载准备完成',
                            'download_url': f"/api/download/file/{result.get('zip_filename')}"
                        })
                    else:
                        return jsonify({'error': result.get('error', '重新下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"重新下载批量文件失败: {e}")
                return jsonify({'error': '重新下载失败'}), 500

        @self.app.route('/api/download/redownload/folder/<int:record_id>', methods=['POST'])
        def redownload_folder(record_id):
            """重新下载文件夹"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取下载记录
                db_manager = self.services.get('database')
                if not db_manager:
                    return jsonify({'error': '数据库服务不可用'}), 503

                with db_manager.get_session() as session:
                    from models.download_record import DownloadRecord

                    record = session.query(DownloadRecord).filter_by(
                        id=record_id,
                        user_id=user_id,
                        download_type='folder'
                    ).first()

                    if not record:
                        return jsonify({'error': '文件夹下载记录不存在'}), 404

                    if not record.folder_id:
                        return jsonify({'error': '文件夹ID不存在'}), 400

                    # 重新准备文件夹下载
                    download_service = self.services.get('download')
                    if not download_service:
                        return jsonify({'error': '下载服务不可用'}), 503

                    result = download_service.prepare_folder_download(
                        record.folder_id, user_id, request_context={
                            'ip_address': request.remote_addr,
                            'user_agent': request.headers.get('User-Agent'),
                            'download_source': 'web'
                        }
                    )

                    if result.get('success'):
                        return jsonify({
                            'success': True,
                            'message': '重新下载准备完成',
                            'download_url': f"/api/download/file/{result.get('zip_filename')}"
                        })
                    else:
                        return jsonify({'error': result.get('error', '重新下载准备失败')}), 400

            except Exception as e:
                self.logger.error(f"重新下载文件夹失败: {e}")
                return jsonify({'error': '重新下载失败'}), 500

        @self.app.route('/api/download/records', methods=['GET'])
        def get_download_records():
            """获取用户下载记录 - 简化版本"""
            try:
                self.logger.info("=== 调试模式：收到下载记录请求 ===")
                
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                self.logger.info(f"收到的token: {token[:20]}..." if token else "未收到token")
                
                user_info = self._validate_token(token)
                self.logger.info(f"用户验证结果: {user_info}")
                
                if not user_info:
                    self.logger.warning("用户验证失败，返回401")
                    return jsonify({'success': False, 'error': '请先登录'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')
                if not user_id:
                    return jsonify({'success': False, 'error': '用户信息无效'}), 401

                # 获取查询参数
                page = request.args.get('page', 1, type=int)
                limit = request.args.get('limit', 50, type=int)

                # 获取筛选参数
                filters = {}
                if request.args.get('type'):
                    filters['download_type'] = request.args.get('type')
                if request.args.get('date_from'):
                    filters['date_from'] = request.args.get('date_from')
                if request.args.get('date_to'):
                    filters['date_to'] = request.args.get('date_to')
                if request.args.get('filename'):
                    filters['filename'] = request.args.get('filename')

                # 获取下载记录
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'success': False, 'error': '服务不可用'}), 503

                self.logger.info(f"调用下载服务获取用户 {user_id} 的记录，页码: {page}, 每页: {limit}, 筛选条件: {filters}")
                result = download_service.get_user_download_records(user_id, page, limit, filters)
                self.logger.info(f"下载服务返回结果: {result}")

                if result.get('success'):
                    records = result.get('records', [])
                    self.logger.info(f"获取到 {len(records)} 条原始记录")
                    
                    # 简化记录格式
                    simple_records = []
                    for i, record in enumerate(records):
                        self.logger.info(f"处理第 {i+1} 条记录: {record}")
                        # 确保下载类型为中文（下载服务已经转换，但这里做二次确认）
                        download_type = record.get('download_type', 'single')
                        if download_type in ['single', 'batch', 'folder']:
                            download_type_map = {
                                'single': '单文件',
                                'batch': '批量下载',
                                'folder': '文件夹'
                            }
                            download_type = download_type_map.get(download_type, download_type)

                        simple_record = {
                            'id': record.get('id'),
                            'filename': record.get('filename', '未知文件'),
                            'file_size': record.get('file_size', 0),
                            'download_time': record.get('download_time', ''),
                            'created_at': record.get('created_at', ''),
                            'download_type': download_type,  # 使用处理后的中文类型
                            'is_encrypted': record.get('is_encrypted', False),
                            'file_count': record.get('file_count', 0),
                            'folder_name': record.get('folder_name', ''),
                            'folder_id': record.get('folder_id'),
                            'file_id': record.get('file_id'),
                            'zip_filename': record.get('zip_filename', ''),
                            'password': record.get('password', '') if record.get('is_encrypted') else None
                        }
                        simple_records.append(simple_record)

                    self.logger.info(f"返回 {len(simple_records)} 条简化记录")
                    return jsonify({
                        'success': True,
                        'records': simple_records,
                        'total': result.get('total', len(simple_records)),
                        'page': result.get('page', page),
                        'limit': result.get('limit', limit),
                        'filters': filters
                    })
                else:
                    return jsonify({
                        'success': True,
                        'records': [],
                        'total': 0,
                        'page': page,
                        'limit': limit,
                        'filters': filters
                    })

            except Exception as e:
                self.logger.error(f"获取下载记录失败: {e}")
                return jsonify({'success': False, 'error': '获取失败'}), 500

        @self.app.route('/api/download/password-requests', methods=['GET'])
        def get_password_requests():
            """获取用户密码申请记录"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取查询参数
                page = request.args.get('page', 1, type=int)
                limit = request.args.get('limit', 50, type=int)

                # 调用下载服务获取密码申请记录
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                result = download_service.get_user_password_requests(user_id, page, limit)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'requests': result.get('requests', []),
                        'total': result.get('total', 0),
                        'page': page,
                        'limit': limit
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '获取密码申请记录失败')
                    }), 400

            except Exception as e:
                self.logger.error(f"获取密码申请记录失败: {e}")
                return jsonify({'error': '获取密码申请记录失败'}), 500

        @self.app.route('/api/download/records/advanced', methods=['GET'])
        def get_advanced_download_records():
            """获取高级筛选的下载记录"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取筛选参数
                filters = {
                    'start_date': request.args.get('start_date'),
                    'end_date': request.args.get('end_date'),
                    'download_type': request.args.get('download_type'),
                    'target_type': request.args.get('target_type'),
                    'download_source': request.args.get('download_source'),
                    'is_encrypted': request.args.get('is_encrypted'),
                    'batch_id': request.args.get('batch_id')
                }
                
                page = request.args.get('page', 1, type=int)
                limit = request.args.get('limit', 50, type=int)

                # 调用下载服务
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                result = download_service.get_user_download_records(
                    user_id, page, limit, filters
                )

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'records': result.get('records', []),
                        'total': result.get('total', 0),
                        'page': page,
                        'limit': limit,
                        'filters': filters
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '获取下载记录失败')
                    }), 400

            except Exception as e:
                self.logger.error(f"获取高级下载记录失败: {e}")
                return jsonify({'error': '获取高级下载记录失败'}), 500

        @self.app.route('/api/download/statistics', methods=['GET'])
        def get_download_statistics():
            """获取用户下载统计"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 调用下载服务
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                result = download_service.get_user_download_statistics(user_id)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'statistics': result.get('statistics', {})
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '获取下载统计失败')
                    }), 400

            except Exception as e:
                self.logger.error(f"获取下载统计失败: {e}")
                return jsonify({'error': '获取下载统计失败'}), 500

        @self.app.route('/api/download/analytics', methods=['GET'])
        def get_download_analytics():
            """获取下载分析数据"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取分析参数
                days = request.args.get('days', 30, type=int)
                analytics_type = request.args.get('type', 'daily')

                # 调用下载服务
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                result = download_service.get_download_analytics(user_id, days, analytics_type)

                if result.get('success'):
                    return jsonify({
                        'success': True,
                        'analytics': result.get('analytics', {}),
                        'period_days': days,
                        'analytics_type': analytics_type
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': result.get('error', '获取下载分析失败')
                    }), 400

            except Exception as e:
                self.logger.error(f"获取下载分析失败: {e}")
                return jsonify({'error': '获取下载分析失败'}), 500

        @self.app.route('/api/download/batches', methods=['GET'])
        def get_download_batches():
            """获取下载批次信息"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                user_id = user_info.get('user_id') or user_info.get('id')

                # 获取查询参数
                page = request.args.get('page', 1, type=int)
                limit = request.args.get('limit', 20, type=int)
                status = request.args.get('status')  # pending, completed, failed, expired

                # 调用下载服务
                download_service = self.services.get('download')
                if not download_service:
                    return jsonify({'error': '下载服务不可用'}), 503

                # 获取批次列表
                try:
                    from models.download_batch import DownloadBatch
                    from datetime import datetime
                    
                    db_manager = self.services.get('database')
                    if not db_manager:
                        return jsonify({'error': '数据库服务不可用'}), 503
                    
                    with db_manager.get_session() as session:
                        query = session.query(DownloadBatch).filter(
                            DownloadBatch.user_id == user_id
                        )
                        
                        # 状态筛选
                        if status:
                            query = query.filter(DownloadBatch.status == status)
                        
                        # 排序
                        query = query.order_by(DownloadBatch.created_at.desc())
                        
                        # 分页
                        total = query.count()
                        batches = query.offset((page - 1) * limit).limit(limit).all()
                        
                        # 转换为字典
                        batch_list = []
                        for batch in batches:
                            batch_dict = batch.to_dict()
                            batch_list.append(batch_dict)
                        
                        return jsonify({
                            'success': True,
                            'batches': batch_list,
                            'total': total,
                            'page': page,
                            'limit': limit
                        })
                        
                except Exception as db_error:
                    self.logger.error(f"数据库查询失败: {db_error}")
                    return jsonify({
                        'success': False,
                        'error': '获取批次信息失败'
                    }), 500

            except Exception as e:
                self.logger.error(f"获取下载批次失败: {e}")
                return jsonify({'error': '获取下载批次失败'}), 500

        @self.app.route('/api/admin/users', methods=['GET'])
        def get_users():
            """获取用户列表（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    page = request.args.get('page', 1, type=int)
                    page_size = request.args.get('page_size', 20, type=int)
                    search_query = request.args.get('search', '')

                    result = user_service.get_user_list(page, page_size, search_query)
                    return jsonify(result)
                else:
                    return jsonify({'users': [], 'total_count': 0})

            except Exception as e:
                self.logger.error(f"获取用户列表失败: {e}")
                return jsonify({'error': '获取用户列表失败'}), 500

        @self.app.route('/api/admin/users', methods=['POST'])
        def create_user():
            """创建用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({
                        'success': False,
                        'error': '⚠️ 需要管理员权限才能创建用户',
                        'error_type': 'permission_denied'
                    }), 403

                data = request.get_json()
                username = data.get('username')
                password = data.get('password')
                email = data.get('email')
                full_name = data.get('full_name')
                user_group = data.get('user_group', 'user')
                is_admin = data.get('is_admin', False)

                if not username or not password:
                    return jsonify({
                        'success': False,
                        'error': '⚠️ 用户名和密码不能为空',
                        'error_type': 'validation_error',
                        'suggestion': '请确保填写了用户名和密码字段'
                    }), 400

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.create_user(
                        username, password, email, full_name, user_group, is_admin
                    )
                    
                    if result.get('success'):
                        # 成功时返回状态码200
                        return jsonify(result), 200
                    else:
                        # 失败时根据错误类型返回适当的状态码
                        error_type = result.get('error_type', 'unknown')
                        if error_type in ['duplicate_username', 'duplicate_email', 'database_constraint']:
                            return jsonify(result), 409  # Conflict
                        else:
                            return jsonify(result), 400  # Bad Request
                else:
                    return jsonify({
                        'success': False,
                        'error': '❌ 用户服务暂时不可用',
                        'error_type': 'service_unavailable',
                        'suggestion': '请稍后重试或联系系统管理员'
                    }), 503

            except Exception as e:
                self.logger.error(f"创建用户失败: {e}")
                return jsonify({
                    'success': False,
                    'error': f'❌ 创建用户时发生系统错误: {str(e)}',
                    'error_type': 'system_error',
                    'suggestion': '请稍后重试或联系系统管理员'
                }), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['PUT'])
        def update_user(user_id):
            """更新用户信息（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.update_user(user_id, data)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"更新用户失败: {e}")
                return jsonify({'error': '更新用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
        def delete_user(user_id):
            """删除用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                user_service = self.services.get('user')
                if user_service:
                    result = user_service.delete_user(user_id)
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"删除用户失败: {e}")
                return jsonify({'error': '删除用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>/ban', methods=['POST'])
        def ban_user(user_id):
            """封禁用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                ban_type = data.get('ban_type', 'login')
                duration_hours = data.get('duration_hours', 24)
                reason = data.get('reason', '')
                is_permanent = data.get('is_permanent', False)
                admin_note = data.get('admin_note', '')

                # 验证参数
                if ban_type not in ['login', 'search', 'download', 'all']:
                    return jsonify({'error': '无效的封禁类型'}), 400

                if not is_permanent and (duration_hours <= 0 or duration_hours > 8760):
                    return jsonify({'error': '封禁时长必须在1-8760小时之间'}), 400

                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    result = behavior_service.ban_user(
                        user_id=user_id,
                        ban_type=ban_type,
                        duration_hours=duration_hours if not is_permanent else 0,
                        reason=reason,
                        admin_id=user_info['user_id'],
                        is_permanent=is_permanent,
                        admin_note=admin_note
                    )
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户行为服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"封禁用户失败: {e}")
                return jsonify({'error': '封禁用户失败'}), 500

        @self.app.route('/api/admin/users/<int:user_id>/unban', methods=['POST'])
        def unban_user(user_id):
            """解封用户（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json() or {}
                ban_type = data.get('ban_type')  # 可选，不指定则解封所有类型

                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    result = behavior_service.unban_user(
                        user_id=user_id,
                        ban_type=ban_type,
                        admin_id=user_info['user_id']
                    )
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户行为服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"解封用户失败: {e}")
                return jsonify({'error': '解封用户失败'}), 500

        @self.app.route('/api/admin/bans', methods=['GET'])
        def get_user_bans():
            """获取用户封禁列表（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                page = request.args.get('page', 1, type=int)
                page_size = request.args.get('page_size', 20, type=int)
                ban_type = request.args.get('ban_type')
                is_active = request.args.get('is_active')
                user_id = request.args.get('user_id', type=int)

                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    result = behavior_service.get_user_bans_paginated(
                        page=page,
                        page_size=page_size,
                        ban_type=ban_type,
                        is_active=is_active,
                        user_id=user_id
                    )
                    return jsonify(result)
                else:
                    return jsonify({'bans': [], 'total_count': 0})

            except Exception as e:
                self.logger.error(f"获取封禁列表失败: {e}")
                return jsonify({'error': '获取封禁列表失败'}), 500

        @self.app.route('/api/admin/bans/<int:ban_id>/extend', methods=['POST'])
        def extend_ban(ban_id):
            """延长封禁时间（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                additional_hours = data.get('additional_hours', 24)

                if additional_hours <= 0 or additional_hours > 8760:
                    return jsonify({'error': '延长时间必须在1-8760小时之间'}), 400

                behavior_service = self.services.get('user_behavior')
                if behavior_service:
                    result = behavior_service.extend_user_ban(
                        ban_id=ban_id,
                        additional_hours=additional_hours,
                        admin_id=user_info['user_id']
                    )
                    return jsonify(result)
                else:
                    return jsonify({'error': '用户行为服务不可用'}), 503

            except Exception as e:
                self.logger.error(f"延长封禁失败: {e}")
                return jsonify({'error': '延长封禁失败'}), 500
        
        @self.app.route('/api/admin/stats', methods=['GET'])
        def get_statistics():
            """获取系统统计信息"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 收集各种统计信息
                stats = {
                    'total_users': 0,
                    'online_users': 0,
                    'total_files': 0,
                    'total_size': 0,
                    'today_downloads': 0,
                    'today_uploads': 0,
                    'today_searches': 0
                }

                # 获取用户统计
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    stats.update({
                        'total_users': user_stats.get('total_users', 0),
                        'online_users': user_stats.get('online_users', 0),
                        'active_users': user_stats.get('active_users', 0),
                        'admin_users': user_stats.get('admin_users', 0)
                    })

                # 获取监控统计
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    monitoring_stats = monitoring_service.get_activity_statistics()
                    stats.update({
                        'total_activities': monitoring_stats.get('total_activities', 0),
                        'recent_activities_24h': monitoring_stats.get('recent_activities_24h', 0),
                        'activity_types': monitoring_stats.get('activity_types', {})
                    })

                # 获取文件统计
                file_service = self.services.get('file')
                if file_service:
                    # TODO: 实现文件统计
                    pass

                return jsonify({
                    'success': True,
                    'data': stats
                })

            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                return jsonify({'error': '获取统计信息失败'}), 500

        @self.app.route('/api/system/info', methods=['GET'])
        def get_system_info():
            """获取系统信息（普通用户可访问）"""
            try:
                # 验证用户权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info:
                    return jsonify({'error': '未授权访问'}), 401

                # 基础系统信息
                info = {
                    'version': '1.0.0',
                    'uptime': time.time() - getattr(self, 'start_time', time.time()),
                    'services_count': len(self.services),
                    'status': 'running' if self.running else 'stopped'
                }

                # 获取在线用户数（不显示具体用户信息）
                user_service = self.services.get('user')
                if user_service:
                    user_stats = user_service.get_user_statistics()
                    info['online_users'] = user_stats.get('online_users', 0)

                # 获取存储信息
                monitoring_service = self.services.get('monitoring')
                if monitoring_service:
                    system_status = monitoring_service.get_system_status()
                    system_stats = system_status.get('system_stats', {})
                    if 'disk' in system_stats:
                        disk_info = system_stats['disk']
                        info['storage'] = {
                            'total': disk_info.get('total', 0),
                            'used': disk_info.get('used', 0),
                            'free': disk_info.get('free', 0),
                            'percent': disk_info.get('percent', 0)
                        }

                return jsonify({
                    'success': True,
                    'data': info
                })

            except Exception as e:
                self.logger.error(f"获取系统信息失败: {e}")
                return jsonify({'error': '获取系统信息失败'}), 500

        @self.app.route('/api/system/online-users', methods=['GET'])
        def get_online_users():
            """获取在线用户列表"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin', False):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 获取在线用户
                user_service = self.services.get('user')
                if user_service:
                    online_users = user_service.get_online_users()
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': online_users,
                            'count': len(online_users)
                        }
                    })
                else:
                    return jsonify({
                        'success': True,
                        'data': {
                            'users': [],
                            'count': 0
                        }
                    })

            except Exception as e:
                self.logger.error(f"获取在线用户失败: {e}")
                return jsonify({'error': '获取在线用户失败'}), 500

        @self.app.route('/api/admin/config', methods=['GET'])
        def get_system_config():
            """获取系统配置（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not self._check_permission(user_info, 'admin'):
                    return jsonify({'error': '需要管理员权限'}), 403

                # 获取完整配置
                config = self.settings.settings.copy()

                return jsonify({
                    'success': True,
                    'data': config
                })

            except Exception as e:
                self.logger.error(f"获取系统配置失败: {e}")
                return jsonify({'error': '获取配置失败'}), 500

        @self.app.route('/api/admin/config', methods=['PUT'])
        def update_system_config():
            """更新系统配置（管理员）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not self._check_permission(user_info, 'admin'):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                if not data:
                    return jsonify({'error': '配置数据不能为空'}), 400

                # 记录配置变更
                old_config = self.settings.settings.copy()

                # 更新配置
                for key, value in data.items():
                    self.settings.set(key, value)

                # 保存配置
                self.settings.save_settings()

                # 检查是否需要重启服务
                restart_required = self._check_restart_required(old_config, data)

                # 广播配置变更通知
                self.broadcast_notification(
                    f"系统配置已更新 - 用户: {user_info.get('username', 'unknown')}",
                    "config_update"
                )

                # 通过WebSocket推送配置变更
                self.socketio.emit('config_updated', {
                    'config': self.settings.settings,
                    'restart_required': restart_required,
                    'timestamp': datetime.now().isoformat()
                })

                return jsonify({
                    'success': True,
                    'message': '配置更新成功',
                    'restart_required': restart_required
                })

            except Exception as e:
                self.logger.error(f"更新系统配置失败: {e}")
                return jsonify({'error': '更新配置失败'}), 500

        @self.app.route('/api/config/public', methods=['GET'])
        def get_public_config():
            """获取公开配置（前端使用）"""
            try:
                # 只返回前端需要的配置信息
                public_config = {
                    'server': {
                        'host': self.settings.get('server.host', '0.0.0.0'),
                        'port': self.settings.get('server.port', 8086),
                        'frontend_port': self.settings.get('server.frontend_port', 8084)
                    },
                    'file_share': {
                        'allowed_extensions': self.settings.get('file_share.allowed_extensions', []),
                        'max_file_size': self.settings.get('file_share.max_file_size', 1073741824),
                        'thumbnail_sizes': self.settings.get('file_share.thumbnail_sizes', {})
                    },
                    'download': {
                        'enable_single_download': self.settings.get('download.enable_single_download', True),
                        'enable_batch_download': self.settings.get('download.enable_batch_download', True),
                        'enable_folder_download': self.settings.get('download.enable_folder_download', True),
                        'max_batch_files': self.settings.get('download.max_batch_files', 100),
                        'max_package_size': self.settings.get('download.max_package_size', 524288000)
                    },
                    'search': {
                        'enable_text_search': self.settings.get('search.enable_text_search', True),
                        'enable_image_search': self.settings.get('search.enable_image_search', True),
                        'max_search_results': self.settings.get('search.max_search_results', 1000)
                    },
                    'notifications': {
                        'enable_rolling_notifications': self.settings.get('notifications.enable_rolling_notifications', True),
                        'notification_duration': self.settings.get('notifications.notification_duration', 5000),
                        'max_notifications': self.settings.get('notifications.max_notifications', 10)
                    }
                }

                return jsonify({
                    'success': True,
                    'data': public_config
                })

            except Exception as e:
                self.logger.error(f"获取公开配置失败: {e}")
                return jsonify({'error': '获取配置失败'}), 500

        @self.app.route('/api/system/marquee', methods=['GET'])
        def get_marquee_message():
            """获取滚动字幕消息"""
            try:
                # 获取滚动字幕设置
                marquee_config = self.settings.settings.get('marquee', {})
                
                return jsonify({
                    'success': True,
                    'data': {
                        'message': marquee_config.get('message', '欢迎使用企业级文件共享系统！'),
                        'theme': marquee_config.get('theme', ''),
                        'hidden': marquee_config.get('hidden', False),
                        'enabled': marquee_config.get('enabled', True),
                        'font_size': marquee_config.get('font_size', 14),
                        'scroll_speed': marquee_config.get('scroll_speed', 15)
                    }
                })

            except Exception as e:
                self.logger.error(f"获取滚动字幕失败: {e}")
                return jsonify({'error': '获取滚动字幕失败'}), 500

        @self.app.route('/api/admin/marquee', methods=['PUT'])
        def update_marquee_message():
            """更新滚动字幕消息（管理员权限）"""
            try:
                # 验证管理员权限
                token = request.headers.get('Authorization', '').replace('Bearer ', '')
                user_info = self._validate_token(token)
                if not user_info or not user_info.get('is_admin'):
                    return jsonify({'error': '需要管理员权限'}), 403

                data = request.get_json()
                message = data.get('message', '')
                theme = data.get('theme', '')
                hidden = data.get('hidden', False)
                enabled = data.get('enabled', True)
                font_size = data.get('font_size', 14)
                scroll_speed = data.get('scroll_speed', 15)

                # 更新设置
                if 'marquee' not in self.settings.settings:
                    self.settings.settings['marquee'] = {}

                self.settings.settings['marquee'].update({
                    'message': message,
                    'theme': theme,
                    'hidden': hidden,
                    'enabled': enabled,
                    'font_size': font_size,
                    'scroll_speed': scroll_speed
                })

                # 保存设置
                self.settings.save_settings()

                # 广播更新给所有客户端
                if hasattr(self, 'socketio'):
                    self.socketio.emit('marquee_update', {
                        'message': message,
                        'theme': theme,
                        'hidden': hidden,
                        'enabled': enabled,
                        'font_size': font_size,
                        'scroll_speed': scroll_speed
                    })

                return jsonify({
                    'success': True,
                    'message': '滚动字幕更新成功'
                })

            except Exception as e:
                self.logger.error(f"更新滚动字幕失败: {e}")
                return jsonify({'error': '更新滚动字幕失败'}), 500

        @self.app.route('/api/network/access/check', methods=['POST'])
        def check_network_access():
            """检查网络访问权限"""
            try:
                data = request.get_json()
                ip_address = data.get('ip_address') or self.network_access_service.get_client_ip(request)
                folder_id = data.get('folder_id')

                if not self.network_access_service:
                    return jsonify({
                        'allowed': True,
                        'reason': '网络访问控制服务不可用',
                        'access_type': 'unknown'
                    })

                if folder_id:
                    # 检查文件夹级别权限
                    file_service = self.services.get('file')
                    if file_service:
                        perm_result = file_service.get_folder_permissions(folder_id)
                        if perm_result.get('success'):
                            folder_permissions = perm_result.get('permissions', {})
                            access_result = self.network_access_service.check_folder_access_permission(
                                ip_address, folder_permissions
                            )
                        else:
                            access_result = self.network_access_service.check_global_access_permission(ip_address)
                    else:
                        access_result = self.network_access_service.check_global_access_permission(ip_address)
                else:
                    # 检查全局权限
                    access_result = self.network_access_service.check_global_access_permission(ip_address)

                # 记录检查结果
                self.network_access_service.log_access_attempt(
                    ip_address, access_result, 'manual_check'
                )

                return jsonify(access_result)

            except Exception as e:
                self.logger.error(f"检查网络访问权限失败: {e}")
                return jsonify({'error': '检查网络访问权限失败'}), 500

        @self.app.route('/api/network/info', methods=['GET'])
        def get_network_info():
            """获取网络信息"""
            try:
                ip_address = request.args.get('ip') or self.network_access_service.get_client_ip(request)

                if not self.network_access_service:
                    return jsonify({'error': '网络访问控制服务不可用'}), 503

                network_info = self.network_access_service.get_network_info(ip_address)
                return jsonify(network_info)

            except Exception as e:
                self.logger.error(f"获取网络信息失败: {e}")
                return jsonify({'error': '获取网络信息失败'}), 500

    def register_socketio_events(self):
        """注册SocketIO事件"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            self.logger.info(f"客户端连接: {request.sid}")
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            self.logger.info(f"客户端断开连接: {request.sid}")
        
        @self.socketio.on('ping')
        def handle_ping():
            """心跳检测"""
            emit('pong', {'timestamp': datetime.now().isoformat()})
    
    def broadcast_notification(self, message: str, notification_type: str = "info"):
        """广播通知"""
        try:
            self.socketio.emit('notification', {
                'message': message,
                'type': notification_type,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"广播通知失败: {e}")

    def broadcast_enhanced_notification(self, notification_data: dict):
        """广播增强通知（支持图片和全体用户）"""
        try:
            # 构建通知数据
            enhanced_notification = {
                'id': f"notification_{int(datetime.now().timestamp() * 1000)}",
                'title': notification_data.get('title', '系统通知'),
                'content': notification_data.get('content', ''),
                'message': notification_data.get('content', ''),  # 兼容旧版本
                'type': notification_data.get('type', 'info'),
                'scope': notification_data.get('scope', '全体用户'),
                'timestamp': notification_data.get('timestamp', datetime.now().isoformat()),
                'image_data': notification_data.get('image_data'),
                'image_filename': notification_data.get('image_filename'),
                'has_image': bool(notification_data.get('image_data'))
            }

            # 根据范围发送通知
            scope = notification_data.get('scope', '全体用户')

            if scope == '全体用户':
                # 发送给所有连接的客户端
                self.socketio.emit('enhanced_notification', enhanced_notification)
                self.logger.info(f"向全体用户发送通知: {enhanced_notification['title']}")

            elif scope == '在线用户':
                # 发送给当前在线的用户
                self.socketio.emit('enhanced_notification', enhanced_notification)
                self.logger.info(f"向在线用户发送通知: {enhanced_notification['title']}")

            elif scope == '管理员':
                # 发送给管理员用户（需要实现用户角色检查）
                self.socketio.emit('enhanced_notification', enhanced_notification, room='admin')
                self.logger.info(f"向管理员发送通知: {enhanced_notification['title']}")

            # 保存通知到数据库（可选）
            self._save_notification_to_db(enhanced_notification)

        except Exception as e:
            self.logger.error(f"广播增强通知失败: {e}")

    def _save_notification_to_db(self, notification_data: dict):
        """保存通知到数据库"""
        try:
            # 这里可以实现将通知保存到数据库的逻辑
            # 暂时只记录日志
            self.logger.info(f"通知已发送: {notification_data['title']} - {notification_data['scope']}")
        except Exception as e:
            self.logger.error(f"保存通知到数据库失败: {e}")
    
    def broadcast_user_activity(self, activity_data: Dict[str, Any]):
        """广播用户活动"""
        try:
            if self.socketio:
                self.socketio.emit('user_activity', activity_data)
        except Exception as e:
            self.logger.error(f"广播用户活动失败: {e}")

    def _validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证用户token"""
        try:
            if not token:
                return None

            user_service = self.services.get('user')
            if user_service:
                return user_service.validate_session(token)
            else:
                # 用户服务不可用时，返回临时用户信息
                return {
                    'user_id': 1,
                    'username': 'temp_user',
                    'permissions': ['read', 'download'],
                    'is_admin': True  # 临时给予管理员权限用于测试
                }

        except Exception as e:
            self.logger.error(f"验证token失败: {e}")
            return None

    def _check_permission(self, user_info: Dict[str, Any], permission: str) -> bool:
        """检查用户权限"""
        try:
            if not user_info:
                return False

            permissions = user_info.get('permissions', [])
            return permission in permissions or user_info.get('is_admin', False)

        except Exception as e:
            self.logger.error(f"检查权限失败: {e}")
            return False

    def _check_restart_required(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> bool:
        """检查配置变更是否需要重启服务"""
        try:
            # 需要重启的配置项
            restart_keys = [
                'server.host',
                'server.port',
                'server.frontend_port',
                'database.host',
                'database.port',
                'database.username',
                'database.password',
                'database.database'
            ]

            for key in restart_keys:
                old_value = self._get_nested_value(old_config, key)
                new_value = self._get_nested_value(new_config, key)
                if old_value != new_value:
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检查重启需求失败: {e}")
            return False

    def _get_nested_value(self, config: Dict[str, Any], key: str) -> Any:
        """获取嵌套配置值"""
        try:
            keys = key.split('.')
            value = config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return None
            return value
        except:
            return None
    
    def run(self):
        """运行服务器"""
        try:
            self.running = True
            self.start_time = time.time()
            
            host = self.settings.get('server.host', '0.0.0.0')
            port = self.settings.get('server.port', 8080)
            debug = self.settings.get('server.debug', False)
            
            self.logger.info(f"API服务器启动: {host}:{port}")
            
            self.socketio.run(
                self.app,
                host=host,
                port=port,
                debug=debug,
                use_reloader=False
            )
            
        except Exception as e:
            self.logger.error(f"API服务器运行失败: {e}")
            self.running = False
    
    def stop(self):
        """停止服务器"""
        try:
            self.running = False
            self.logger.info("API服务器已停止")
        except Exception as e:
            self.logger.error(f"停止API服务器失败: {e}")
    
    def get_app(self):
        """获取Flask应用实例"""
        return self.app
    
    def get_socketio(self):
        """获取SocketIO实例"""
        return self.socketio
