#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对话框工具类
提供通用的对话框功能，如居中显示等
"""

import tkinter as tk
from tkinter import ttk

class DialogUtils:
    """对话框工具类"""
    
    @staticmethod
    def center_window(window, width=None, height=None):
        """
        将窗口居中显示
        
        Args:
            window: tkinter窗口对象
            width: 窗口宽度（可选，如果不提供则使用当前宽度）
            height: 窗口高度（可选，如果不提供则使用当前高度）
        """
        try:
            # 更新窗口以获取准确的尺寸
            window.update_idletasks()
            
            # 获取窗口尺寸
            if width is None or height is None:
                # 如果没有提供尺寸，尝试从当前几何信息中获取
                geometry = window.geometry()
                if 'x' in geometry and '+' in geometry:
                    size_part = geometry.split('+')[0]
                    if 'x' in size_part:
                        w, h = size_part.split('x')
                        width = width or int(w)
                        height = height or int(h)
                
                # 如果仍然没有尺寸，使用默认值
                if width is None:
                    width = window.winfo_width() or 400
                if height is None:
                    height = window.winfo_height() or 300
            
            # 获取屏幕尺寸
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            
            # 计算居中位置
            x = (screen_width // 2) - (width // 2)
            y = (screen_height // 2) - (height // 2)
            
            # 确保窗口不会超出屏幕边界
            x = max(0, min(x, screen_width - width))
            y = max(0, min(y, screen_height - height))
            
            # 设置窗口位置
            window.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            print(f"警告：窗口居中失败: {e}")
            # 如果居中失败，至少确保窗口可见
            try:
                if width and height:
                    window.geometry(f"{width}x{height}")
            except:
                pass
    
    @staticmethod
    def create_centered_dialog(parent, title, width=400, height=300, resizable=True):
        """
        创建一个居中的对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            width: 对话框宽度
            height: 对话框高度
            resizable: 是否可调整大小
            
        Returns:
            创建的对话框窗口
        """
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.transient(parent)
        dialog.grab_set()
        
        # 设置是否可调整大小
        dialog.resizable(resizable, resizable)
        
        # 居中显示
        DialogUtils.center_window(dialog, width, height)
        
        return dialog
    
    @staticmethod
    def create_info_dialog(parent, title, message, width=400, height=200):
        """
        创建一个信息对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            message: 显示的消息
            width: 对话框宽度
            height: 对话框高度
            
        Returns:
            创建的对话框窗口
        """
        dialog = DialogUtils.create_centered_dialog(parent, title, width, height, False)
        
        # 创建主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 添加消息标签
        message_label = ttk.Label(main_frame, text=message, wraplength=width-40)
        message_label.pack(expand=True)
        
        # 添加确定按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="确定", command=dialog.destroy).pack()
        
        return dialog
    
    @staticmethod
    def create_confirm_dialog(parent, title, message, width=400, height=200):
        """
        创建一个确认对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            message: 显示的消息
            width: 对话框宽度
            height: 对话框高度
            
        Returns:
            (dialog, result_var) - 对话框窗口和结果变量
        """
        dialog = DialogUtils.create_centered_dialog(parent, title, width, height, False)
        result = tk.BooleanVar(value=False)
        
        # 创建主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 添加消息标签
        message_label = ttk.Label(main_frame, text=message, wraplength=width-40)
        message_label.pack(expand=True)
        
        # 添加按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        def on_yes():
            result.set(True)
            dialog.destroy()
        
        def on_no():
            result.set(False)
            dialog.destroy()
        
        ttk.Button(button_frame, text="确定", command=on_yes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=on_no).pack(side=tk.LEFT)
        
        return dialog, result

# 为了向后兼容，提供一个简单的函数
def center_window(window, width=None, height=None):
    """居中显示窗口的简单函数"""
    return DialogUtils.center_window(window, width, height)
