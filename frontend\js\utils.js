/**
 * 工具函数库
 * 提供常用的工具函数和帮助方法
 */

const Utils = {
    /**
     * 防抖函数
     */
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    },
    
    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 深拷贝
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * 生成唯一ID
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        return CONFIG.formatFileSize(bytes);
    },
    
    /**
     * 格式化日期
     */
    formatDate(date) {
        return CONFIG.formatDate(date);
    },

    /**
     * 格式化日期时间 - 使用中国时间
     */
    formatDateTime(dateString) {
        if (!dateString) return '未知时间';

        try {
            const chinaTime = CONFIG.toChinaTime(dateString);
            if (!chinaTime) return '无效时间';

            const now = CONFIG.toChinaTime(new Date());
            const diffMs = now - chinaTime;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                // 今天
                return chinaTime.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'Asia/Shanghai'
                });
            } else if (diffDays === 1) {
                // 昨天
                return '昨天 ' + chinaTime.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit',
                    timeZone: 'Asia/Shanghai'
                });
            } else if (diffDays < 7) {
                // 一周内
                return diffDays + '天前';
            } else {
                // 超过一周
                return chinaTime.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    timeZone: 'Asia/Shanghai'
                });
            }
        } catch (error) {
            CONFIG.log('error', 'Format datetime error:', error);
            return '时间格式错误';
        }
    },

    /**
     * 格式化为完整的中国时间字符串
     */
    formatChinaDateTime(dateString) {
        if (!dateString) return '未知时间';

        try {
            const chinaTime = CONFIG.toChinaTime(dateString);
            if (!chinaTime) return '无效时间';

            return chinaTime.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            });
        } catch (error) {
            CONFIG.log('error', 'Format China datetime error:', error);
            return '时间格式错误';
        }
    },

    /**
     * 下载Blob文件
     */
    downloadBlob(blob, filename) {
        try {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || 'download';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            CONFIG.log('error', 'Download blob error:', error);
            throw error;
        }
    },
    
    /**
     * 获取文件扩展名
     */
    getFileExtension(filename) {
        if (!filename) return '';
        const lastDot = filename.lastIndexOf('.');
        return lastDot !== -1 ? filename.substring(lastDot) : '';
    },
    
    /**
     * 获取文件名（不含扩展名）
     */
    getFileName(filename) {
        if (!filename) return '';
        const lastDot = filename.lastIndexOf('.');
        return lastDot !== -1 ? filename.substring(0, lastDot) : filename;
    },
    
    /**
     * 检查文件类型
     */
    isImageFile(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        return CONFIG.FILES.SUPPORTED_TYPES.IMAGE.includes(ext);
    },
    
    isVideoFile(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        return CONFIG.FILES.SUPPORTED_TYPES.VIDEO.includes(ext);
    },
    
    isAudioFile(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        return CONFIG.FILES.SUPPORTED_TYPES.AUDIO.includes(ext);
    },
    
    isDocumentFile(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        return CONFIG.FILES.SUPPORTED_TYPES.DOCUMENT.includes(ext);
    },
    
    /**
     * 本地存储操作
     */
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                CONFIG.log('error', 'Storage set error:', error);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                CONFIG.log('error', 'Storage get error:', error);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                CONFIG.log('error', 'Storage remove error:', error);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                CONFIG.log('error', 'Storage clear error:', error);
                return false;
            }
        }
    },
    
    /**
     * DOM操作工具
     */
    dom: {
        /**
         * 查询元素
         */
        $(selector, context = document) {
            return context.querySelector(selector);
        },
        
        /**
         * 查询所有元素
         */
        $$(selector, context = document) {
            return Array.from(context.querySelectorAll(selector));
        },
        
        /**
         * 创建元素
         */
        create(tag, attributes = {}, children = []) {
            const element = document.createElement(tag);
            
            // 设置属性
            for (const [key, value] of Object.entries(attributes)) {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'innerHTML') {
                    element.innerHTML = value;
                } else if (key === 'textContent') {
                    element.textContent = value;
                } else if (key.startsWith('data-')) {
                    element.setAttribute(key, value);
                } else {
                    element[key] = value;
                }
            }
            
            // 添加子元素
            children.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child instanceof Node) {
                    element.appendChild(child);
                }
            });
            
            return element;
        },
        
        /**
         * 添加类名
         */
        addClass(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },
        
        /**
         * 移除类名
         */
        removeClass(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },
        
        /**
         * 切换类名
         */
        toggleClass(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        },
        
        /**
         * 检查是否有类名
         */
        hasClass(element, className) {
            return element && className && element.classList.contains(className);
        },
        
        /**
         * 显示元素
         */
        show(element) {
            if (element) {
                element.style.display = '';
                this.removeClass(element, 'hidden');
            }
        },
        
        /**
         * 隐藏元素
         */
        hide(element) {
            if (element) {
                this.addClass(element, 'hidden');
            }
        },
        
        /**
         * 切换显示/隐藏
         */
        toggle(element) {
            if (element) {
                this.toggleClass(element, 'hidden');
            }
        }
    },
    
    /**
     * 事件工具
     */
    event: {
        /**
         * 添加事件监听器
         */
        on(element, event, handler, options = false) {
            if (element && event && handler) {
                element.addEventListener(event, handler, options);
            }
        },
        
        /**
         * 移除事件监听器
         */
        off(element, event, handler, options = false) {
            if (element && event && handler) {
                element.removeEventListener(event, handler, options);
            }
        },
        
        /**
         * 触发自定义事件
         */
        trigger(element, eventName, detail = null) {
            if (element && eventName) {
                const event = new CustomEvent(eventName, { detail });
                element.dispatchEvent(event);
            }
        },
        
        /**
         * 阻止默认行为
         */
        preventDefault(event) {
            if (event && event.preventDefault) {
                event.preventDefault();
            }
        },
        
        /**
         * 阻止事件冒泡
         */
        stopPropagation(event) {
            if (event && event.stopPropagation) {
                event.stopPropagation();
            }
        }
    },
    
    /**
     * URL工具
     */
    url: {
        /**
         * 获取查询参数
         */
        getParams(url = window.location.href) {
            const params = new URLSearchParams(new URL(url).search);
            const result = {};
            for (const [key, value] of params) {
                result[key] = value;
            }
            return result;
        },
        
        /**
         * 设置查询参数
         */
        setParams(params, url = window.location.href) {
            const urlObj = new URL(url);
            for (const [key, value] of Object.entries(params)) {
                if (value === null || value === undefined) {
                    urlObj.searchParams.delete(key);
                } else {
                    urlObj.searchParams.set(key, value);
                }
            }
            return urlObj.toString();
        },
        
        /**
         * 下载文件
         */
        downloadFile(url, filename) {
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || '';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    },
    
    /**
     * 验证工具
     */
    validate: {
        /**
         * 验证邮箱
         */
        email(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        /**
         * 验证文件大小
         */
        fileSize(size, maxSize = CONFIG.FILES.MAX_SIZE) {
            return size <= maxSize;
        },
        
        /**
         * 验证文件类型
         */
        fileType(filename, allowedTypes = null) {
            if (!allowedTypes) return true;
            
            const ext = Utils.getFileExtension(filename).toLowerCase();
            return allowedTypes.includes(ext);
        }
    },
    
    /**
     * 异步工具
     */
    async: {
        /**
         * 延迟执行
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        
        /**
         * 重试执行
         */
        async retry(fn, maxAttempts = 3, delay = 1000) {
            let lastError;
            
            for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                try {
                    return await fn();
                } catch (error) {
                    lastError = error;
                    if (attempt < maxAttempts) {
                        await this.delay(delay);
                    }
                }
            }
            
            throw lastError;
        }
    },
    
    /**
     * 性能监控
     */
    performance: {
        /**
         * 测量执行时间
         */
        measure(name, fn) {
            const start = performance.now();
            const result = fn();
            const end = performance.now();
            
            if (CONFIG.DEBUG.SHOW_PERFORMANCE) {
                CONFIG.log('info', `Performance [${name}]: ${(end - start).toFixed(2)}ms`);
            }
            
            return result;
        },
        
        /**
         * 异步测量执行时间
         */
        async measureAsync(name, fn) {
            const start = performance.now();
            const result = await fn();
            const end = performance.now();
            
            if (CONFIG.DEBUG.SHOW_PERFORMANCE) {
                CONFIG.log('info', `Performance [${name}]: ${(end - start).toFixed(2)}ms`);
            }
            
            return result;
        }
    }
};

// 全局可用
window.Utils = Utils;
