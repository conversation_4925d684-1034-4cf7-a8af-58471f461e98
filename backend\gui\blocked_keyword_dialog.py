#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏蔽关键词对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox

class BlockedKeywordDialog:
    """屏蔽关键词对话框"""
    
    def __init__(self, parent, title, initial_values=None):
        print(f"🔧 初始化BlockedKeywordDialog: {title}")
        self.parent = parent
        self.result = None

        try:
            # 创建对话框窗口
            print("📋 创建对话框窗口...")
            self.dialog = tk.Toplevel(parent)
            self.dialog.title(title)
            self.dialog.geometry("500x400")
            self.dialog.transient(parent)
            self.dialog.grab_set()

            # 居中显示
            self.dialog.update_idletasks()
            x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
            y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
            self.dialog.geometry(f"500x400+{x}+{y}")

            print("📋 创建对话框组件...")
            self.create_widgets(initial_values)

            print("⏳ 等待对话框关闭...")
            # 等待对话框关闭
            self.dialog.wait_window()
            print(f"✅ 对话框已关闭，结果: {self.result}")

        except Exception as e:
            print(f"❌ BlockedKeywordDialog初始化异常: {e}")
            import traceback
            traceback.print_exc()
            self.result = None
    
    def create_widgets(self, initial_values):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 关键词输入
        ttk.Label(main_frame, text="关键词:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.keyword_var = tk.StringVar()
        keyword_entry = ttk.Entry(main_frame, textvariable=self.keyword_var, width=40)
        keyword_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=(0, 5))
        
        # 匹配类型
        ttk.Label(main_frame, text="匹配类型:", font=("Arial", 10, "bold")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.keyword_type_var = tk.StringVar(value="partial")
        type_frame = ttk.Frame(main_frame)
        type_frame.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))
        
        ttk.Radiobutton(type_frame, text="精确匹配", variable=self.keyword_type_var, 
                       value="exact").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="部分匹配", variable=self.keyword_type_var, 
                       value="partial").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(type_frame, text="正则表达式", variable=self.keyword_type_var, 
                       value="regex").pack(side=tk.LEFT)
        
        # 严重程度
        ttk.Label(main_frame, text="严重程度:", font=("Arial", 10, "bold")).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.severity_var = tk.StringVar(value="中等")
        severity_combo = ttk.Combobox(main_frame, textvariable=self.severity_var,
                                     values=["低", "中等", "高", "严重"],
                                     state="readonly", width=15)
        severity_combo.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        
        # 选项
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding=10)
        options_frame.grid(row=3, column=0, columnspan=2, sticky=tk.W+tk.E, pady=(10, 0))
        
        self.is_active_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="启用", variable=self.is_active_var).pack(anchor=tk.W)
        
        self.is_regex_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="正则表达式", variable=self.is_regex_var).pack(anchor=tk.W)
        
        self.block_search_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="屏蔽搜索", variable=self.block_search_var).pack(anchor=tk.W)
        
        self.block_download_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="屏蔽下载", variable=self.block_download_var).pack(anchor=tk.W)
        
        # 描述
        ttk.Label(main_frame, text="描述:", font=("Arial", 10, "bold")).grid(
            row=4, column=0, sticky=tk.NW, pady=(10, 5))
        self.description_text = tk.Text(main_frame, height=4, width=40)
        self.description_text.grid(row=4, column=1, sticky=tk.W+tk.E, pady=(10, 5))
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT)
        
        # 配置列权重
        main_frame.columnconfigure(1, weight=1)
        
        # 如果有初始值，填充表单
        if initial_values:
            self.load_initial_values(initial_values)
        
        # 设置焦点
        keyword_entry.focus()
    
    def load_initial_values(self, values):
        """加载初始值"""
        try:
            if len(values) >= 7:
                # 英文到中文的映射
                severity_map = {
                    "low": "低",
                    "medium": "中等",
                    "high": "高",
                    "critical": "严重"
                }

                self.keyword_var.set(values[1])  # 关键词
                self.keyword_type_var.set(values[2])  # 类型
                self.is_regex_var.set(values[3] == '是')  # 正则
                self.severity_var.set(severity_map.get(values[4], "中等"))  # 严重程度
                self.is_active_var.set(values[5] == '是')  # 启用
        except Exception as e:
            print(f"加载初始值失败: {e}")
    
    def on_ok(self):
        """确定按钮事件"""
        print("🔧 用户点击了确定按钮")
        keyword = self.keyword_var.get().strip()
        print(f"📋 输入的关键词: '{keyword}'")

        if not keyword:
            print("❌ 关键词为空")
            messagebox.showerror("错误", "请输入关键词")
            return

        # 验证正则表达式
        if self.is_regex_var.get():
            try:
                import re
                re.compile(keyword)
                print("✅ 正则表达式验证通过")
            except re.error as e:
                print(f"❌ 正则表达式无效: {e}")
                messagebox.showerror("错误", f"正则表达式无效: {e}")
                return

        # 中文到英文的映射
        severity_map = {
            "低": "low",
            "中等": "medium",
            "高": "high",
            "严重": "critical"
        }

        # 收集结果
        self.result = {
            'keyword': keyword,
            'keyword_type': self.keyword_type_var.get(),
            'is_regex': self.is_regex_var.get(),
            'severity': severity_map.get(self.severity_var.get(), "medium"),
            'is_active': self.is_active_var.get(),
            'block_search': self.block_search_var.get(),
            'block_download': self.block_download_var.get(),
            'description': self.description_text.get("1.0", tk.END).strip()
        }

        print(f"✅ 准备返回结果: {self.result}")
        self.dialog.destroy()
    
    def on_cancel(self):
        """取消按钮事件"""
        print("❌ 用户点击了取消按钮")
        self.result = None
        self.dialog.destroy()

class BatchImportDialog:
    """批量导入屏蔽关键词对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("批量导入屏蔽关键词")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
        
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 说明
        info_text = """
批量导入屏蔽关键词

格式说明：
- 每行一个关键词
- 支持格式：关键词,类型,严重程度,描述
- 类型：exact(精确)/partial(部分)/regex(正则)
- 严重程度：low/medium/high/critical
- 示例：
  机密文件,partial,high,包含机密信息的文件
  密码,exact,critical,密码相关内容
  .*财务.*,regex,high,财务相关正则匹配
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 文本输入区域
        ttk.Label(main_frame, text="关键词列表:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        self.keywords_text = tk.Text(text_frame, height=15)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.keywords_text.yview)
        self.keywords_text.configure(yscrollcommand=scrollbar.set)
        
        self.keywords_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(10, 0))
        
        ttk.Button(button_frame, text="导入", command=self.on_import).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT)
        
        # 设置焦点
        self.keywords_text.focus()
    
    def on_import(self):
        """导入按钮事件"""
        content = self.keywords_text.get("1.0", tk.END).strip()
        if not content:
            messagebox.showerror("错误", "请输入关键词列表")
            return
        
        # 解析关键词
        keywords = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            try:
                parts = [p.strip() for p in line.split(',')]
                if len(parts) >= 1:
                    keyword_data = {
                        'keyword': parts[0],
                        'keyword_type': parts[1] if len(parts) > 1 else 'partial',
                        'severity': parts[2] if len(parts) > 2 else 'medium',
                        'description': parts[3] if len(parts) > 3 else '',
                        'is_active': True,
                        'is_regex': parts[1] == 'regex' if len(parts) > 1 else False,
                        'block_search': True,
                        'block_download': False
                    }
                    keywords.append(keyword_data)
            except Exception as e:
                messagebox.showerror("错误", f"第{i}行格式错误: {e}")
                return
        
        if not keywords:
            messagebox.showerror("错误", "没有有效的关键词")
            return
        
        self.result = keywords
        self.dialog.destroy()
    
    def on_cancel(self):
        """取消按钮事件"""
        self.result = None
        self.dialog.destroy()
