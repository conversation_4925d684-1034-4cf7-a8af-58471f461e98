#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文路径图像读取
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path

def test_opencv_chinese_path():
    """测试OpenCV读取中文路径图像"""
    print("🔍 测试OpenCV读取中文路径图像...")
    
    # 测试文件夹路径
    test_folder = Path("C:/321/2")
    
    if not test_folder.exists():
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return False
    
    # 查找图像文件
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']:
        image_files.extend(test_folder.glob(f"*{ext}"))
        image_files.extend(test_folder.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"❌ 在 {test_folder} 中未找到图像文件")
        return False
    
    print(f"📁 找到 {len(image_files)} 个图像文件")
    
    success_count = 0
    fail_count = 0
    
    for image_file in image_files:
        print(f"\n📄 测试文件: {image_file.name}")
        print(f"   完整路径: {image_file}")
        
        # 方法1：直接读取
        image1 = None
        try:
            image1 = cv2.imread(str(image_file))
            if image1 is not None:
                print("   ✅ 方法1（直接读取）成功")
            else:
                print("   ❌ 方法1（直接读取）失败")
        except Exception as e:
            print(f"   ❌ 方法1（直接读取）异常: {e}")
        
        # 方法2：numpy处理中文路径
        image2 = None
        try:
            image_data = np.fromfile(str(image_file), dtype=np.uint8)
            image2 = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            if image2 is not None:
                print("   ✅ 方法2（numpy解码）成功")
            else:
                print("   ❌ 方法2（numpy解码）失败")
        except Exception as e:
            print(f"   ❌ 方法2（numpy解码）异常: {e}")
        
        # 方法3：PIL备选
        image3 = None
        try:
            from PIL import Image as PILImage
            pil_image = PILImage.open(str(image_file))
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            image3 = np.array(pil_image)
            image3 = cv2.cvtColor(image3, cv2.COLOR_RGB2BGR)
            if image3 is not None:
                print("   ✅ 方法3（PIL转换）成功")
            else:
                print("   ❌ 方法3（PIL转换）失败")
        except Exception as e:
            print(f"   ❌ 方法3（PIL转换）异常: {e}")
        
        # 统计结果
        if image1 is not None or image2 is not None or image3 is not None:
            success_count += 1
            print("   🎉 至少一种方法成功读取图像")
        else:
            fail_count += 1
            print("   💥 所有方法都失败")
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count} 个文件")
    print(f"   失败: {fail_count} 个文件")
    print(f"   成功率: {success_count/(success_count+fail_count)*100:.1f}%")
    
    return success_count > 0

def test_file_encoding():
    """测试文件名编码"""
    print("\n🔤 测试文件名编码...")
    
    test_folder = Path("C:/321/2")
    if not test_folder.exists():
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return
    
    for file_path in test_folder.iterdir():
        if file_path.is_file():
            print(f"📄 文件名: {file_path.name}")
            print(f"   UTF-8编码: {file_path.name.encode('utf-8')}")
            print(f"   GBK编码: {file_path.name.encode('gbk', errors='ignore')}")
            print(f"   完整路径: {file_path}")
            print()

if __name__ == "__main__":
    print("🚀 开始测试中文路径图像处理...")
    
    # 测试文件名编码
    test_file_encoding()
    
    # 测试OpenCV读取
    success = test_opencv_chinese_path()
    
    if success:
        print("\n✅ 测试完成，至少部分文件可以正常读取")
    else:
        print("\n❌ 测试失败，无法读取任何图像文件")
