<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="version" content="1751190000">
    <title>企业级文件共享系统 v1751190000</title>
    <link rel="stylesheet" href="css/style.css?v=1733857627">
    <link rel="stylesheet" href="css/components.css?v=1733857627">
    <link rel="stylesheet" href="css/animations.css?v=1733857627">
    <link rel="stylesheet" href="css/download-records.css?v=1733857627">
    <link rel="stylesheet" href="css/simple-downloads.css?v=1734013628">
    <link rel="stylesheet" href="css/marquee.css?v=1733857627">
    <link rel="stylesheet" href="css/enhanced-notifications.css?v=1735819200">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载...</p>
        </div>
    </div>

    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-cloud"></i>
                <span>文件共享系统</span>
            </div>
            
            <div class="nav-search">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="搜索图片文件...">
                    <div class="search-type-indicator">
                        <i class="fas fa-image"></i>
                    </div>
                    <!-- 以图搜图按钮 -->
                    <button class="image-search-btn" id="image-search-btn" title="以图搜图">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
                <!-- 图片上传区域 -->
                <div class="image-upload-area" id="image-upload-area" style="display: none;">
                    <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
                    <div class="upload-zone" id="upload-zone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <p class="upload-main-text">拖拽图片到此处或点击上传</p>
                            <p class="upload-sub-text">支持 JPG、PNG、GIF、BMP、TIFF、WEBP 格式，最大 10MB</p>
                        </div>
                    </div>
                    <!-- 上传进度显示 -->
                    <div class="upload-progress" id="upload-progress" style="display: none;">
                        <div class="progress-info">
                            <div class="progress-icon">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <div class="progress-text">
                                <p class="progress-main-text" id="progress-main-text">正在上传图片...</p>
                                <p class="progress-sub-text" id="progress-sub-text">请稍候，正在处理您的图片</p>
                            </div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-percentage" id="progress-percentage">0%</div>
                    </div>
                    <div class="upload-actions">
                        <button class="btn-secondary" id="cancel-upload-btn">取消</button>
                    </div>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn" id="upload-btn">
                    <i class="fas fa-upload"></i>
                    <span>上传</span>
                </button>
                <button class="nav-btn" id="notifications-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="user-menu-btn">
                        <i class="fas fa-user"></i>
                    </button>
                    <div class="user-dropdown" id="user-dropdown">
                        <div class="user-info">
                            <div class="user-name" id="current-username">加载中...</div>
                            <div class="user-role" id="current-user-role">正在获取用户信息</div>
                        </div>
                        <hr>
                        <a href="#" class="dropdown-item" id="user-settings">
                            <i class="fas fa-cog"></i> 设置
                        </a>
                        <a href="#" class="dropdown-item" id="user-help">
                            <i class="fas fa-question-circle"></i> 帮助
                        </a>
                        <a href="#" class="dropdown-item" id="user-logout">
                            <i class="fas fa-sign-out-alt"></i> 退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 滚动字幕条 -->
    <div class="marquee-container" id="marquee-container">
        <div class="marquee-content" id="marquee-content">
            <span class="marquee-text" id="marquee-text">欢迎使用企业级文件共享系统！</span>
        </div>
        <button class="marquee-close" id="marquee-close" title="关闭字幕">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <h3>快速访问</h3>
                <ul class="sidebar-menu">
                    <li class="menu-item active">
                        <a href="#" data-view="home">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" data-view="downloads">
                            <i class="fas fa-download"></i>
                            <span>下载记录</span>
                        </a>
                    </li>
                </ul>
            </div>
            

            



        </aside>

        <!-- 内容区域 -->
        <section class="content-area">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <nav class="breadcrumb-nav">
                    <a href="#" class="breadcrumb-item">
                        <i class="fas fa-home"></i>
                        首页
                    </a>
                </nav>
                
                <div class="view-controls">
                    <div class="layout-toggle">
                        <button class="layout-btn active" data-layout="grid" title="网格布局">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="layout-btn" data-layout="horizontal" title="横向布局">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>

                    <div class="view-toggle">
                        <button class="view-btn" data-view="extra-large-icons" title="超大图标">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="view-btn active" data-view="large-icons" title="大图标">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="medium-icons" title="中等图标">
                            <i class="fas fa-grip-horizontal"></i>
                        </button>
                        <button class="view-btn" data-view="small-icons" title="小图标">
                            <i class="fas fa-grip-vertical"></i>
                        </button>
                    </div>

                    <div class="management-controls">
                        <button class="management-btn" id="thumbnail-management-btn" title="缩略图管理">
                            <i class="fas fa-images"></i>
                        </button>
                    </div>
                    
                    <div class="sort-controls">
                        <select id="sort-select">
                            <option value="name">按名称排序</option>
                            <option value="date">按日期排序</option>
                            <option value="size">按大小排序</option>
                            <option value="type">按类型排序</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 文件网格视图 -->
            <div class="file-grid" id="file-grid">
                <!-- 动态加载文件 -->
            </div>

            <!-- 文件列表视图 -->
            <div class="file-list hidden" id="file-list">
                <table class="file-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>大小</th>
                            <th>类型</th>
                            <th>修改时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="file-table-body">
                        <!-- 动态加载文件 -->
                    </tbody>
                </table>
            </div>

            <!-- 下载记录视图 -->
            <div class="download-records-view hidden" id="download-records-view">
                <!-- 页面内容将由 JavaScript 动态生成 -->
            </div>
        </section>
    </main>

    <!-- 上传模态框 -->
    <div class="modal" id="upload-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>上传文件</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>拖拽文件到此处或点击选择文件</p>
                    <input type="file" id="file-input" multiple hidden>
                    <button class="btn btn-primary" id="select-files-btn">选择文件</button>
                </div>
                <div class="upload-progress" id="upload-progress">
                    <!-- 上传进度 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通知面板 -->
    <div class="notification-panel" id="notification-panel">
        <div class="notification-header">
            <h3>系统通知</h3>
            <button class="close-panel">&times;</button>
        </div>
        <div class="notification-list" id="notification-list">
            <!-- 动态加载通知 -->
        </div>
    </div>

    <!-- 文件预览模态框 -->
    <div class="modal" id="preview-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="preview-title">文件预览</h3>
                <button class="modal-close" onclick="Components.Modal.hide('preview-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="preview-container" id="preview-container">
                    <!-- 预览内容将在这里动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" id="context-menu">
        <ul>
            <li><i class="fas fa-download"></i> 下载</li>
            <li><i class="fas fa-eye"></i> 预览</li>
            <li><i class="fas fa-share"></i> 分享</li>
            <li class="divider"></li>
            <li><i class="fas fa-info-circle"></i> 详细信息</li>
        </ul>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container" id="toast-container">
        <!-- 动态添加 toast -->
    </div>

    <!-- 图片预览浮窗 -->
    <div class="image-preview-overlay" id="image-preview-overlay"></div>
    <div class="image-preview-float" id="image-preview-float">
        <div class="float-header" id="float-header">
            <h3 class="float-title" id="float-title">图片预览</h3>
            <div class="float-controls">
                <button class="float-btn" id="float-minimize" title="最小化">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="float-btn" id="float-maximize" title="最大化">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="float-btn close" id="float-close" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="float-content" id="float-content">
            <img class="float-image" id="float-image" alt="预览图片">
        </div>
        <div class="float-toolbar">
            <div class="toolbar-group">
                <button class="toolbar-btn" id="zoom-out" title="缩小">
                    <i class="fas fa-search-minus"></i>
                </button>
                <span class="zoom-info" id="zoom-info">100%</span>
                <button class="toolbar-btn" id="zoom-in" title="放大">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="toolbar-btn" id="zoom-fit" title="适应窗口">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <button class="toolbar-btn" id="zoom-actual" title="实际大小">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <div class="toolbar-group">
                <button class="toolbar-btn" id="rotate-left" title="向左旋转">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="toolbar-btn" id="rotate-right" title="向右旋转">
                    <i class="fas fa-redo"></i>
                </button>
            </div>
            <div class="toolbar-group">
                <!-- 下载按钮已移除 -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="js/config.js?v=1751190000"></script>
    <script src="js/api.js?v=1751190000"></script>
    <script src="js/utils.js?v=1751190000"></script>
    <script src="js/components.js?v=1751190000"></script>
    <script src="js/file-manager.js?v=1751190000"></script>
    <script src="js/upload.js?v=1751190000"></script>
    <script src="js/search.js?v=1751190000"></script>
    <script src="js/notifications.js?v=1751190000"></script>
    <script src="js/marquee.js?v=1751190000"></script>
    <script src="js/simple-downloads.js?v=1751190000"></script>
    <script src="js/app.js?v=1751190000"></script>


</body>
</html>
