<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>清除缓存</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 50px; text-align: center; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .step { margin: 20px 0; padding: 20px; background: #e3f2fd; border-radius: 8px; text-align: left; }
        .step h3 { margin-top: 0; color: #1976d2; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        button { padding: 15px 30px; margin: 10px; background: #2196f3; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #1976d2; }
        .danger { background: #f44336; }
        .danger:hover { background: #d32f2f; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 清除浏览器缓存</h1>
        <p>如果文件下载功能不正常，请按照以下步骤清除缓存：</p>
        
        <div class="step warning">
            <h3>⚠️ 重要提示</h3>
            <p>浏览器仍在使用旧版本的JavaScript文件。需要强制清除缓存才能加载修复后的代码。</p>
        </div>

        <div class="step">
            <h3>方法 1: 使用快捷键（推荐）</h3>
            <p><strong>按住 Ctrl+Shift+Delete</strong>，在弹出的对话框中：</p>
            <ul>
                <li>选择时间范围：<strong>全部时间</strong></li>
                <li>勾选：<strong>缓存的图片和文件</strong></li>
                <li>勾选：<strong>Cookie和其他网站数据</strong></li>
                <li>点击 <strong>清除数据</strong></li>
            </ul>
        </div>

        <div class="step">
            <h3>方法 2: 强制刷新</h3>
            <p><strong>按住 Ctrl+F5</strong> 或 <strong>Ctrl+Shift+R</strong> 强制刷新页面</p>
        </div>

        <div class="step">
            <h3>方法 3: 开发者工具清除</h3>
            <p>1. 按 <strong>F12</strong> 打开开发者工具</p>
            <p>2. 右键点击刷新按钮，选择 <strong>清空缓存并硬性重新加载</strong></p>
        </div>

        <div class="step">
            <h3>方法 4: 无痕模式</h3>
            <p>按 <strong>Ctrl+Shift+N</strong> 打开无痕窗口，在无痕模式下访问系统</p>
        </div>

        <button onclick="clearLocalStorage()">🗑️ 清除本地存储</button>
        <button onclick="forceReload()" class="danger">🔄 强制重新加载</button>
        <button onclick="openNewWindow()">🆕 新窗口访问</button>

        <div class="step success" id="status" style="display: none;">
            <h3>✅ 操作完成</h3>
            <p id="status-message"></p>
        </div>
    </div>

    <script>
        function clearLocalStorage() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('本地存储已清除！请重新登录。');
            } catch (e) {
                showStatus('清除本地存储失败：' + e.message);
            }
        }

        function forceReload() {
            // 添加随机参数强制重新加载
            const url = window.location.href.split('?')[0] + '?t=' + Date.now();
            window.location.href = url;
        }

        function openNewWindow() {
            const newUrl = window.location.origin + '/index.html?v=' + Date.now();
            window.open(newUrl, '_blank');
            showStatus('已在新窗口打开系统，请在新窗口中测试下载功能。');
        }

        function showStatus(message) {
            const status = document.getElementById('status');
            const statusMessage = document.getElementById('status-message');
            statusMessage.textContent = message;
            status.style.display = 'block';
        }

        // 页面加载时检查缓存状态
        window.onload = function() {
            // 检查当前页面是否是强制刷新的
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('t')) {
                showStatus('页面已强制刷新！缓存应该已经清除。');
            }
        };
    </script>
</body>
</html> 