#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为监控服务
"""

import re
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_, text

from models.search_record import SearchRecord, SensitiveFile, BlockedKeyword
from models.user_quota import UserQuota, UserBan
from models.online_user import OnlineUser, UserSession, UserRanking
from models.download_record import DownloadRecord
from models.activity_log import ActivityLog
from models.user import User
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.time_utils import TimeUtils

class UserBehaviorService:
    """用户行为监控服务"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)

    def _safe_format_datetime(self, dt_value, format_str='%Y-%m-%d %H:%M:%S'):
        """安全地格式化日期时间字段为中国时间"""
        return TimeUtils.format_china_time(dt_value, format_str)
    
    # ==================== 搜索行为监控 ====================
    
    def record_search(self, user_id: Optional[int], search_query: str,
                     results_count: int = 0, **kwargs) -> Dict[str, Any]:
        """记录搜索行为"""
        try:
            with self.db_manager.get_session() as session:
                # 检查屏蔽关键词
                is_blocked, block_reason = self.check_blocked_keywords(search_query)

                # 如果被屏蔽，记录并返回错误
                if is_blocked:
                    self.logger.warning(f"搜索被屏蔽: 用户={user_id}, 查询='{search_query}', 原因={block_reason}")

                    # 记录屏蔽的搜索
                    search_record = SearchRecord(
                        user_id=user_id,
                        search_query=search_query,
                        search_type=kwargs.get('search_type', 'text'),
                        results_count=0,  # 屏蔽的搜索结果数为0
                        is_sensitive=True,
                        is_blocked=True,
                        block_reason=block_reason,
                        ip_address=kwargs.get('ip_address'),
                        user_agent=kwargs.get('user_agent'),
                        session_id=kwargs.get('session_id')
                    )
                    session.add(search_record)
                    session.commit()

                    return {
                        'success': False,
                        'blocked': True,
                        'reason': block_reason,
                        'search_id': search_record.id
                    }

                # 检查敏感内容
                is_sensitive, _ = self.check_sensitive_content(search_query)

                # 记录正常搜索

                # 创建搜索记录
                search_record = SearchRecord(
                    search_query=search_query,
                    user_id=user_id,
                    results_count=results_count,
                    is_sensitive=is_sensitive,
                    is_blocked=is_blocked,
                    block_reason=block_reason,
                    search_type=kwargs.get('search_type', 'text'),
                    ip_address=kwargs.get('ip_address'),
                    user_agent=kwargs.get('user_agent'),
                    session_id=kwargs.get('session_id')
                )

                session.add(search_record)
                session.commit()

                self.logger.info(f"搜索记录保存成功: ID={search_record.id}, 用户={user_id}, 查询='{search_query}'")

                return {
                    'success': True,
                    'record_id': search_record.id,
                    'is_blocked': is_blocked,
                    'block_reason': block_reason,
                    'is_sensitive': is_sensitive
                }

        except Exception as e:
            self.logger.error(f"记录搜索行为失败: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def check_sensitive_content(self, content: str) -> Tuple[bool, Dict[str, Any]]:
        """检查敏感内容"""
        try:
            with self.db_manager.get_session() as session:
                # 安全地查询敏感文件，检查字段是否存在
                try:
                    # 先检查keywords字段是否存在
                    sensitive_files = session.query(SensitiveFile).filter(
                        SensitiveFile.keywords.isnot(None)
                    ).all()
                except Exception:
                    # 如果keywords字段不存在，使用description字段
                    sensitive_files = session.query(SensitiveFile).filter(
                        SensitiveFile.description.isnot(None)
                    ).all()

                sensitivity_info = {
                    'level': 'none',
                    'matched_keywords': [],
                    'files': []
                }

                for sensitive_file in sensitive_files:
                    # 安全地获取关键词
                    keywords = []
                    if hasattr(sensitive_file, 'keywords') and sensitive_file.keywords:
                        if isinstance(sensitive_file.keywords, list):
                            keywords = sensitive_file.keywords
                        elif isinstance(sensitive_file.keywords, str):
                            keywords = [sensitive_file.keywords]
                    elif hasattr(sensitive_file, 'description') and sensitive_file.description:
                        # 如果没有keywords字段，使用description作为关键词
                        keywords = [sensitive_file.description]

                    for keyword in keywords:
                        if keyword and keyword.lower() in content.lower():
                            sensitivity_info['matched_keywords'].append(keyword)
                            sensitivity_info['files'].append({
                                'file_id': sensitive_file.file_id,
                                'folder_id': sensitive_file.folder_id,
                                'level': sensitive_file.sensitivity_level
                            })

                            # 更新最高敏感级别
                            levels = ['low', 'medium', 'high', 'critical']
                            current_level_index = levels.index(sensitivity_info['level']) if sensitivity_info['level'] in levels else -1
                            file_level_index = levels.index(sensitive_file.sensitivity_level)

                            if file_level_index > current_level_index:
                                sensitivity_info['level'] = sensitive_file.sensitivity_level
                
                return len(sensitivity_info['matched_keywords']) > 0, sensitivity_info
                
        except Exception as e:
            self.logger.error(f"检查敏感内容失败: {e}")
            return False, {}
    
    def check_blocked_keywords(self, content: str) -> Tuple[bool, Optional[str]]:
        """检查屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                blocked_keywords = session.query(BlockedKeyword).filter(
                    BlockedKeyword.is_active == True,
                    BlockedKeyword.block_search == True
                ).all()
                
                for blocked_keyword in blocked_keywords:
                    keyword = blocked_keyword.keyword
                    keyword_type = blocked_keyword.keyword_type
                    
                    is_matched = False
                    
                    if keyword_type == 'exact':
                        is_matched = keyword.lower() == content.lower()
                    elif keyword_type == 'partial':
                        is_matched = keyword.lower() in content.lower()
                    elif keyword_type == 'regex':
                        try:
                            is_matched = bool(re.search(keyword, content, re.IGNORECASE))
                        except re.error:
                            self.logger.warning(f"无效的正则表达式: {keyword}")
                            continue
                    
                    if is_matched:
                        # 更新命中次数
                        blocked_keyword.increment_hit()
                        session.commit()
                        
                        return True, f"包含屏蔽关键词: {keyword}"
                
                return False, None
                
        except Exception as e:
            self.logger.error(f"检查屏蔽关键词失败: {e}")
            return False, None
    
    # ==================== 下载行为监控 ====================
    
    def record_download(self, user_id: Optional[int], download_info: Dict[str, Any]) -> Dict[str, Any]:
        """记录下载行为"""
        try:
            with self.db_manager.get_session() as session:
                # 更新用户配额
                if user_id:
                    quota_result = self.update_user_quota_download(
                        session, user_id, download_info.get('file_size', 0)
                    )
                    if not quota_result['success']:
                        return quota_result
                
                # 更新在线用户统计
                if download_info.get('session_id'):
                    self.update_online_user_download(session, download_info['session_id'])
                
                # 检查是否是敏感文件下载
                if download_info.get('file_id'):
                    is_sensitive = self.check_sensitive_file_download(session, download_info['file_id'])
                    if is_sensitive:
                        self.log_sensitive_activity(session, user_id, 'download', {
                            'file_id': download_info['file_id'],
                            'file_name': download_info.get('file_name'),
                            'file_size': download_info.get('file_size')
                        }, download_info.get('ip_address'))
                
                session.commit()
                
                return {'success': True}
                
        except Exception as e:
            self.logger.error(f"记录下载行为失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def check_sensitive_file_download(self, session: Session, file_id: int) -> bool:
        """检查敏感文件下载"""
        try:
            sensitive_file = session.query(SensitiveFile).filter(
                SensitiveFile.file_id == file_id
            ).first()
            
            return sensitive_file is not None
            
        except Exception as e:
            self.logger.error(f"检查敏感文件下载失败: {e}")
            return False
    
    # ==================== 配额管理 ====================
    
    def update_user_quota_search(self, session: Session, user_id: int):
        """更新用户搜索配额"""
        quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
        if not quota:
            quota = UserQuota(user_id=user_id)
            session.add(quota)
        
        # 检查是否需要重置每日配额
        if quota.last_reset.date() < date.today():
            quota.reset_daily_quota()
        
        quota.add_search()
    
    def update_user_quota_download(self, session: Session, user_id: int, file_size: int) -> Dict[str, Any]:
        """更新用户下载配额"""
        quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
        if not quota:
            quota = UserQuota(user_id=user_id)
            session.add(quota)
        
        # 检查是否需要重置每日配额
        if quota.last_reset.date() < date.today():
            quota.reset_daily_quota()
        
        # 检查是否可以下载
        can_download, reason = quota.can_download(file_size)
        if not can_download:
            return {'success': False, 'error': reason}
        
        quota.add_download(file_size)
        return {'success': True}
    
    def check_user_quota(self, user_id: int, action: str, **kwargs) -> Dict[str, Any]:
        """检查用户配额"""
        try:
            with self.db_manager.get_session() as session:
                quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
                if not quota:
                    return {'success': True, 'message': '无配额限制'}
                
                # 检查是否需要重置每日配额
                if quota.last_reset.date() < date.today():
                    quota.reset_daily_quota()
                    session.commit()
                
                if action == 'search':
                    can_action, reason = quota.can_search()
                elif action == 'download':
                    file_size = kwargs.get('file_size', 0)
                    can_action, reason = quota.can_download(file_size)
                else:
                    return {'success': True, 'message': '未知操作类型'}
                
                if not can_action:
                    return {'success': False, 'error': reason}
                
                return {'success': True, 'quota': quota.to_dict()}
                
        except Exception as e:
            self.logger.error(f"检查用户配额失败: {e}")
            return {'success': False, 'error': str(e)}
    
    # ==================== 在线用户管理 ====================

    def create_or_update_online_user(self, user_id: int = None, session_id: str = None,
                                   ip_address: str = None, user_agent: str = None,
                                   current_page: str = None) -> bool:
        """创建或更新在线用户记录"""
        try:
            with self.db_manager.get_session() as session:
                # 如果没有session_id，生成一个
                if not session_id:
                    import uuid
                    session_id = str(uuid.uuid4())

                # 使用原生SQL查询避免字段不匹配问题
                result = session.execute(text("PRAGMA table_info(online_users)"))
                columns = [row[1] for row in result.fetchall()]

                # 检查现有记录
                existing_query = "SELECT id FROM online_users WHERE session_id = ?"
                existing_result = session.execute(text(existing_query), (session_id,)).fetchone()

                if existing_result:
                    # 更新现有记录
                    update_fields = []
                    update_values = []

                    if 'last_activity' in columns:
                        update_fields.append('last_activity = ?')
                        update_values.append(datetime.now())

                    if current_page and 'current_page' in columns:
                        update_fields.append('current_page = ?')
                        update_values.append(current_page)

                    if ip_address and 'ip_address' in columns:
                        update_fields.append('ip_address = ?')
                        update_values.append(ip_address)

                    if user_agent and 'user_agent' in columns:
                        update_fields.append('user_agent = ?')
                        update_values.append(user_agent)

                    if update_fields:
                        update_values.append(session_id)  # WHERE条件的值
                        update_sql = f"UPDATE online_users SET {', '.join(update_fields)} WHERE session_id = ?"
                        session.execute(text(update_sql), tuple(update_values))
                else:
                    # 创建新记录
                    insert_fields = ['session_id']
                    insert_values = [session_id]
                    placeholders = ['?']

                    if user_id and 'user_id' in columns:
                        insert_fields.append('user_id')
                        insert_values.append(user_id)
                        placeholders.append('?')

                    if ip_address and 'ip_address' in columns:
                        insert_fields.append('ip_address')
                        insert_values.append(ip_address)
                        placeholders.append('?')

                    if user_agent and 'user_agent' in columns:
                        insert_fields.append('user_agent')
                        insert_values.append(user_agent)
                        placeholders.append('?')

                    if current_page and 'current_page' in columns:
                        insert_fields.append('current_page')
                        insert_values.append(current_page)
                        placeholders.append('?')

                    if 'login_time' in columns:
                        insert_fields.append('login_time')
                        insert_values.append(datetime.now())
                        placeholders.append('?')

                    if 'last_activity' in columns:
                        insert_fields.append('last_activity')
                        insert_values.append(datetime.now())
                        placeholders.append('?')

                    if 'page_views' in columns:
                        insert_fields.append('page_views')
                        insert_values.append(1 if current_page else 0)
                        placeholders.append('?')

                    if 'search_count' in columns:
                        insert_fields.append('search_count')
                        insert_values.append(0)
                        placeholders.append('?')

                    if 'download_count' in columns:
                        insert_fields.append('download_count')
                        insert_values.append(0)
                        placeholders.append('?')

                    insert_sql = f"INSERT INTO online_users ({', '.join(insert_fields)}) VALUES ({', '.join(placeholders)})"
                    session.execute(text(insert_sql), tuple(insert_values))

                session.commit()
                return True

        except Exception as e:
            self.logger.error(f"创建或更新在线用户记录失败: {e}")
            return False

    def update_online_user_search(self, session_id: str):
        """更新在线用户搜索统计"""
        try:
            with self.db_manager.get_session() as session:
                # 使用原生SQL更新
                update_sql = """
                UPDATE online_users
                SET search_count = COALESCE(search_count, 0) + 1,
                    last_activity = ?
                WHERE session_id = ?
                """
                result = session.execute(text(update_sql), (datetime.now(), session_id))
                session.commit()
                return result.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新在线用户搜索统计失败: {e}")
        return False

    def update_online_user_download(self, session_id: str):
        """更新在线用户下载统计"""
        try:
            with self.db_manager.get_session() as session:
                # 使用原生SQL更新
                update_sql = """
                UPDATE online_users
                SET download_count = COALESCE(download_count, 0) + 1,
                    last_activity = ?
                WHERE session_id = ?
                """
                result = session.execute(text(update_sql), (datetime.now(), session_id))
                session.commit()
                return result.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新在线用户下载统计失败: {e}")
        return False

    def update_online_user_activity(self, session_id: str, current_page: str = None):
        """更新在线用户活动"""
        try:
            with self.db_manager.get_session() as session:
                if current_page:
                    # 更新页面和页面访问次数
                    update_sql = """
                    UPDATE online_users
                    SET last_activity = ?,
                        current_page = ?,
                        page_views = COALESCE(page_views, 0) + 1
                    WHERE session_id = ?
                    """
                    result = session.execute(text(update_sql), (datetime.now(), current_page, session_id))
                else:
                    # 只更新活动时间
                    update_sql = """
                    UPDATE online_users
                    SET last_activity = ?
                    WHERE session_id = ?
                    """
                    result = session.execute(text(update_sql), (datetime.now(), session_id))

                session.commit()
                return result.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新在线用户活动失败: {e}")
        return False

    def logout_online_user(self, session_id: str):
        """用户登出"""
        try:
            with self.db_manager.get_session() as session:
                # 使用原生SQL更新
                update_sql = """
                UPDATE online_users
                SET logout_time = ?
                WHERE session_id = ?
                """
                result = session.execute(text(update_sql), (datetime.now(), session_id))
                session.commit()
                return result.rowcount > 0

        except Exception as e:
            self.logger.error(f"用户登出失败: {e}")
        return False
    
    def get_online_users(self) -> List[Dict[str, Any]]:
        """获取在线用户列表"""
        try:
            with self.db_manager.get_session() as session:
                # 使用原生SQL查询，只选择存在的字段
                timeout_time = datetime.now() - timedelta(minutes=30)

                # 先检查表结构，只查询存在的字段
                result = session.execute(text("PRAGMA table_info(online_users)"))
                columns = [row[1] for row in result.fetchall()]

                # 构建查询字段列表，只包含存在的字段
                base_fields = ['id', 'user_id', 'session_id', 'ip_address', 'last_activity', 'login_time']
                optional_fields = ['user_agent', 'browser_info', 'is_active', 'current_page',
                                 'page_views', 'search_count', 'download_count', 'logout_time']

                select_fields = []
                for field in base_fields:
                    if field in columns:
                        select_fields.append(field)

                for field in optional_fields:
                    if field in columns:
                        select_fields.append(field)

                # 构建SQL查询
                if select_fields:
                    sql = f"SELECT {', '.join(select_fields)} FROM online_users WHERE last_activity >= :timeout_time"
                    # 使用命名参数
                    result = session.execute(text(sql), {'timeout_time': timeout_time})
                else:
                    # 如果没有字段，返回空结果
                    result = []

                users_data = []
                if result and hasattr(result, 'fetchall'):
                    for row in result.fetchall():
                        row_dict = dict(zip(select_fields, row))

                        user_data = {
                            'user_id': row_dict.get('user_id'),
                            'session_id': row_dict.get('session_id', ''),
                            'ip_address': row_dict.get('ip_address', ''),
                            'login_time': row_dict.get('login_time', ''),
                            'last_activity': row_dict.get('last_activity', ''),
                            'page_views': row_dict.get('page_views', 0) or 0,
                            'search_count': row_dict.get('search_count', 0) or 0,
                            'download_count': row_dict.get('download_count', 0) or 0,
                            'current_page': row_dict.get('current_page', '') or '',
                            'user_agent': row_dict.get('user_agent', ''),
                            'browser_info': row_dict.get('browser_info', '')
                        }

                        # 格式化时间
                        user_data['login_time'] = self._safe_format_datetime(user_data['login_time'])
                        user_data['last_activity'] = self._safe_format_datetime(user_data['last_activity'])

                        # 获取用户信息
                        if user_data['user_id']:
                            db_user = session.query(User).filter(User.id == user_data['user_id']).first()
                            if db_user:
                                user_data['username'] = db_user.username
                                user_data['role'] = 'admin' if db_user.is_admin else 'user'
                            else:
                                user_data['username'] = f'用户{user_data["user_id"]}'
                                user_data['role'] = 'user'
                        else:
                            user_data['username'] = '匿名用户'
                            user_data['role'] = 'guest'

                        users_data.append(user_data)

                return users_data

        except Exception as e:
            self.logger.error(f"获取在线用户失败: {e}")
            return []
    
    # ==================== 活动日志 ====================
    
    def log_sensitive_activity(self, session: Session, user_id: Optional[int], 
                             action: str, details: Dict[str, Any], ip_address: str = None):
        """记录敏感活动日志"""
        try:
            activity_log = ActivityLog.create_log(
                user_id=user_id,
                action=f"sensitive_{action}",
                details=details,
                ip_address=ip_address,
                success='warning'
            )
            session.add(activity_log)
            
        except Exception as e:
            self.logger.error(f"记录敏感活动日志失败: {e}")
    
    # ==================== 统计和排行 ====================
    
    def get_user_behavior_stats(self, user_id: int = None, days: int = 7) -> Dict[str, Any]:
        """获取用户行为统计"""
        try:
            with self.db_manager.get_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                # 构建查询条件
                search_filter = SearchRecord.created_at >= start_date
                download_filter = DownloadRecord.created_at >= start_date
                
                if user_id:
                    search_filter = and_(search_filter, SearchRecord.user_id == user_id)
                    download_filter = and_(download_filter, DownloadRecord.user_id == user_id)
                
                # 搜索统计
                search_stats = session.query(
                    func.count(SearchRecord.id).label('total_searches'),
                    func.count(SearchRecord.id).filter(SearchRecord.is_sensitive == True).label('sensitive_searches'),
                    func.count(SearchRecord.id).filter(SearchRecord.is_blocked == True).label('blocked_searches')
                ).filter(search_filter).first()
                
                # 下载统计
                download_stats = session.query(
                    func.count(DownloadRecord.id).label('total_downloads'),
                    func.sum(DownloadRecord.file_size).label('total_size')
                ).filter(download_filter).first()
                
                return {
                    'search_stats': {
                        'total_searches': search_stats.total_searches or 0,
                        'sensitive_searches': search_stats.sensitive_searches or 0,
                        'blocked_searches': search_stats.blocked_searches or 0
                    },
                    'download_stats': {
                        'total_downloads': download_stats.total_downloads or 0,
                        'total_size': download_stats.total_size or 0
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取用户行为统计失败: {e}")
            return {}
    
    def get_search_records(self, limit: int = 100, offset: int = 0,
                          user_id: int = None, include_sensitive: bool = True) -> List[Dict[str, Any]]:
        """获取搜索记录"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(SearchRecord, User.username).outerjoin(
                    User, SearchRecord.user_id == User.id
                ).order_by(SearchRecord.created_at.desc())

                if user_id:
                    query = query.filter(SearchRecord.user_id == user_id)

                if not include_sensitive:
                    query = query.filter(SearchRecord.is_sensitive == False)

                records = query.offset(offset).limit(limit).all()

                result = []
                for record, username in records:
                    result.append({
                        'id': record.id,
                        'user_id': record.user_id,
                        'username': username or '匿名用户',
                        'search_query': record.search_query,
                        'results_count': record.results_count,
                        'is_sensitive': record.is_sensitive,
                        'is_blocked': record.is_blocked,
                        'block_reason': record.block_reason,
                        'ip_address': record.ip_address,
                        'created_at': self._safe_format_datetime(record.created_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取搜索记录失败: {e}")
            return []

    def get_search_records_filtered(self, limit: int = 100, offset: int = 0, **filters) -> List[Dict[str, Any]]:
        """获取带过滤条件的搜索记录"""
        try:
            with self.db_manager.get_session() as session:
                # 构建基础查询
                query = session.query(SearchRecord, User.username).outerjoin(
                    User, SearchRecord.user_id == User.id
                )

                # 应用过滤条件
                if filters.get('username'):
                    query = query.filter(User.username.contains(filters['username']))

                if filters.get('user_id'):
                    query = query.filter(SearchRecord.user_id == filters['user_id'])

                if filters.get('only_sensitive'):
                    query = query.filter(SearchRecord.is_sensitive == True)

                if filters.get('only_blocked'):
                    query = query.filter(SearchRecord.is_blocked == True)

                if filters.get('search_query'):
                    query = query.filter(SearchRecord.search_query.contains(filters['search_query']))

                # 时间过滤
                if filters.get('date_filter'):
                    date_filter = filters['date_filter']
                    now = datetime.now()

                    if date_filter == 'today':
                        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        query = query.filter(SearchRecord.created_at >= start_date)
                    elif date_filter == 'week':
                        start_date = now - timedelta(days=7)
                        query = query.filter(SearchRecord.created_at >= start_date)
                    elif date_filter == 'month':
                        start_date = now - timedelta(days=30)
                        query = query.filter(SearchRecord.created_at >= start_date)

                # 排序和分页
                records = query.order_by(SearchRecord.created_at.desc()).offset(offset).limit(limit).all()

                result = []
                for record, username in records:
                    result.append({
                        'id': record.id,
                        'user_id': record.user_id,
                        'username': username or '匿名用户',
                        'search_query': record.search_query,
                        'search_type': record.search_type,
                        'results_count': record.results_count,
                        'is_sensitive': record.is_sensitive,
                        'is_blocked': record.is_blocked,
                        'block_reason': record.block_reason,
                        'ip_address': record.ip_address,
                        'created_at': self._safe_format_datetime(record.created_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取过滤搜索记录失败: {e}")
            return []

    def get_download_records(self, limit: int = 100, offset: int = 0,
                           user_id: int = None) -> List[Dict[str, Any]]:
        """获取下载记录"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(DownloadRecord, User.username).outerjoin(
                    User, DownloadRecord.user_id == User.id
                ).order_by(DownloadRecord.created_at.desc())

                if user_id:
                    query = query.filter(DownloadRecord.user_id == user_id)

                records = query.offset(offset).limit(limit).all()

                result = []
                for record, username in records:
                    result.append({
                        'id': record.id,
                        'user_id': record.user_id,
                        'username': username or '匿名用户',
                        'download_type': record.download_type,
                        'file_id': record.file_id,
                        'folder_id': record.folder_id,
                        'file_size': record.file_size or 0,
                        'is_encrypted': record.is_encrypted,
                        'download_status': record.download_status,
                        'ip_address': record.ip_address,
                        'created_at': self._safe_format_datetime(record.created_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取下载记录失败: {e}")
            return []

    def get_user_rankings(self, ranking_type: str = 'activity',
                         period: str = 'daily', limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户排行榜"""
        try:
            with self.db_manager.get_session() as session:
                # 根据周期确定时间范围
                if period == 'daily':
                    start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                elif period == 'weekly':
                    start_time = datetime.now() - timedelta(days=7)
                elif period == 'monthly':
                    start_time = datetime.now() - timedelta(days=30)
                else:
                    start_time = datetime.min

                if ranking_type == 'search':
                    # 搜索排行
                    rankings = session.query(
                        SearchRecord.user_id,
                        func.count(SearchRecord.id).label('search_count')
                    ).filter(
                        SearchRecord.created_at >= start_time,
                        SearchRecord.user_id.isnot(None)
                    ).group_by(SearchRecord.user_id).order_by(
                        desc('search_count')
                    ).limit(limit).all()

                elif ranking_type == 'download':
                    # 下载排行
                    rankings = session.query(
                        DownloadRecord.user_id,
                        func.count(DownloadRecord.id).label('download_count'),
                        func.sum(DownloadRecord.file_size).label('download_size')
                    ).filter(
                        DownloadRecord.created_at >= start_time,
                        DownloadRecord.user_id.isnot(None)
                    ).group_by(DownloadRecord.user_id).order_by(
                        desc('download_count')
                    ).limit(limit).all()
                    
                else:
                    # 综合活跃度排行
                    search_subquery = session.query(
                        SearchRecord.user_id,
                        func.count(SearchRecord.id).label('search_count')
                    ).filter(
                        SearchRecord.created_at >= start_time,
                        SearchRecord.user_id.isnot(None)
                    ).group_by(SearchRecord.user_id).subquery()
                    
                    download_subquery = session.query(
                        DownloadRecord.user_id,
                        func.count(DownloadRecord.id).label('download_count')
                    ).filter(
                        DownloadRecord.created_at >= start_time,
                        DownloadRecord.user_id.isnot(None)
                    ).group_by(DownloadRecord.user_id).subquery()
                    
                    rankings = session.query(
                        User.id.label('user_id'),
                        User.username,
                        func.coalesce(search_subquery.c.search_count, 0).label('search_count'),
                        func.coalesce(download_subquery.c.download_count, 0).label('download_count'),
                        (func.coalesce(search_subquery.c.search_count, 0) + 
                         func.coalesce(download_subquery.c.download_count, 0) * 2).label('activity_score')
                    ).outerjoin(
                        search_subquery, User.id == search_subquery.c.user_id
                    ).outerjoin(
                        download_subquery, User.id == download_subquery.c.user_id
                    ).order_by(desc('activity_score')).limit(limit).all()
                
                # 格式化结果
                result = []
                for i, ranking in enumerate(rankings):
                    if hasattr(ranking, 'username'):
                        # 综合排行已包含用户名
                        user_data = {
                            'rank': i + 1,
                            'user_id': ranking.user_id,
                            'username': ranking.username,
                            'search_count': getattr(ranking, 'search_count', 0),
                            'download_count': getattr(ranking, 'download_count', 0),
                            'activity_score': getattr(ranking, 'activity_score', 0)
                        }
                    else:
                        # 需要查询用户信息
                        user = session.query(User).filter(User.id == ranking.user_id).first()
                        user_data = {
                            'rank': i + 1,
                            'user_id': ranking.user_id,
                            'username': user.username if user else f'用户{ranking.user_id}',
                            'search_count': getattr(ranking, 'search_count', 0),
                            'download_count': getattr(ranking, 'download_count', 0),
                            'download_size': getattr(ranking, 'download_size', 0)
                        }
                    
                    result.append(user_data)
                
                return result

        except Exception as e:
            self.logger.error(f"获取用户排行榜失败: {e}")
            return []

    def get_sensitive_files(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取敏感文件列表"""
        try:
            with self.db_manager.get_session() as session:
                sensitive_files = session.query(SensitiveFile).order_by(
                    SensitiveFile.created_at.desc()
                ).offset(offset).limit(limit).all()

                result = []
                for sf in sensitive_files:
                    result.append({
                        'id': sf.id,
                        'file_id': getattr(sf, 'file_id', None),
                        'file_path': getattr(sf, 'file_path', ''),
                        'reason': getattr(sf, 'reason', getattr(sf, 'description', '')),
                        'severity': getattr(sf, 'severity', getattr(sf, 'sensitivity_level', 'medium')),
                        'auto_detected': getattr(sf, 'auto_detected', False),
                        'created_at': self._safe_format_datetime(sf.created_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取敏感文件列表失败: {e}")
            return []

    def get_blocked_keywords(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取屏蔽关键词列表"""
        try:
            with self.db_manager.get_session() as session:
                keywords = session.query(BlockedKeyword).order_by(
                    BlockedKeyword.created_at.desc()
                ).offset(offset).limit(limit).all()

                result = []
                for kw in keywords:
                    result.append({
                        'id': kw.id,
                        'keyword': kw.keyword,
                        'keyword_type': kw.keyword_type,
                        'is_regex': getattr(kw, 'is_regex', False),
                        'severity': getattr(kw, 'severity', 'medium'),
                        'is_active': kw.is_active,
                        'block_search': kw.block_search,
                        'block_download': kw.block_download,
                        'description': getattr(kw, 'description', ''),
                        'hit_count': kw.hit_count or 0,
                        'last_hit': self._safe_format_datetime(kw.last_hit),
                        'created_at': self._safe_format_datetime(kw.created_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取屏蔽关键词列表失败: {e}")
            return []

    def add_blocked_keyword(self, keyword: str, keyword_type: str = 'partial',
                           is_regex: bool = False, severity: str = 'medium',
                           is_active: bool = True, description: str = '', **kwargs) -> bool:
        """添加屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                # 检查关键词是否已存在
                existing = session.query(BlockedKeyword).filter(
                    BlockedKeyword.keyword == keyword
                ).first()

                if existing:
                    self.logger.warning(f"屏蔽关键词已存在: {keyword}")
                    return False

                # 创建新的屏蔽关键词
                blocked_keyword = BlockedKeyword(
                    keyword=keyword,
                    keyword_type=keyword_type,
                    is_regex=is_regex,
                    severity=severity,
                    is_active=is_active,
                    description=description,
                    block_search=kwargs.get('block_search', True),
                    block_download=kwargs.get('block_download', False)
                )

                session.add(blocked_keyword)
                session.commit()

                self.logger.info(f"屏蔽关键词添加成功: {keyword}")
                return True

        except Exception as e:
            self.logger.error(f"添加屏蔽关键词失败: {e}")
            return False

    def update_blocked_keyword(self, keyword_id: int, **kwargs) -> bool:
        """更新屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                keyword = session.query(BlockedKeyword).filter(
                    BlockedKeyword.id == keyword_id
                ).first()

                if not keyword:
                    self.logger.warning(f"屏蔽关键词不存在: ID={keyword_id}")
                    return False

                # 更新字段
                for key, value in kwargs.items():
                    if hasattr(keyword, key):
                        setattr(keyword, key, value)

                session.commit()

                self.logger.info(f"屏蔽关键词更新成功: ID={keyword_id}")
                return True

        except Exception as e:
            self.logger.error(f"更新屏蔽关键词失败: {e}")
            return False

    def delete_blocked_keyword(self, keyword_id: int) -> bool:
        """删除屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                keyword = session.query(BlockedKeyword).filter(
                    BlockedKeyword.id == keyword_id
                ).first()

                if not keyword:
                    self.logger.warning(f"屏蔽关键词不存在: ID={keyword_id}")
                    return False

                session.delete(keyword)
                session.commit()

                self.logger.info(f"屏蔽关键词删除成功: ID={keyword_id}")
                return True

        except Exception as e:
            self.logger.error(f"删除屏蔽关键词失败: {e}")
            return False

    def get_user_quotas(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户配额列表"""
        try:
            with self.db_manager.get_session() as session:
                quotas = session.query(UserQuota, User.username).join(
                    User, UserQuota.user_id == User.id
                ).order_by(UserQuota.updated_at.desc()).offset(offset).limit(limit).all()

                result = []
                for quota, username in quotas:
                    result.append({
                        'id': quota.id,
                        'user_id': quota.user_id,
                        'username': username,
                        'daily_search_limit': quota.daily_search_limit,
                        'daily_download_limit': quota.daily_download_limit,
                        'daily_searches': quota.daily_searches,
                        'daily_downloads': quota.daily_downloads,
                        'total_searches': quota.total_searches,
                        'total_downloads': quota.total_downloads,
                        'last_reset': self._safe_format_datetime(quota.last_reset, '%Y-%m-%d'),
                        'updated_at': self._safe_format_datetime(quota.updated_at)
                    })

                return result

        except Exception as e:
            self.logger.error(f"获取用户配额列表失败: {e}")
            return []

    def get_user_bans(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户封禁列表"""
        try:
            with self.db_manager.get_session() as session:
                # 直接使用SQL查询避免字段不匹配问题
                result = session.execute(text("""
                    SELECT ub.id, ub.user_id, u.username, ub.ban_type, ub.reason,
                           ub.duration_hours, ub.is_active, ub.banned_at,
                           ub.expires_at, ub.unbanned_at, ub.banned_by
                    FROM user_bans ub
                    JOIN users u ON ub.user_id = u.id
                    ORDER BY ub.banned_at DESC
                    LIMIT :limit OFFSET :offset
                """), {'limit': limit, 'offset': offset}).fetchall()

                ban_list = []
                for row in result:
                    # 计算剩余时间
                    remaining_time = ""
                    duration_hours = row[5] if row[5] is not None else 0

                    if not row[6]:  # not is_active
                        remaining_time = "已解封"
                    elif row[8]:  # has expires_at
                        try:
                            from datetime import datetime
                            expires_at = datetime.fromisoformat(row[8].replace('Z', '+00:00')) if isinstance(row[8], str) else row[8]
                            if expires_at > datetime.now():
                                delta = expires_at - datetime.now()
                                days = delta.days
                                hours = int(delta.seconds // 3600)
                                minutes = int((delta.seconds % 3600) // 60)

                                if days > 0:
                                    remaining_time = f"{days}天{hours}小时"
                                elif hours > 0:
                                    remaining_time = f"{hours}小时{minutes}分钟"
                                else:
                                    remaining_time = f"{minutes}分钟"
                            else:
                                remaining_time = "已过期"
                        except:
                            remaining_time = "未知"
                    else:
                        # 没有到期时间，可能是永久封禁
                        remaining_time = "永久"

                    ban_list.append({
                        'id': row[0],
                        'user_id': row[1],
                        'username': row[2],
                        'ban_type': row[3],
                        'reason': row[4] or '',
                        'duration_hours': duration_hours,
                        'is_active': row[6],
                        'banned_at': self._safe_format_datetime(row[7]),
                        'expires_at': self._safe_format_datetime(row[8]),
                        'unbanned_at': self._safe_format_datetime(row[9]),
                        'banned_by': row[10],
                        'remaining_time': remaining_time
                    })

                return ban_list

        except Exception as e:
            self.logger.error(f"获取用户封禁列表失败: {e}")
            # 如果SQL查询失败，尝试直接从表中获取基本信息
            try:
                with self.db_manager.get_session() as session:
                    import sqlite3
                    conn = sqlite3.connect('data/file_share_system.db')
                    cursor = conn.cursor()

                    cursor.execute("""
                        SELECT ub.id, ub.user_id, u.username, ub.ban_type, ub.reason,
                               ub.is_active, ub.banned_at, ub.expires_at
                        FROM user_bans ub
                        LEFT JOIN users u ON ub.user_id = u.id
                        ORDER BY ub.id DESC
                        LIMIT ? OFFSET ?
                    """, (limit, offset))

                    rows = cursor.fetchall()
                    conn.close()

                    ban_list = []
                    for row in rows:
                        ban_list.append({
                            'id': row[0],
                            'user_id': row[1],
                            'username': row[2] or f'用户{row[1]}',
                            'ban_type': row[3] or 'login',
                            'reason': row[4] or '',
                            'duration_hours': 0,
                            'is_active': row[5],
                            'banned_at': row[6] or '',
                            'expires_at': row[7] or '',
                            'unbanned_at': '',
                            'banned_by': 0,
                            'remaining_time': ''
                        })

                    return ban_list

            except Exception as e2:
                self.logger.error(f"备用查询也失败: {e2}")
                return []

    # ==================== 管理功能 ====================

    def add_sensitive_file(self, file_path: str, reason: str, severity: str = 'medium') -> bool:
        """添加敏感文件"""
        try:
            with self.db_manager.get_session() as session:
                # 根据实际的表结构创建敏感文件记录
                sensitive_file_data = {
                    'sensitivity_level': severity,
                    'description': reason,
                    'action_type': 'warn',
                    'alert_admin': False
                }

                # 如果有file_path字段，使用它；否则使用description
                if hasattr(SensitiveFile, 'file_path'):
                    sensitive_file_data['file_path'] = file_path

                sensitive_file = SensitiveFile(**sensitive_file_data)
                session.add(sensitive_file)
                session.commit()
                return True

        except Exception as e:
            self.logger.error(f"添加敏感文件失败: {e}")
            return False

    def update_sensitive_file(self, file_id: int, **kwargs) -> bool:
        """更新敏感文件"""
        try:
            with self.db_manager.get_session() as session:
                sensitive_file = session.query(SensitiveFile).filter(
                    SensitiveFile.id == file_id
                ).first()

                if not sensitive_file:
                    return False

                for key, value in kwargs.items():
                    if hasattr(sensitive_file, key):
                        setattr(sensitive_file, key, value)

                session.commit()
                return True

        except Exception as e:
            self.logger.error(f"更新敏感文件失败: {e}")
            return False

    def delete_sensitive_file(self, file_id: int) -> bool:
        """删除敏感文件"""
        try:
            with self.db_manager.get_session() as session:
                sensitive_file = session.query(SensitiveFile).filter(
                    SensitiveFile.id == file_id
                ).first()

                if sensitive_file:
                    session.delete(sensitive_file)
                    session.commit()
                    return True
                return False

        except Exception as e:
            self.logger.error(f"删除敏感文件失败: {e}")
            return False



    def update_blocked_keyword(self, keyword_id: int, **kwargs) -> bool:
        """更新屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                blocked_keyword = session.query(BlockedKeyword).filter(
                    BlockedKeyword.id == keyword_id
                ).first()

                if not blocked_keyword:
                    return False

                for key, value in kwargs.items():
                    if hasattr(blocked_keyword, key):
                        setattr(blocked_keyword, key, value)

                session.commit()
                return True

        except Exception as e:
            self.logger.error(f"更新屏蔽关键词失败: {e}")
            return False

    def delete_blocked_keyword(self, keyword_id: int) -> bool:
        """删除屏蔽关键词"""
        try:
            with self.db_manager.get_session() as session:
                blocked_keyword = session.query(BlockedKeyword).filter(
                    BlockedKeyword.id == keyword_id
                ).first()

                if blocked_keyword:
                    session.delete(blocked_keyword)
                    session.commit()
                    return True
                return False

        except Exception as e:
            self.logger.error(f"删除屏蔽关键词失败: {e}")
            return False

    def set_user_quota(self, username: str, daily_search_limit: int = None,
                      daily_download_limit: int = None) -> bool:
        """设置用户配额"""
        try:
            with self.db_manager.get_session() as session:
                # 查找用户
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return False

                # 查找或创建配额记录
                quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                if not quota:
                    quota = UserQuota(user_id=user.id)
                    session.add(quota)

                # 更新配额
                if daily_search_limit is not None:
                    quota.daily_search_limit = daily_search_limit
                if daily_download_limit is not None:
                    quota.daily_download_limit = daily_download_limit

                session.commit()
                return True

        except Exception as e:
            self.logger.error(f"设置用户配额失败: {e}")
            return False

    def reset_user_quota(self, username: str) -> bool:
        """重置用户配额"""
        try:
            with self.db_manager.get_session() as session:
                # 查找用户
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return False

                # 查找配额记录
                quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                if quota:
                    quota.reset_daily_quota()
                    session.commit()
                    return True
                return False

        except Exception as e:
            self.logger.error(f"重置用户配额失败: {e}")
            return False

    def cleanup_old_records(self, days: int = 30) -> Dict[str, Any]:
        """清理旧记录"""
        try:
            with self.db_manager.get_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)
                deleted_counts = {}

                # 清理搜索记录
                search_count = session.query(SearchRecord).filter(
                    SearchRecord.created_at < cutoff_date
                ).count()
                session.query(SearchRecord).filter(
                    SearchRecord.created_at < cutoff_date
                ).delete()
                deleted_counts['search_records'] = search_count

                # 清理下载记录
                download_count = session.query(DownloadRecord).filter(
                    DownloadRecord.created_at < cutoff_date
                ).count()
                session.query(DownloadRecord).filter(
                    DownloadRecord.created_at < cutoff_date
                ).delete()
                deleted_counts['download_records'] = download_count

                # 清理活动日志
                activity_count = session.query(ActivityLog).filter(
                    ActivityLog.created_at < cutoff_date
                ).count()
                session.query(ActivityLog).filter(
                    ActivityLog.created_at < cutoff_date
                ).delete()
                deleted_counts['activity_logs'] = activity_count

                session.commit()

                return {
                    'success': True,
                    'deleted_counts': deleted_counts
                }

        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
            return {'success': False, 'error': str(e)}

    # ==================== 用户封禁管理 ====================

    def ban_user(self, user_id: int, ban_type: str = 'login', duration_hours: int = 24,
                 reason: str = None, admin_id: int = None, is_permanent: bool = False,
                 admin_note: str = None) -> Dict[str, Any]:
        """封禁用户"""
        try:
            with self.db_manager.get_session() as session:
                # 检查是否已有活跃的封禁记录
                existing_ban = session.query(UserBan).filter(
                    UserBan.user_id == user_id,
                    UserBan.ban_type == ban_type,
                    UserBan.is_active == True
                ).first()

                if existing_ban and not existing_ban.is_expired():
                    return {'success': False, 'error': f'用户已被{ban_type}封禁'}

                # 如果有过期的封禁记录，先清理
                if existing_ban and existing_ban.is_expired():
                    existing_ban.is_active = False

                # 创建封禁记录
                ban = UserBan(
                    user_id=user_id,
                    ban_type=ban_type,
                    duration_hours=duration_hours,
                    reason=reason,
                    banned_by=admin_id,
                    is_permanent=is_permanent,
                    admin_note=admin_note
                )
                session.add(ban)

                # 更新User表中的封禁状态
                from models.user import User
                user = session.query(User).filter(User.id == user_id).first()
                if user:
                    if is_permanent:
                        user.ban_user(permanent=True)
                    else:
                        user.ban_user(duration_minutes=duration_hours * 60)

                # 记录活动日志
                self.log_sensitive_activity(session, user_id, 'ban', {
                    'ban_type': ban_type,
                    'duration_hours': duration_hours,
                    'is_permanent': is_permanent,
                    'reason': reason,
                    'admin_id': admin_id
                })

                # 如果是登录封禁，强制下线用户
                if ban_type in ['login', 'all']:
                    session.query(OnlineUser).filter(
                        OnlineUser.user_id == user_id
                    ).update({'is_active': False, 'logout_time': datetime.now()})

                session.commit()

                return {'success': True, 'ban_id': ban.id, 'message': '用户封禁成功'}

        except Exception as e:
            self.logger.error(f"封禁用户失败: {e}")
            return {'success': False, 'error': str(e)}

    def unban_user(self, user_id: int, ban_type: str = None, admin_id: int = None) -> Dict[str, Any]:
        """解封用户"""
        try:
            self.logger.info(f"开始解封用户: user_id={user_id}, ban_type={ban_type}, admin_id={admin_id}")

            with self.db_manager.get_session() as session:
                # 首先检查用户是否存在
                from models.user import User
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    self.logger.error(f"用户不存在: user_id={user_id}")
                    return {'success': False, 'error': '未找到指定的用户记录'}

                self.logger.info(f"找到用户: {user.username}, 当前封禁状态: {user.is_banned}")

                # 构建查询条件
                query = session.query(UserBan).filter(
                    UserBan.user_id == user_id,
                    UserBan.is_active == True
                )

                if ban_type:
                    query = query.filter(UserBan.ban_type == ban_type)

                bans = query.all()
                self.logger.info(f"找到 {len(bans)} 条活跃的封禁记录")

                if not bans:
                    # 如果用户在User表中被标记为封禁，但没有活跃的封禁记录，直接解封
                    if user.is_banned:
                        self.logger.info("用户被标记为封禁但没有活跃的封禁记录，直接解封用户")
                        user.unban_user()

                        # 记录活动日志
                        self.log_sensitive_activity(session, user_id, 'unban', {
                            'ban_type': ban_type,
                            'admin_id': admin_id,
                            'unban_count': 0,
                            'note': '直接解封（无活跃封禁记录）'
                        })

                        session.commit()
                        return {'success': True, 'unbanned_count': 0, 'message': '用户已解封（无活跃封禁记录）'}
                    else:
                        return {'success': False, 'error': '用户未被封禁'}

                # 解封所有匹配的记录
                for ban in bans:
                    self.logger.info(f"解封记录: ban_id={ban.id}, ban_type={ban.ban_type}")
                    ban.unban(admin_id)

                # 检查用户是否还有其他活跃的封禁记录
                remaining_bans = session.query(UserBan).filter(
                    UserBan.user_id == user_id,
                    UserBan.is_active == True
                ).count()

                self.logger.info(f"剩余活跃封禁记录数: {remaining_bans}")

                # 如果没有其他活跃的封禁记录，更新User表中的is_banned字段
                if remaining_bans == 0:
                    self.logger.info("没有剩余的活跃封禁记录，解封用户")
                    user.unban_user()  # 这会设置is_banned=False并清除ban_until

                # 记录活动日志
                self.log_sensitive_activity(session, user_id, 'unban', {
                    'ban_type': ban_type,
                    'admin_id': admin_id,
                    'unban_count': len(bans)
                })

                session.commit()
                self.logger.info(f"解封操作完成: unbanned_count={len(bans)}")

                return {'success': True, 'unbanned_count': len(bans)}

        except Exception as e:
            self.logger.error(f"解封用户失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def check_user_ban(self, user_id: int, action: str = 'login') -> Dict[str, Any]:
        """检查用户是否被封禁"""
        try:
            self.logger.info(f"检查用户封禁状态: user_id={user_id}, action={action}")

            with self.db_manager.get_session() as session:
                # 清理过期的封禁记录（只清理有到期时间且已过期的记录）
                expired_count = session.query(UserBan).filter(
                    UserBan.expires_at.isnot(None),  # 有到期时间
                    UserBan.expires_at <= datetime.now(),  # 已过期
                    UserBan.is_active == True  # 仍然活跃
                ).update({'is_active': False})

                if expired_count > 0:
                    self.logger.info(f"清理了 {expired_count} 条过期的封禁记录")

                # 检查相关的封禁记录
                ban_types = [action, 'all']
                ban = session.query(UserBan).filter(
                    UserBan.user_id == user_id,
                    UserBan.ban_type.in_(ban_types),
                    UserBan.is_active == True,
                    or_(
                        UserBan.is_permanent == True,  # 永久封禁
                        and_(
                            UserBan.expires_at.isnot(None),  # 有到期时间
                            UserBan.expires_at > datetime.now()  # 临时封禁未过期
                        )
                    )
                ).first()

                session.commit()

                self.logger.info(f"封禁检查结果: {'被封禁' if ban else '未封禁'}")
                if ban:
                    self.logger.info(f"封禁详情: 类型={ban.ban_type}, 永久={ban.is_permanent}, 到期={ban.expires_at}")

                if ban:
                    return {
                        'is_banned': True,
                        'ban_info': ban.to_dict()
                    }
                else:
                    return {'is_banned': False}

        except Exception as e:
            self.logger.error(f"检查用户封禁状态失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return {'is_banned': False}

    def get_user_bans_paginated(self, page: int = 1, page_size: int = 20,
                               ban_type: str = None, is_active: bool = None,
                               user_id: int = None) -> Dict[str, Any]:
        """分页获取用户封禁列表"""
        try:
            with self.db_manager.get_session() as session:
                # 构建查询
                query = session.query(UserBan).join(User, UserBan.user_id == User.id)

                # 添加过滤条件
                if ban_type:
                    query = query.filter(UserBan.ban_type == ban_type)

                if is_active is not None:
                    if is_active:
                        # 活跃的封禁：is_active=True 且未过期
                        query = query.filter(
                            UserBan.is_active == True,
                            or_(
                                UserBan.is_permanent == True,
                                UserBan.expires_at > datetime.now()
                            )
                        )
                    else:
                        # 非活跃的封禁：is_active=False 或已过期
                        query = query.filter(
                            or_(
                                UserBan.is_active == False,
                                and_(
                                    UserBan.is_permanent == False,
                                    UserBan.expires_at <= datetime.now()
                                )
                            )
                        )

                if user_id:
                    query = query.filter(UserBan.user_id == user_id)

                # 获取总数
                total_count = query.count()

                # 分页查询
                offset = (page - 1) * page_size
                bans = query.order_by(UserBan.banned_at.desc()).offset(offset).limit(page_size).all()

                # 转换为字典并添加用户信息
                ban_list = []
                for ban in bans:
                    ban_dict = ban.to_dict()
                    ban_dict['username'] = ban.user.username if ban.user else '未知用户'
                    ban_dict['banned_by_username'] = ban.admin.username if ban.admin else '系统'
                    ban_list.append(ban_dict)

                return {
                    'success': True,
                    'bans': ban_list,
                    'total_count': total_count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': (total_count + page_size - 1) // page_size
                }

        except Exception as e:
            self.logger.error(f"获取封禁列表失败: {e}")
            return {'success': False, 'error': str(e), 'bans': [], 'total_count': 0}

    def extend_user_ban(self, ban_id: int, additional_hours: int, admin_id: int = None) -> Dict[str, Any]:
        """延长用户封禁时间"""
        try:
            with self.db_manager.get_session() as session:
                ban = session.query(UserBan).filter(UserBan.id == ban_id).first()

                if not ban:
                    return {'success': False, 'error': '封禁记录不存在'}

                if not ban.is_active:
                    return {'success': False, 'error': '封禁记录已失效'}

                if ban.is_permanent:
                    return {'success': False, 'error': '永久封禁无法延长时间'}

                # 延长封禁时间
                ban.extend_ban(additional_hours)

                # 记录活动日志
                self.log_sensitive_activity(session, ban.user_id, 'extend_ban', {
                    'ban_id': ban_id,
                    'additional_hours': additional_hours,
                    'admin_id': admin_id,
                    'new_expires_at': ban.expires_at.isoformat() if ban.expires_at else None
                })

                session.commit()

                return {'success': True, 'message': f'封禁时间已延长{additional_hours}小时'}

        except Exception as e:
            self.logger.error(f"延长封禁时间失败: {e}")
            return {'success': False, 'error': str(e)}

    def make_ban_permanent(self, ban_id: int, admin_id: int = None) -> Dict[str, Any]:
        """将封禁设为永久"""
        try:
            with self.db_manager.get_session() as session:
                ban = session.query(UserBan).filter(UserBan.id == ban_id).first()

                if not ban:
                    return {'success': False, 'error': '封禁记录不存在'}

                if not ban.is_active:
                    return {'success': False, 'error': '封禁记录已失效'}

                if ban.is_permanent:
                    return {'success': False, 'error': '该封禁已是永久封禁'}

                # 设为永久封禁
                ban.make_permanent()

                # 记录活动日志
                self.log_sensitive_activity(session, ban.user_id, 'make_permanent_ban', {
                    'ban_id': ban_id,
                    'admin_id': admin_id
                })

                session.commit()

                return {'success': True, 'message': '已设为永久封禁'}

        except Exception as e:
            self.logger.error(f"设置永久封禁失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_user_ban_history(self, user_id: int, limit: int = 50) -> Dict[str, Any]:
        """获取用户封禁历史"""
        try:
            with self.db_manager.get_session() as session:
                bans = session.query(UserBan).filter(
                    UserBan.user_id == user_id
                ).order_by(UserBan.banned_at.desc()).limit(limit).all()

                ban_list = []
                for ban in bans:
                    ban_dict = ban.to_dict()
                    ban_dict['banned_by_username'] = ban.admin.username if ban.admin else '系统'
                    ban_list.append(ban_dict)

                return {
                    'success': True,
                    'bans': ban_list,
                    'total_count': len(ban_list)
                }

        except Exception as e:
            self.logger.error(f"获取用户封禁历史失败: {e}")
            return {'success': False, 'error': str(e), 'bans': []}

    # ==================== 敏感文件管理 ====================

    def add_sensitive_file(self, file_id: int = None, folder_id: int = None,
                          sensitivity_level: str = 'medium', keywords: List[str] = None,
                          description: str = None, action_type: str = 'warn') -> Dict[str, Any]:
        """添加敏感文件"""
        try:
            with self.db_manager.get_session() as session:
                # 检查是否已存在
                existing = session.query(SensitiveFile).filter(
                    or_(
                        and_(SensitiveFile.file_id == file_id, file_id is not None),
                        and_(SensitiveFile.folder_id == folder_id, folder_id is not None)
                    )
                ).first()

                if existing:
                    return {'success': False, 'error': '敏感文件记录已存在'}

                # 创建敏感文件记录
                sensitive_file = SensitiveFile(
                    file_id=file_id,
                    folder_id=folder_id,
                    sensitivity_level=sensitivity_level,
                    keywords=keywords or [],
                    description=description,
                    action_type=action_type
                )
                session.add(sensitive_file)
                session.commit()

                return {'success': True, 'sensitive_file_id': sensitive_file.id}

        except Exception as e:
            self.logger.error(f"添加敏感文件失败: {e}")
            return {'success': False, 'error': str(e)}



    # ==================== 数据清理 ====================

    def cleanup_old_records(self, days: int = 30) -> Dict[str, Any]:
        """清理旧记录"""
        try:
            with self.db_manager.get_session() as session:
                cutoff_date = datetime.now() - timedelta(days=days)

                # 清理搜索记录
                search_deleted = session.query(SearchRecord).filter(
                    SearchRecord.created_at < cutoff_date
                ).delete()

                # 清理活动日志
                activity_deleted = session.query(ActivityLog).filter(
                    ActivityLog.created_at < cutoff_date
                ).delete()

                # 清理过期的在线用户记录
                online_deleted = session.query(OnlineUser).filter(
                    OnlineUser.last_activity < cutoff_date,
                    OnlineUser.is_active == False
                ).delete()

                # 清理过期的封禁记录
                ban_deleted = session.query(UserBan).filter(
                    UserBan.expires_at < cutoff_date,
                    UserBan.is_active == False
                ).delete()

                session.commit()

                return {
                    'success': True,
                    'deleted_counts': {
                        'search_records': search_deleted,
                        'activity_logs': activity_deleted,
                        'online_users': online_deleted,
                        'ban_records': ban_deleted
                    }
                }

        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}")
            return {'success': False, 'error': str(e)}
