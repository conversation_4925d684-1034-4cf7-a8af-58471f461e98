#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文件夹上传权限脚本
"""

import sqlite3
import json
import os
import sys

def fix_upload_permission():
    """修复文件夹上传权限"""
    
    # 数据库路径
    db_path = os.path.join('backend', 'data', 'file_share_system.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有共享文件夹
        cursor.execute("SELECT id, name, allow_upload, allow_write FROM shared_folders")
        folders = cursor.fetchall()

        print("📁 当前文件夹权限状态:")
        for folder_id, name, allow_upload, allow_write in folders:
            print(f"  - 文件夹 '{name}' (ID: {folder_id}): 上传权限 = {bool(allow_upload)}, 写权限 = {bool(allow_write)}")

        # 修复所有文件夹的上传权限
        updated_count = 0
        for folder_id, name, allow_upload, allow_write in folders:
            if not allow_upload:
                # 启用上传权限和写权限
                cursor.execute(
                    "UPDATE shared_folders SET allow_upload = 1, allow_write = 1 WHERE id = ?",
                    (folder_id,)
                )

                print(f"✅ 已启用文件夹 '{name}' 的上传权限和写权限")
                updated_count += 1
        
        # 提交更改
        conn.commit()
        conn.close()
        
        if updated_count > 0:
            print(f"\n🎉 成功修复了 {updated_count} 个文件夹的上传权限")
            print("⚠️  请重启服务器以使更改生效")
        else:
            print("\n✅ 所有文件夹的上传权限都已正确配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复权限时发生错误: {e}")
        return False

if __name__ == '__main__':
    print("🔧 文件夹上传权限修复工具")
    print("=" * 40)
    
    success = fix_upload_permission()
    
    if success:
        print("\n📝 下一步操作:")
        print("1. 重启服务器 (关闭GUI界面后重新运行 python backend/main.py)")
        print("2. 刷新浏览器页面")
        print("3. 重新尝试文件上传")
    else:
        print("\n❌ 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
