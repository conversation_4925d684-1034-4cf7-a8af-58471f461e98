#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Enum
from sqlalchemy.sql import func
from config.database import Base
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional

class User(Base):
    """用户模型"""
    
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, comment='用户名')
    password_hash = Column(String(128), nullable=False, comment='密码哈希')
    salt = Column(String(32), nullable=False, comment='密码盐值')
    email = Column(String(100), unique=True, nullable=True, comment='邮箱')
    full_name = Column(String(100), nullable=True, comment='真实姓名')
    
    # 用户状态
    is_active = Column(Boolean, default=True, comment='是否激活')
    is_admin = Column(Boolean, default=False, comment='是否管理员')
    is_banned = Column(Boolean, default=False, comment='是否被禁用')
    ban_until = Column(DateTime, nullable=True, comment='禁用到期时间')
    
    # 用户组和权限
    user_group = Column(Enum('admin', 'user', 'guest', name='user_group_enum'), 
                       default='user', comment='用户组')
    
    # 登录信息
    last_login = Column(DateTime, nullable=True, comment='最后登录时间')
    login_count = Column(Integer, default=0, comment='登录次数')
    failed_login_attempts = Column(Integer, default=0, comment='失败登录次数')
    last_failed_login = Column(DateTime, nullable=True, comment='最后失败登录时间')
    
    # 会话信息
    session_token = Column(String(128), nullable=True, comment='会话令牌')
    session_expires = Column(DateTime, nullable=True, comment='会话过期时间')
    
    # 统计信息
    download_count = Column(Integer, default=0, comment='下载次数')
    upload_count = Column(Integer, default=0, comment='上传次数')
    search_count = Column(Integer, default=0, comment='搜索次数')
    
    # 配额和限制 - 已移除
    # download_quota = Column(Integer, default=0, comment='下载配额(MB)')
    # used_quota = Column(Integer, default=0, comment='已用配额(MB)')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 备注
    notes = Column(Text, nullable=True, comment='备注')
    
    def __init__(self, username: str, password: str, **kwargs):
        self.username = username
        self.set_password(password)
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_password(self, password: str):
        """设置密码"""
        self.salt = secrets.token_hex(16)
        self.password_hash = self._hash_password(password, self.salt)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        return self.password_hash == self._hash_password(password, self.salt)
    
    def _hash_password(self, password: str, salt: str) -> str:
        """密码哈希 - 使用简单但安全的方法"""
        import hashlib

        # 使用SHA256 + 盐值的简单方法，兼容性更好
        combined = password + salt
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()
    
    def generate_session_token(self, expires_hours: int = 24) -> str:
        """生成会话令牌"""
        self.session_token = secrets.token_urlsafe(64)
        self.session_expires = datetime.now() + timedelta(hours=expires_hours)
        return self.session_token
    
    def is_session_valid(self) -> bool:
        """检查会话是否有效"""
        if not self.session_token or not self.session_expires:
            return False
        return datetime.now() < self.session_expires
    
    def clear_session(self):
        """清除会话"""
        self.session_token = None
        self.session_expires = None
    
    def is_banned_now(self) -> bool:
        """检查当前是否被禁用"""
        if not self.is_banned:
            return False
        if self.ban_until and datetime.now() > self.ban_until:
            # 禁用期已过，自动解除禁用
            self.is_banned = False
            self.ban_until = None
            return False
        return True
    
    def ban_user(self, duration_minutes: int = 0, permanent: bool = False):
        """禁用用户"""
        self.is_banned = True
        if permanent:
            self.ban_until = None
        elif duration_minutes > 0:
            self.ban_until = datetime.now() + timedelta(minutes=duration_minutes)
        else:
            self.ban_until = datetime.now() + timedelta(hours=24)  # 默认24小时
    
    def unban_user(self):
        """解除禁用"""
        self.is_banned = False
        self.ban_until = None
        self.failed_login_attempts = 0
    
    def record_login_success(self):
        """记录成功登录"""
        self.last_login = datetime.now()
        self.login_count = (self.login_count or 0) + 1
        self.failed_login_attempts = 0
    
    def record_login_failure(self):
        """记录失败登录"""
        self.failed_login_attempts = (self.failed_login_attempts or 0) + 1
        self.last_failed_login = datetime.now()
    
    def increment_download_count(self, size_mb: int = 0):
        """增加下载计数"""
        self.download_count = (self.download_count or 0) + 1
        # self.used_quota += size_mb  # 配额功能已被移除
    
    def increment_upload_count(self):
        """增加上传计数"""
        self.upload_count = (self.upload_count or 0) + 1
    
    def increment_search_count(self):
        """增加搜索计数"""
        self.search_count = (self.search_count or 0) + 1
    
    def can_download(self, size_mb: int = 0) -> bool:
        """检查是否可以下载"""
        # 配额功能已被移除，始终允许下载
        return True
    
    def get_permissions(self) -> list:
        """获取用户权限列表"""
        if self.is_admin:
            return ['read', 'write', 'delete', 'admin', 'upload', 'download']
        elif self.user_group == 'user':
            return ['read', 'download']
        elif self.user_group == 'guest':
            return ['read']  # 访客默认只有读取权限，没有下载权限
        else:
            return []
    
    def has_permission(self, permission: str) -> bool:
        """检查是否有指定权限"""
        return permission in self.get_permissions()
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """转换为字典"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'is_banned': self.is_banned_now(),
            'user_group': self.user_group,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'download_count': self.download_count,
            'upload_count': self.upload_count,
            'search_count': self.search_count,
            # 'download_quota': self.download_quota,  # 配额功能已被移除
            # 'used_quota': self.used_quota,  # 配额功能已被移除
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'permissions': self.get_permissions()
        }
        
        if include_sensitive:
            data.update({
                'failed_login_attempts': self.failed_login_attempts,
                'last_failed_login': self.last_failed_login.isoformat() if self.last_failed_login else None,
                'ban_until': self.ban_until.isoformat() if self.ban_until else None,
                'session_expires': self.session_expires.isoformat() if self.session_expires else None,
                'notes': self.notes
            })
        
        return data
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', group='{self.user_group}')>"
