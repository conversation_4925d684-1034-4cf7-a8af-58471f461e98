/**
 * 文件管理器核心模块
 * 处理文件列表、文件操作等核心功能
 */

class FileManager {
    constructor() {
        this.files = [];
        this.folders = [];
        this.currentFolder = null;
        this.selectedFiles = new Set();
        this.selectedForDownload = new Set(); // 添加批量下载选择集合
        this.currentViewMode = 'grid';
        this.currentLayoutMode = 'grid';
        this.currentSortField = 'name';
        this.currentSortOrder = 'asc';
        this.searchQuery = '';
        this.searchMode = false;
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalPages = 1;
        this.isLoading = false;
        this.isDownloading = false; // 添加下载状态标志
        this.currentView = 'files';
        
        // 初始化搜索相关属性 - 修复searchResults未定义的问题
        this.isInSearchMode = false;
        this.searchResults = [];
        
        // 初始化排序配置 - 修复sortBy未定义的问题
        this.sortBy = { 
            field: 'name', 
            order: 'asc' 
        };
        
        // 从存储中加载用户偏好设置
        this.loadUserPreferences();
        
        // 绑定方法
        this.handleFileAction = this.handleFileAction.bind(this);
        this.handleKeyboard = this.handleKeyboard.bind(this);
        
        // 防抖函数
        this.refreshThumbnail = Utils.debounce(this.refreshThumbnail.bind(this), 300);
        
        this.init();
    }

    /**
     * 绑定缩略图事件
     */
    bindThumbnailEvents() {
        // 使用事件代理监听缩略图加载事件
        Utils.event.on(document, 'load', (e) => {
            if (e.target.classList && e.target.classList.contains('thumbnail-image')) {
                this.onThumbnailLoad(e.target);
            }
        }, true);

        Utils.event.on(document, 'error', (e) => {
            if (e.target.classList && e.target.classList.contains('thumbnail-image')) {
                this.onThumbnailError(e.target);
            }
        }, true);
    }

    /**
     * 安全显示Toast消息
     */
    showToast(message, type = 'info') {
        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast[type](message);
        } else {
            CONFIG.log(type, message);
        }
    }

    /**
     * 显示缩略图管理界面
     */
    showThumbnailManagement() {
        const modal = Utils.dom.create('div', {
            className: 'modal-overlay',
            innerHTML: `
                <div class="modal-content thumbnail-management-modal">
                    <div class="modal-header">
                        <h3>缩略图管理</h3>
                        <button class="modal-close" data-action="close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="management-section">
                            <h4>当前文件夹操作</h4>
                            <div class="management-actions">
                                <button class="btn btn-primary" data-action="scan-current">
                                    <i class="fas fa-sync"></i>
                                    重新扫描并生成缩略图
                                </button>
                                <button class="btn btn-secondary" data-action="generate-current">
                                    <i class="fas fa-images"></i>
                                    仅生成缩略图
                                </button>
                                <button class="btn btn-warning" data-action="regenerate-current">
                                    <i class="fas fa-redo"></i>
                                    强制重新生成
                                </button>
                            </div>
                        </div>

                        <div class="management-section">
                            <h4>全局操作（管理员）</h4>
                            <div class="management-actions">
                                <button class="btn btn-info" data-action="generate-all">
                                    <i class="fas fa-globe"></i>
                                    为所有文件夹生成缩略图
                                </button>
                                <button class="btn btn-danger" data-action="regenerate-all">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    强制重新生成所有缩略图
                                </button>
                            </div>
                        </div>

                        <div class="management-section">
                            <h4>操作说明</h4>
                            <ul class="help-list">
                                <li><strong>重新扫描：</strong>扫描文件夹并为新文件生成缩略图</li>
                                <li><strong>仅生成缩略图：</strong>只为缺失缩略图的文件生成</li>
                                <li><strong>强制重新生成：</strong>删除现有缩略图并重新生成</li>
                                <li><strong>全局操作：</strong>对所有共享文件夹执行操作（需要管理员权限）</li>
                            </ul>
                        </div>

                        <div class="progress-section" style="display: none;">
                            <h4>操作进度</h4>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="progress-text">准备中...</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" data-action="close">关闭</button>
                    </div>
                </div>
            `
        });

        // 绑定事件
        Utils.event.on(modal, 'click', (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            if (!action) return;

            switch (action) {
                case 'close':
                    modal.remove();
                    break;
                case 'scan-current':
                    this.handleThumbnailAction('scan', false);
                    break;
                case 'generate-current':
                    this.handleThumbnailAction('generate', false);
                    break;
                case 'regenerate-current':
                    this.handleThumbnailAction('generate', true);
                    break;
                case 'generate-all':
                    this.handleThumbnailAction('generate-all', false);
                    break;
                case 'regenerate-all':
                    this.handleThumbnailAction('generate-all', true);
                    break;
            }
        });

        // 点击背景关闭
        Utils.event.on(modal, 'click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        document.body.appendChild(modal);
    }

    /**
     * 处理缩略图操作
     */
    async handleThumbnailAction(action, forceRegenerate = false) {
        const modal = Utils.dom.$('.thumbnail-management-modal');
        const progressSection = modal?.querySelector('.progress-section');
        const progressFill = modal?.querySelector('.progress-fill');
        const progressText = modal?.querySelector('.progress-text');

        if (!modal) return;

        try {
            // 显示进度
            if (progressSection) {
                progressSection.style.display = 'block';
                progressText.textContent = '正在处理...';
                progressFill.style.width = '0%';
            }

            let result;

            switch (action) {
                case 'scan':
                    if (!this.currentFolder) {
                        this.showToast('请先选择一个文件夹', 'warning');
                        return;
                    }
                    progressText.textContent = '正在扫描文件夹并生成缩略图...';
                    result = await FolderAPI.scanFolder(this.currentFolder.id, true);
                    break;

                case 'generate':
                    if (!this.currentFolder) {
                        this.showToast('请先选择一个文件夹', 'warning');
                        return;
                    }
                    progressText.textContent = forceRegenerate ? '正在强制重新生成缩略图...' : '正在生成缺失的缩略图...';
                    result = await FolderAPI.generateThumbnails(this.currentFolder.id, forceRegenerate);
                    break;

                case 'generate-all':
                    progressText.textContent = forceRegenerate ? '正在为所有文件夹强制重新生成缩略图...' : '正在为所有文件夹生成缩略图...';
                    result = await FolderAPI.generateAllThumbnails(forceRegenerate);
                    break;

                default:
                    this.showToast('未知操作', 'error');
                    return;
            }

            // 更新进度
            if (progressFill) {
                progressFill.style.width = '100%';
            }

            if (result.success) {
                // 显示成功信息
                let message = '操作完成！';
                if (result.generated !== undefined) {
                    message += ` 生成了 ${result.generated} 个缩略图`;
                }
                if (result.failed !== undefined && result.failed > 0) {
                    message += `，失败 ${result.failed} 个`;
                }
                if (result.total_generated !== undefined) {
                    message += ` 总共生成 ${result.total_generated} 个缩略图`;
                }

                this.showToast(message, 'success');
                progressText.textContent = message;

                // 刷新文件列表以显示新的缩略图
                setTimeout(() => {
                    this.loadFiles();
                }, 1000);

            } else {
                this.showToast(`操作失败: ${result.error || '未知错误'}`, 'error');
                progressText.textContent = `操作失败: ${result.error || '未知错误'}`;
            }

        } catch (error) {
            CONFIG.log('error', '缩略图操作失败:', error);
            this.showToast(`操作失败: ${error.message}`, 'error');
            if (progressText) {
                progressText.textContent = `操作失败: ${error.message}`;
            }
        } finally {
            // 隐藏进度（延迟3秒）
            setTimeout(() => {
                if (progressSection) {
                    progressSection.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        try {
            // 加载视图模式
            this.viewMode = Utils.storage.get(CONFIG.STORAGE_KEYS.VIEW_MODE) || 'large-icons';
            this.layoutMode = Utils.storage.get(CONFIG.STORAGE_KEYS.LAYOUT_MODE) || 'grid';
            
            // 加载排序设置
            const savedSortBy = Utils.storage.get(CONFIG.STORAGE_KEYS.SORT_ORDER);
            if (savedSortBy && savedSortBy.field && savedSortBy.order) {
                this.sortBy = savedSortBy;
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to load user preferences:', error);
            // 使用默认设置
            this.viewMode = 'large-icons';
            this.layoutMode = 'grid';
            this.sortBy = { field: 'name', order: 'asc' };
        }
    }

    /**
     * 初始化文件管理器
     */
    init() {
        this.bindEvents();
        this.bindThumbnailEvents();
        this.loadFiles();
        this.setupViewMode();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 布局切换
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                const layoutMode = e.target.closest('.layout-btn').dataset.layout;
                if (layoutMode) {
                    this.setLayoutMode(layoutMode);
                }
            });
        });

        // 视图切换
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.event.on(btn, 'click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const viewMode = e.target.closest('.view-btn').dataset.view;
                CONFIG.log('info', `视图按钮点击: ${viewMode}, 当前搜索模式: ${this.isInSearchMode}`);
                if (viewMode) {
                    this.setViewMode(viewMode);
                }
            });
        });
        
        // 排序
        const sortSelect = Utils.dom.$('#sort-select');
        if (sortSelect) {
            Utils.event.on(sortSelect, 'change', (e) => {
                const [field, order] = e.target.value.split('-');
                this.setSortOrder(field, order || 'asc');
            });
        }

        // 缩略图管理
        const thumbnailManagementBtn = Utils.dom.$('#thumbnail-management-btn');
        if (thumbnailManagementBtn) {
            Utils.event.on(thumbnailManagementBtn, 'click', (e) => {
                e.preventDefault();
                this.showThumbnailManagement();
            });
        }
        
        // 文件夹导航 - 双击进入文件夹
        Utils.event.on(document, 'dblclick', (e) => {
            const folderItem = e.target.closest('.folder-item');
            if (folderItem) {
                const folderId = folderItem.dataset.fileId;
                this.navigateToFolder(folderId);
            }
        });
        
        // 面包屑导航
        Utils.event.on(document, 'click', (e) => {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem) {
                e.preventDefault();

                // 如果在搜索模式下，清除搜索状态
                if (this.isInSearchMode) {
                    CONFIG.log('info', '面包屑导航：清除搜索模式');
                    this.clearSearchMode();
                }

                const folderId = breadcrumbItem.dataset.folderId;
                this.navigateToFolder(folderId || null);
            }
        });
        
        // 文件选择
        Utils.event.on(document, 'click', (e) => {
            // 如果点击的是操作按钮或其子元素，不处理文件选择
            if (e.target.closest('.action-btn')) {
                return;
            }

            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                this.handleFileClick(fileItem, e);
            }
        });
        
        // 右键菜单
        Utils.event.on(document, 'contextmenu', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                e.preventDefault();
                this.showContextMenu(e.clientX, e.clientY, fileItem);
            }
        });
        
        // 双击文件 - 避免与文件夹双击冲突
        Utils.event.on(document, 'dblclick', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem && !fileItem.classList.contains('folder-item')) {
                this.handleFileDoubleClick(fileItem);
            }
        });

        // 文件操作按钮点击事件
        Utils.event.on(document, 'click', (e) => {
            console.log('=== 全局点击事件触发 ===');
            console.log('点击目标:', e.target);
            console.log('点击目标类名:', e.target.className);
            console.log('点击目标父元素:', e.target.parentElement);

            const actionBtn = e.target.closest('.action-btn');
            console.log('找到的操作按钮:', actionBtn);

            if (actionBtn) {
                console.log('操作按钮详情:', {
                    className: actionBtn.className,
                    dataset: actionBtn.dataset,
                    innerHTML: actionBtn.innerHTML
                });

                e.preventDefault();
                e.stopPropagation();

                const action = actionBtn.dataset.action;
                const fileItem = actionBtn.closest('.file-item');
                const fileId = fileItem?.dataset.fileId;

                console.log('=== 操作按钮点击详情 ===');
                console.log('action:', action);
                console.log('fileId:', fileId);
                console.log('fileItem:', fileItem);

                CONFIG.log('info', `操作按钮点击: action=${action}, fileId=${fileId}, timestamp=${Date.now()}`);

                if (!fileId) {
                    CONFIG.log('error', '未找到文件ID');
                    console.error('未找到文件ID，fileItem:', fileItem);
                    return;
                }

                switch (action) {
                    case 'preview':
                        console.log('=== 执行预览操作 ===');
                        CONFIG.log('info', `执行预览: ${fileId}`);
                        this.previewFile(fileId);
                        break;

                    case 'open':
                        CONFIG.log('info', `执行打开: ${fileId}`);
                        this.navigateToFolder(fileId);
                        break;
                    // 文件夹下载功能已移除
                    default:
                        CONFIG.log('warn', `未知操作: ${action}`);
                        console.warn('未知操作:', action);
                }
            } else {
                // 如果不是操作按钮，检查是否是图标点击
                if (e.target.tagName === 'I' && e.target.classList.contains('fa-eye')) {
                    console.log('=== 直接点击了眼睛图标 ===');
                    console.log('眼睛图标父元素:', e.target.parentElement);
                    const btn = e.target.parentElement;
                    if (btn && btn.classList.contains('action-btn')) {
                        console.log('触发父按钮点击');
                        btn.click();
                    }
                }
            }
        });

        // 侧边栏菜单点击
        Utils.event.on(document, 'click', (e) => {
            const menuLink = e.target.closest('.menu-item a[data-view]');
            if (menuLink) {
                e.preventDefault();
                const view = menuLink.dataset.view;
                CONFIG.log('info', `侧边栏菜单点击: ${view}`);

                switch (view) {

                    case 'home':
                        this.navigateToFolder(null);
                        break;
                    case 'downloads':
                        // 禁用原有处理，使用新的简单实现
                        // this.switchView('downloads');
                        console.log('下载记录点击被简单下载管理器处理');
                        break;
                    default:
                        CONFIG.log('warn', `未知的菜单视图: ${view}`);
                }
            }
        });

        // 文件复选框事件
        Utils.event.on(document, 'change', (e) => {
            if (e.target.classList.contains('file-checkbox')) {
                e.stopPropagation();
                const fileId = e.target.dataset.fileId;
                CONFIG.log('debug', `复选框变化: fileId=${fileId}, checked=${e.target.checked}`);
                this.handleFileCheckboxChange(fileId, e.target.checked);
            }
        });

        // 键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    

    

    
    /**
     * 加载文件列表
     */
    async loadFiles(folderId = null) {
        try {
            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.show(folderId ? '正在加载文件...' : '正在加载文件夹...');
            }

            CONFIG.log('info', `Loading files for folder: ${folderId}`);

            // 检查认证状态
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                this.showToast('请先登录', 'error');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                return;
            }

            this.currentFolderId = folderId;
            this.isInFolder = !!folderId;
            this.isShowingFavorites = false;

            let files = [];
            let response = null;

            if (this.isInFolder) {
                // 在文件夹内：获取文件夹中的文件
                response = await FileAPI.getFiles(folderId);
                CONFIG.log('info', 'Files API Response:', response);

                // 处理不同的响应格式，确保files是数组
                if (response && response.files && Array.isArray(response.files)) {
                    files = response.files;
                } else if (Array.isArray(response)) {
                    files = response;
                } else if (response && response.data && Array.isArray(response.data)) {
                    files = response.data;
                } else {
                    files = []; // 确保files是数组
                    CONFIG.log('warn', 'API返回了非预期的响应格式:', response);
                }

                // 在文件夹内：显示所有图片文件
                files = this.filterAllowedFiles(files);
            } else {
                // 在首页：获取共享文件夹列表
                const folders = await FolderAPI.getSharedFolders();
                CONFIG.log('info', 'Folders API Response:', folders);

                // 确保folders是数组，然后转换为文件格式
                if (Array.isArray(folders)) {
                    files = folders.map(folder => ({
                        id: folder.id,
                        name: folder.name,
                        type: 'folder',
                        size: folder.statistics?.total_size || 0,
                        modified_at: folder.updated_at || folder.created_at,
                        file_count: folder.statistics?.file_count || folder.file_count || 0,
                        path: folder.path
                    }));
                } else {
                    files = []; // 确保files是数组
                    CONFIG.log('warn', '获取文件夹列表失败或返回非数组格式:', folders);
                }
            }

            CONFIG.log('info', `Processed ${files.length} items`);

            this.files = files;
            this.currentFolder = response?.folder || null;

            this.renderFiles();
            this.updateBreadcrumb();

            // 显示加载结果（只在首次加载或文件夹变化时显示）
            if (!this._lastLoadedFolderId || this._lastLoadedFolderId !== folderId) {
                // 只在真正需要时显示Toast消息
                if (files.length === 0) {
                    // 空状态消息已经在renderEmptyState中处理，这里不再显示Toast
                    CONFIG.log('info', this.isInFolder ? '当前文件夹没有图片文件' : '没有共享文件夹');
                } else {
                    // 只在成功加载文件时显示成功消息
                    if (this.isInFolder) {
                        this.showToast(`加载了 ${files.length} 个文件`, 'success');
                    } else {
                        this.showToast(`找到 ${files.length} 个文件夹`, 'success');
                    }
                }
                this._lastLoadedFolderId = folderId;
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load files:', error);

            // 更详细的错误信息
            let errorMessage = '加载失败';
            let isNetworkAccessDenied = false;

            if (error.userMessage) {
                errorMessage = error.userMessage;
            } else if (error.message) {
                if (error.message.includes('401')) {
                    errorMessage = '请先登录';
                } else if (error.message.includes('403')) {
                    // 检查是否是网络访问控制错误
                    if (error.message.includes('内网访问已被禁用') ||
                        error.message.includes('外网访问已被禁用') ||
                        error.message.includes('访问被拒绝')) {
                        isNetworkAccessDenied = true;
                        errorMessage = '网络访问已被管理员禁用';
                    } else {
                        errorMessage = '没有访问权限';
                    }
                } else if (error.message.includes('404')) {
                    errorMessage = '文件夹不存在';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '无法连接到服务器';
                }
            }

            // 如果是网络访问被拒绝，静默处理 - 显示空的文件列表，不显示错误
            if (isNetworkAccessDenied) {
                this.files = [];
                this.renderEmptyState('暂无可访问的文件', '当前没有可显示的内容');
            } else {
                this.showToast(errorMessage, 'error');

                // 如果是认证错误，跳转到登录页
                if (error.message && (error.message.includes('401') || error.message.includes('请先登录'))) {
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                }
            }
        } finally {
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }
        }
    }



    /**
     * 生成缩略图HTML
     */
    generateThumbnailHTML(file, icon) {
        // 使用API缩略图
        const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');
        const uniqueId = `thumb-${file.id}-${Date.now()}`;

        return `
            <div class="thumbnail-container" id="${uniqueId}">
                <div class="thumbnail-loading">
                    <div class="loading-spinner-small"></div>
                </div>
                <img src="${thumbnailUrl}"
                     alt="${file.name}"
                     class="thumbnail-image"
                     style="display:none;"
                     data-file-id="${file.id}"
                     data-file-name="${file.name}"
                     onload="window.safeThumbnailLoad(this)"
                     onerror="window.safeThumbnailError(this)">
                <div class="thumbnail-fallback" style="display:none;">
                    <i class="${icon}"></i>
                </div>
            </div>
        `;
    }

    /**
     * 缩略图加载成功处理
     */
    onThumbnailLoad(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        if (loading) loading.style.display = 'none';
        img.style.display = 'block';

        CONFIG.log('info', `缩略图加载成功: ${img.dataset.fileName}`);
    }

    /**
     * 缩略图加载失败处理
     */
    onThumbnailError(img) {
        const container = img.parentElement;
        const loading = container.querySelector('.thumbnail-loading');
        const fallback = container.querySelector('.thumbnail-fallback');

        if (loading) loading.style.display = 'none';
        if (fallback) fallback.style.display = 'flex';

        CONFIG.log('warn', `缩略图加载失败: ${img.dataset.fileName}, URL: ${img.src}`);

        // 尝试重新加载（添加时间戳避免缓存）
        setTimeout(() => {
            const newUrl = img.src.includes('&retry=') ?
                img.src : `${img.src}&retry=${Date.now()}`;

            if (!img.dataset.retried) {
                img.dataset.retried = 'true';
                img.src = newUrl;
                CONFIG.log('info', `重试缩略图加载: ${img.dataset.fileName}`);
            }
        }, 1000);
    }

    /**
     * 预加载缩略图
     */
    preloadThumbnails(files) {
        // 确保files是数组
        if (!Array.isArray(files)) {
            CONFIG.log('warn', 'preloadThumbnails接收到非数组参数:', files);
            return;
        }

        // 只预加载图片文件的缩略图
        const imageFiles = files.filter(file =>
            file && file.name && Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        // 限制同时预加载的数量，避免过多请求
        const maxConcurrent = 6;
        let currentIndex = 0;

        const loadNext = () => {
            if (currentIndex >= imageFiles.length) return;

            const file = imageFiles[currentIndex++];
            if (!file || !file.id) {
                loadNext();
                return;
            }

            const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');

            // 创建隐藏的图片元素来预加载
            const img = new Image();
            img.onload = () => {
                CONFIG.log('info', `缩略图预加载成功: ${file.name}`);
                loadNext();
            };
            img.onerror = () => {
                CONFIG.log('warn', `缩略图预加载失败: ${file.name}`);
                loadNext();
            };
            img.src = thumbnailUrl;
        };

        // 启动并发预加载
        for (let i = 0; i < Math.min(maxConcurrent, imageFiles.length); i++) {
            loadNext();
        }
    }

    /**
     * 过滤允许的文件 - 只显示图片格式
     */
    filterAllowedFiles(files) {
        // 确保files是数组
        if (!Array.isArray(files)) {
            CONFIG.log('warn', 'filterAllowedFiles接收到非数组参数:', files);
            return [];
        }

        return files.filter(file => {
            // 确保file对象存在
            if (!file || typeof file !== 'object') {
                return false;
            }

            // 文件夹总是显示
            if (file.type === 'folder') {
                return true;
            }

            // 确保文件名存在
            if (!file.name) {
                return false;
            }

            // 只显示允许的图片格式
            return CONFIG.FILES.isAllowedFile(file.name);
        });
    }
    
    /**
     * 渲染文件列表
     */
    renderFiles() {
        const sortedFiles = this.sortFiles(this.files);

        // 根据视图模式渲染
        switch (this.viewMode) {
            case 'extra-large-icons':
            case 'large-icons':
            case 'medium-icons':
            case 'small-icons':
                this.renderIconView(sortedFiles);
                break;
            default:
                this.renderIconView(sortedFiles);
        }

        // 更新视图模式按钮状态
        this.updateViewModeButtons();

        // 确保批量操作按钮存在（仅在文件夹内显示）
        if (this.isInFolder) {
            this.ensureBatchActionButtons();
        }
    }
    
    /**
     * 渲染图标视图
     */
    renderIconView(files) {
        const container = Utils.dom.$('#file-grid');
        if (!container) return;

        container.innerHTML = '';
        Utils.dom.show(container);
        Utils.dom.hide(Utils.dom.$('#file-list'));

        // 设置容器的CSS类以控制图标大小和布局
        let containerClasses = ['file-grid'];

        // 添加布局模式类
        if (this.layoutMode === 'horizontal' && !this.isInFolder) {
            // 在首页且为横向布局时，使用横向布局
            containerClasses.push('horizontal-layout');
        } else if (this.isInFolder) {
            // 在文件夹内，使用指定的视图模式
            containerClasses.push(this.viewMode);
        }
        // 在首页且为网格布局时，使用默认样式（不添加额外类）

        container.className = containerClasses.join(' ');

        // 如果没有文件，显示空状态
        if (files.length === 0) {
            this.renderEmptyState(container);
            return;
        }

        files.forEach(file => {
            const fileElement = this.createFileIconItem(file);
            container.appendChild(fileElement);
        });

        // 预加载缩略图
        this.preloadThumbnails(files);
    }

    /**
     * 渲染空状态
     */
    renderEmptyState(containerOrTitle, description = null) {
        // 如果第一个参数是字符串，说明是自定义标题和描述
        if (typeof containerOrTitle === 'string') {
            const container = Utils.dom.$('#file-grid');
            if (!container) return;

            const emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-info-circle"></i>
                    <h3>${containerOrTitle}</h3>
                    <p>${description || ''}</p>
                </div>
            `;
            container.innerHTML = emptyStateHTML;
            return;
        }

        // 原有逻辑：第一个参数是容器元素
        const container = containerOrTitle;
        let emptyStateHTML = '';

        if (this.isInSearchMode) {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h3>未找到匹配的文件</h3>
                    <p>请尝试其他搜索关键词</p>
                </div>
            `;
        } else if (this.isInFolder) {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>文件夹为空</h3>
                    <p>当前文件夹没有图片文件</p>
                </div>
            `;
        } else {
            emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder"></i>
                    <h3>暂无共享文件夹</h3>
                    <p>管理员还没有设置共享文件夹</p>
                </div>
            `;
        }

        container.innerHTML = emptyStateHTML;
    }

    /**
     * 渲染网络访问被拒绝状态
     */
    renderNetworkAccessDeniedState() {
        const container = Utils.dom.$('#file-grid');
        if (!container) return;

        const accessDeniedHTML = `
            <div class="access-denied-state">
                <div class="access-denied-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3>网络访问已被禁用</h3>
                <p>管理员已禁用网络访问功能</p>
                <div class="access-denied-details">
                    <p>• 当前无法访问文件和文件夹</p>
                    <p>• 请联系管理员开启网络访问权限</p>
                    <p>• 或在本地环境下使用系统</p>
                </div>
                <div class="access-denied-actions">
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i>
                        刷新页面
                    </button>
                </div>
            </div>
        `;

        container.innerHTML = accessDeniedHTML;
    }
    

    
    /**
     * 创建图标文件项
     */
    createFileIconItem(file) {
        const isImage = Utils.isImageFile(file.name);
        const isFolder = file.type === 'folder';
        const icon = CONFIG.FILES.getFileIcon(file.name, isFolder);


        // 对所有图片文件显示缩略图（不限制在文件夹内）
        const showThumbnail = isImage && !isFolder;



        const element = Utils.dom.create('div', {
            className: `file-item ${isFolder ? 'folder-item' : 'file-item'}`,
            'data-file-id': file.id,
            'data-file-type': file.type,
            innerHTML: `
                ${!isFolder ? `
                    <div class="file-checkbox-wrapper">
                        <input type="checkbox" class="file-checkbox" data-file-id="${file.id}">
                    </div>
                ` : ''}
                <div class="file-icon">
                    ${showThumbnail ?
                        this.generateThumbnailHTML(file, icon) :
                        `<i class="${icon}"></i>`
                    }
                </div>
                <div class="file-name" title="${file.name}">${file.name}</div>
                ${!isFolder && this.viewMode !== 'small-icons' ? `
                    <div class="file-meta">
                        <span class="file-size">${Utils.formatFileSize(file.size)}</span>
                    </div>
                ` : ''}
                <div class="file-actions">
                    ${isFolder ? `
                        <button class="action-btn" data-action="open" title="打开">
                            <i class="fas fa-folder-open"></i>
                        </button>
                    ` : `
                        <button class="action-btn" data-action="preview" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>

                    `}
                </div>
            `
        });

        return element;
    }
    

    
    /**
     * 获取文件类型文本 - 专门针对图片文件
     */
    getFileTypeText(filename) {
        const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));

        // 根据扩展名返回具体的图片类型
        const imageTypeMap = {
            '.jpg': 'JPEG图片',
            '.jpeg': 'JPEG图片',
            '.png': 'PNG图片',
            '.psd': 'Photoshop文档',
            '.tif': 'TIFF图片',
            '.tiff': 'TIFF图片',
            '.ai': 'Illustrator文档',
            '.eps': 'EPS矢量图',
            '.gif': 'GIF动图',
            '.bmp': 'BMP图片',
            '.webp': 'WebP图片',
            '.svg': 'SVG矢量图'
        };

        return imageTypeMap[ext] || '图片文件';
    }
    
    /**
     * 排序文件
     */
    sortFiles(files) {
        return [...files].sort((a, b) => {
            // 文件夹优先
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            
            let aValue, bValue;
            
            switch (this.sortBy.field) {
                case 'name':
                    aValue = a.name.toLowerCase();
                    bValue = b.name.toLowerCase();
                    break;
                case 'size':
                    aValue = a.size || 0;
                    bValue = b.size || 0;
                    break;
                case 'date':
                    aValue = new Date(a.modified_at);
                    bValue = new Date(b.modified_at);
                    break;
                case 'type':
                    aValue = this.getFileTypeText(a.name);
                    bValue = this.getFileTypeText(b.name);
                    break;
                default:
                    return 0;
            }
            
            if (aValue < bValue) return this.sortBy.order === 'asc' ? -1 : 1;
            if (aValue > bValue) return this.sortBy.order === 'asc' ? 1 : -1;
            return 0;
        });
    }
    
    /**
     * 设置视图模式
     */
    setViewMode(mode) {
        CONFIG.log('info', `设置视图模式: ${mode}, 搜索模式: ${this.isInSearchMode}, 搜索结果数量: ${this.searchResults ? this.searchResults.length : 0}`);

        this.viewMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.VIEW_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults && Array.isArray(this.searchResults) && this.searchResults.length > 0) {
            CONFIG.log('info', '保持搜索结果，不重新加载文件');
            this.files = this.searchResults;
        } else {
            CONFIG.log('info', '非搜索模式或无搜索结果，使用当前文件列表');
        }

        this.renderFiles();
    }

    /**
     * 设置布局模式
     */
    setLayoutMode(mode) {
        this.layoutMode = mode;
        Utils.storage.set(CONFIG.STORAGE_KEYS.LAYOUT_MODE, mode);

        // 更新按钮状态
        Utils.dom.$$('.layout-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.layout === mode) {
                Utils.dom.addClass(btn, 'active');
            }
        });

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults && Array.isArray(this.searchResults) && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置排序方式
     */
    setSortOrder(field, order = 'asc') {
        this.sortBy = { field, order };
        Utils.storage.set(CONFIG.STORAGE_KEYS.SORT_ORDER, this.sortBy);

        // 如果在搜索模式下，保持搜索结果
        if (this.isInSearchMode && this.searchResults && Array.isArray(this.searchResults) && this.searchResults.length > 0) {
            this.files = this.searchResults;
        }

        this.renderFiles();
    }
    
    /**
     * 设置视图模式
     */
    setupViewMode() {
        this.setViewMode(this.viewMode);
        this.setLayoutMode(this.layoutMode);
    }
    
    /**
     * 导航到文件夹
     */
    navigateToFolder(folderId) {
        // 重置加载标志，允许显示新的Toast消息
        this._lastLoadedFolderId = null;

        // 如果在搜索模式下，清除搜索状态
        if (this.isInSearchMode) {
            this.clearSearchMode();
        }



        this.selectedFiles.clear();
        this.loadFiles(folderId);
    }

    /**
     * 清除搜索模式
     */
    clearSearchMode() {
        this.isInSearchMode = false;
        this.searchResults = [];

        // 同时清除搜索管理器的状态
        if (window.searchManager) {
            window.searchManager.isInSearchMode = false;
            window.searchManager.searchResults = [];
        }
    }

    /**
     * 更新视图模式按钮状态
     */
    updateViewModeButtons() {
        // 更新按钮状态
        Utils.dom.$$('.view-btn').forEach(btn => {
            Utils.dom.removeClass(btn, 'active');
            if (btn.dataset.view === this.viewMode) {
                Utils.dom.addClass(btn, 'active');
            }
        });
    }
    
    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(items = null) {
        const container = Utils.dom.$('.breadcrumb-nav');
        if (!container) return;

        // 如果传入了自定义items，使用新的格式
        if (items && Array.isArray(items)) {
            let html = '';
            items.forEach((item, index) => {
                if (index === items.length - 1) {
                    html += `
                        <span class="breadcrumb-item current">
                            <i class="${item.icon}"></i>
                            ${item.name}
                        </span>
                    `;
                } else {
                    html += `
                        <a href="#" class="breadcrumb-item" onclick="fileManager.switchView('${item.view || 'home'}')">
                            <i class="${item.icon}"></i>
                            ${item.name}
                        </a>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    `;
                }
            });
            container.innerHTML = html;
            return;
        }

        // 默认的文件浏览面包屑
        container.innerHTML = `
            <a href="#" class="breadcrumb-item" data-folder-id="">
                <i class="fas fa-home"></i>
                首页
            </a>
        `;

        if (this.isInFolder && this.currentFolder) {
            container.innerHTML += ' <span class="breadcrumb-separator">/</span> ';
            const folderLink = Utils.dom.create('a', {
                className: 'breadcrumb-item active',
                'data-folder-id': this.currentFolder.id,
                textContent: this.currentFolder.name
            });
            container.appendChild(folderLink);
        }
    }
    
    /**
     * 处理文件点击
     */
    handleFileClick(fileItem, event) {
        const fileId = fileItem.dataset.fileId;
        
        if (event.ctrlKey || event.metaKey) {
            // 多选
            this.toggleFileSelection(fileId, fileItem);
        } else {
            // 单选
            this.clearSelection();
            this.selectFile(fileId, fileItem);
        }
    }
    
    /**
     * 处理文件双击
     */
    handleFileDoubleClick(fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        if (fileType === 'folder') {
            this.navigateToFolder(fileId);
        } else {
            this.previewFile(fileId);
        }
    }
    
    /**
     * 选择文件
     */
    selectFile(fileId, fileItem) {
        this.selectedFiles.add(fileId);
        Utils.dom.addClass(fileItem, 'selected');
    }
    
    /**
     * 切换文件选择状态
     */
    toggleFileSelection(fileId, fileItem) {
        if (this.selectedFiles.has(fileId)) {
            this.selectedFiles.delete(fileId);
            Utils.dom.removeClass(fileItem, 'selected');
        } else {
            this.selectedFiles.add(fileId);
            Utils.dom.addClass(fileItem, 'selected');
        }
    }
    
    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedFiles.clear();
        Utils.dom.$$('.file-item.selected').forEach(item => {
            Utils.dom.removeClass(item, 'selected');
        });
    }
    
    /**
     * 显示右键菜单
     */
    showContextMenu(x, y, fileItem) {
        const fileType = fileItem.dataset.fileType;
        const fileId = fileItem.dataset.fileId;
        
        const menuItems = [
            {
                icon: 'fas fa-download',
                text: '下载',
                action: 'download',
                handler: () => this.downloadFile(fileId)
            },
            {
                icon: 'fas fa-eye',
                text: '预览',
                action: 'preview',
                handler: () => this.previewFile(fileId)
            },

            {
                icon: 'fas fa-share',
                text: '分享',
                action: 'share',
                handler: () => this.shareFile(fileId)
            },
            { divider: true },
            {
                icon: 'fas fa-info-circle',
                text: '详细信息',
                action: 'info',
                handler: () => this.showFileInfo(fileId)
            }
        ];
        
        if (typeof Components !== 'undefined' && Components.ContextMenu) {
            Components.ContextMenu.show(x, y, menuItems);
        }
    }
    
    /**
     * 处理文件操作
     */
    handleFileAction(action, file) {
        const fileId = file.id;

        switch (action) {
            case 'download':
                this.downloadFile(fileId);
                break;
            case 'preview':
                this.previewFile(fileId);
                break;
            case 'open':
                if (file.type === 'folder') {
                    this.navigateToFolder(fileId);
                }
                break;
            default:
                CONFIG.log('warn', `Unknown file action: ${action}`);
        }
    }

    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        try {
            // 检查当前文件夹的下载权限
            if (this.currentFolder && !this.currentFolder.allow_download) {
                this.showToast('⚠️ 后台关闭下载', 'error');
                CONFIG.log('info', '单文件下载被拒绝：当前文件夹禁止下载');
                return;
            }

            // 获取正确的认证token
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            // 使用新的单文件下载接口，确保所有下载都是压缩包形式
            const response = await fetch(`${api.getBaseURL()}/download/single/${fileId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 423) {
                // 文件被加密，需要申请密码
                const errorData = await response.json();
                const fileName = errorData.filename || `文件_${fileId}`;
                this.showPasswordRequestDialog(fileId, fileName);
                return;
            }

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                const downloadData = result.data;

                if (downloadData.is_encrypted) {
                    // 文件已加密，显示密码申请对话框
                    this.showPasswordRequestDialog(fileId, downloadData.filename);
                } else {
                    // 文件未加密，直接下载
                    // downloadData.download_url 已经包含 /api 前缀
                    const downloadUrl = downloadData.download_url.startsWith('http')
                        ? downloadData.download_url
                        : downloadData.download_url.startsWith('/api')
                        ? `${window.location.protocol}//${window.location.host}${downloadData.download_url}`
                        : `${api.getBaseURL()}${downloadData.download_url}`;
                    Utils.url.downloadFile(downloadUrl, downloadData.filename);
                    this.showToast('文件下载成功', 'success');

                    // 刷新下载记录
                    this.refreshDownloadRecords();
                }
            } else {
                throw new Error(result.error || '下载失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Download failed:', error);

            // 解析错误信息
            let errorMessage = error.message || '下载失败';

            // 检查是否是下载权限错误
            if (errorMessage.includes('后台关闭下载')) {
                this.showToast('⚠️ 后台关闭下载', 'error');
                CONFIG.log('info', '下载被拒绝：下载权限不足');
                return; // 下载权限错误不显示网络访问对话框
            }
            // 检查是否是文件不存在错误
            else if (errorMessage.includes('文件不存在于磁盘') || errorMessage.includes('文件不存在')) {
                this.showToast('⚠️ 文件已被移动或删除，请刷新页面', 'error');
                CONFIG.log('info', '下载失败：文件不存在');
                // 自动刷新文件列表
                setTimeout(() => {
                    this.refresh();
                }, 2000);
                return;
            }
            // 检查是否是网络访问权限错误
            else if (errorMessage.includes('内网访问已被禁用') ||
                errorMessage.includes('外网访问已被禁用') ||
                errorMessage.includes('403')) {
                this.showToast('⚠️ 网络访问被禁止 - 当前文件夹不允许从您的网络位置下载文件', 'error');
                CONFIG.log('info', '下载被拒绝：网络访问权限不足');
                this.showNetworkAccessDialog();
            } else if (errorMessage.includes('⚠️')) {
                // 如果错误信息已经包含警告符号，直接显示
                this.showToast(errorMessage, 'error');
            } else if (errorMessage.includes('下载失败: 400')) {
                // 处理通用400错误
                this.showToast('⚠️ 下载请求无效 - 请刷新页面后重试，如问题持续请联系管理员', 'error');
            } else if (errorMessage.includes('下载失败: 404')) {
                // 处理404错误
                this.showToast('⚠️ 文件不存在 - 文件可能已被删除或移动，请刷新页面', 'error');
            } else if (errorMessage.includes('下载失败: 500')) {
                // 处理500错误
                this.showToast('⚠️ 服务器错误 - 下载服务暂时不可用，请稍后重试', 'error');
            } else {
                // 其他错误
                this.showToast('文件下载失败: ' + errorMessage, 'error');
            }
        }
    }
    
    /**
     * 预览文件
     */
    async previewFile(fileId) {
        try {
            console.log('=== previewFile 开始 ===');
            console.log('fileId:', fileId, 'typeof:', typeof fileId);
            console.log('this.files:', this.files);

            // 尝试多种匹配方式
            let file = this.files.find(f => f.id == fileId); // 使用 == 进行类型转换匹配

            console.log('找到的文件:', file);

            // 调试：显示所有文件的ID和类型
            console.log('所有文件的ID:', this.files.map(f => ({ id: f.id, type: typeof f.id, name: f.name })));

            if (!file) {
                console.error('未找到文件，fileId:', fileId);
                return;
            }

            console.log('文件名:', file.name);
            console.log('Utils.isImageFile 检查结果:', Utils.isImageFile(file.name));

            // 如果是图片文件，使用浮窗预览
            if (Utils.isImageFile(file.name)) {
                console.log('确认是图片文件，调用 showImagePreviewFloat');
                this.showImagePreviewFloat(fileId, file);
                return;
            } else {
                console.log('不是图片文件，使用模态框预览');
            }

            // 非图片文件使用原有的模态框预览
            const previewModal = Utils.dom.$('#preview-modal');
            const previewTitle = Utils.dom.$('#preview-title');
            const previewContainer = Utils.dom.$('#preview-container');

            if (!previewModal || !previewTitle || !previewContainer) return;

            previewTitle.textContent = file.name;
            previewContainer.innerHTML = '<div class="loading-spinner"><div class="spinner"></div></div>';

            if (typeof Components !== 'undefined' && Components.Modal) {
                Components.Modal.show('preview-modal');
            }

            if (Utils.isVideoFile(file.name)) {
                const video = Utils.dom.create('video', {
                    controls: true,
                    src: `${api.getBaseURL()}/files/stream/${fileId}`,
                    style: 'max-width: 100%; max-height: 80vh;'
                });
                previewContainer.innerHTML = '';
                previewContainer.appendChild(video);
            } else {
                previewContainer.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-file"></i>
                        <p>此文件类型不支持预览</p>
                    </div>
                `;
            }
        } catch (error) {
            CONFIG.log('error', 'Preview failed:', error);

            // 检查是否是网络访问权限错误，如果是则静默处理
            if (error.message && (
                error.message.includes('内网访问已被禁用') ||
                error.message.includes('外网访问已被禁用') ||
                error.message.includes('403')
            )) {
                // 静默处理权限错误，不显示Toast
                CONFIG.log('info', '预览被拒绝：网络访问权限不足');
            } else {
                this.showToast('文件预览失败', 'error');
            }
        }
    }

    /**
     * 显示图片预览浮窗
     */
    async showImagePreviewFloat(fileId, file) {
        try {
            console.log('=== showImagePreviewFloat 开始 ===');
            console.log('文件ID:', fileId);
            console.log('文件信息:', file);
            console.log('typeof Components:', typeof Components);
            console.log('Components:', Components);
            console.log('Components.ImagePreviewFloat:', Components ? Components.ImagePreviewFloat : 'Components未定义');

            // 检查浮窗组件是否可用
            if (typeof Components === 'undefined' || !Components.ImagePreviewFloat) {
                console.warn('ImagePreviewFloat component not available, fallback to modal');
                CONFIG.log('warn', 'ImagePreviewFloat component not available, fallback to modal');
                this.createImagePreview(fileId, file, Utils.dom.$('#preview-container'));
                return;
            }

            console.log('浮窗组件可用，开始获取图片URL');

            // 获取图片URL
            const imageUrl = await this.getImageUrlWithAuth(fileId);
            console.log('图片URL获取成功:', imageUrl);

            // 显示浮窗
            console.log('调用 Components.ImagePreviewFloat.show');
            Components.ImagePreviewFloat.show(imageUrl, file.name, fileId);

            // 更新浮窗中的收藏按钮状态
            setTimeout(() => {
                console.log('更新收藏按钮状态');
                Components.ImagePreviewFloat.updateFavoriteButton();
            }, 100);

            CONFIG.log('info', `图片预览浮窗已显示: ${file.name}`);
            console.log('=== showImagePreviewFloat 完成 ===');

        } catch (error) {
            console.error('showImagePreviewFloat 错误:', error);
            CONFIG.log('error', 'Failed to show image preview float:', error);

            // 检查是否是网络访问权限错误，如果是则静默处理
            if (error.message && (
                error.message.includes('内网访问已被禁用') ||
                error.message.includes('外网访问已被禁用') ||
                error.message.includes('403')
            )) {
                // 静默处理权限错误，不显示Toast
                CONFIG.log('info', '图片预览被拒绝：网络访问权限不足');
            } else {
                this.showToast('图片预览失败', 'error');
            }
        }
    }

    /**
     * 获取带认证的图片URL
     */
    async getImageUrlWithAuth(fileId) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                return URL.createObjectURL(blob);
            } else {
                // 如果预览失败，尝试缩略图
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
                const thumbnailResponse = await fetch(thumbnailUrl, {
                    headers: token ? {
                        'Authorization': `Bearer ${token}`
                    } : {}
                });

                if (thumbnailResponse.ok) {
                    const blob = await thumbnailResponse.blob();
                    return URL.createObjectURL(blob);
                } else {
                    throw new Error('无法获取图片数据');
                }
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to get image URL with auth:', error);
            throw error;
        }
    }

    /**
     * 创建图片预览
     */
    createImagePreview(fileId, file, container) {
        // 创建预览容器
        const previewWrapper = Utils.dom.create('div', {
            className: 'image-preview-wrapper'
        });

        // 创建工具栏
        const toolbar = Utils.dom.create('div', {
            className: 'preview-toolbar',
            innerHTML: `
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="zoom-out" title="缩小">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level">100%</span>
                    <button class="toolbar-btn" data-action="zoom-in" title="放大">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-fit" title="适应窗口">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="toolbar-btn" data-action="zoom-actual" title="实际大小">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="rotate-left" title="向左旋转">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="toolbar-btn" data-action="rotate-right" title="向右旋转">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                <div class="toolbar-group">
                    <button class="toolbar-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            `
        });

        // 创建图片容器
        const imageContainer = Utils.dom.create('div', {
            className: 'image-container'
        });

        // 创建图片元素
        const img = Utils.dom.create('img', {
            className: 'preview-image',
            alt: file.name,
            draggable: false
        });

        // 异步加载图片（带认证）
        this.loadImageWithAuth(fileId, img);

        // 预览状态
        const previewState = {
            scale: 1,
            rotation: 0,
            translateX: 0,
            translateY: 0,
            isDragging: false,
            lastX: 0,
            lastY: 0
        };

        // 更新图片变换
        const updateTransform = () => {
            const transform = `translate(${previewState.translateX}px, ${previewState.translateY}px) scale(${previewState.scale}) rotate(${previewState.rotation}deg)`;
            img.style.transform = transform;

            // 更新缩放显示
            const zoomLevel = toolbar.querySelector('.zoom-level');
            if (zoomLevel) {
                zoomLevel.textContent = Math.round(previewState.scale * 100) + '%';
            }
        };

        // 图片加载完成后的处理
        img.onload = () => {
            this.fitImageToContainer(img, imageContainer, previewState, updateTransform);
        };

        // 图片加载错误处理
        img.onerror = () => {
            // 如果预览失败，尝试使用缩略图
            img.src = FileAPI.getThumbnailURL(fileId, 'large');
            img.onerror = () => {
                // 如果缩略图也失败，显示错误信息
                container.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image"></i>
                        <p>图片预览失败</p>
                    </div>
                `;
            };
        };

        // 绑定工具栏事件
        this.bindPreviewToolbarEvents(toolbar, img, previewState, updateTransform, fileId);

        // 绑定图片交互事件
        this.bindImageInteractionEvents(img, imageContainer, previewState, updateTransform);

        // 组装预览界面
        imageContainer.appendChild(img);
        previewWrapper.appendChild(toolbar);
        previewWrapper.appendChild(imageContainer);

        container.innerHTML = '';
        container.appendChild(previewWrapper);
    }
    








    /**
     * 带认证的图片加载
     */
    async loadImageWithAuth(fileId, imgElement) {
        try {
            // 获取认证token
            const authInfo = Utils.storage.get('fileShareAuth');
            const token = authInfo ? authInfo.token : null;

            // 首先尝试预览URL
            const previewUrl = FileAPI.getPreviewURL(fileId);
            const response = await fetch(previewUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (response.ok) {
                const blob = await response.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL（在图片加载完成后）
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果预览失败，尝试缩略图
            const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'large');
            const thumbnailResponse = await fetch(thumbnailUrl, {
                headers: token ? {
                    'Authorization': `Bearer ${token}`
                } : {}
            });

            if (thumbnailResponse.ok) {
                const blob = await thumbnailResponse.blob();
                const imageUrl = URL.createObjectURL(blob);
                imgElement.src = imageUrl;

                // 清理blob URL
                imgElement.onload = () => {
                    setTimeout(() => {
                        URL.revokeObjectURL(imageUrl);
                    }, 1000);
                };

                return;
            }

            // 如果都失败了，触发错误处理
            if (imgElement.onerror) {
                imgElement.onerror();
            }

        } catch (error) {
            CONFIG.log('error', '带认证的图片加载失败:', error);
            if (imgElement.onerror) {
                imgElement.onerror();
            }
        }
    }

    /**
     * 适应图片到容器
     */
    fitImageToContainer(img, container, state, updateTransform) {
        const containerRect = container.getBoundingClientRect();
        const imgNaturalWidth = img.naturalWidth;
        const imgNaturalHeight = img.naturalHeight;

        if (imgNaturalWidth && imgNaturalHeight) {
            const containerRatio = containerRect.width / containerRect.height;
            const imageRatio = imgNaturalWidth / imgNaturalHeight;

            let scale;
            if (imageRatio > containerRatio) {
                // 图片更宽，以宽度为准
                scale = (containerRect.width * 0.9) / imgNaturalWidth;
            } else {
                // 图片更高，以高度为准
                scale = (containerRect.height * 0.9) / imgNaturalHeight;
            }

            state.scale = Math.min(scale, 1); // 不超过原始大小
            state.translateX = 0;
            state.translateY = 0;
            updateTransform();
        }
    }

    /**
     * 绑定预览工具栏事件
     */
    bindPreviewToolbarEvents(toolbar, img, state, updateTransform, fileId) {
        Utils.event.on(toolbar, 'click', (e) => {
            const btn = e.target.closest('.toolbar-btn');
            if (!btn) return;

            const action = btn.dataset.action;

            switch (action) {
                case 'zoom-in':
                    state.scale = Math.min(state.scale * 1.2, 5);
                    updateTransform();
                    break;

                case 'zoom-out':
                    state.scale = Math.max(state.scale / 1.2, 0.1);
                    updateTransform();
                    break;

                case 'zoom-fit':
                    this.fitImageToContainer(img, img.parentElement, state, updateTransform);
                    break;

                case 'zoom-actual':
                    state.scale = 1;
                    state.translateX = 0;
                    state.translateY = 0;
                    updateTransform();
                    break;

                case 'rotate-left':
                    state.rotation -= 90;
                    updateTransform();
                    break;

                case 'rotate-right':
                    state.rotation += 90;
                    updateTransform();
                    break;



                case 'download':
                    this.downloadFile(fileId);
                    break;
            }
        });
    }

    /**
     * 绑定图片交互事件
     */
    bindImageInteractionEvents(img, container, state, updateTransform) {
        // 鼠标拖拽
        Utils.event.on(img, 'mousedown', (e) => {
            e.preventDefault();
            state.isDragging = true;
            state.lastX = e.clientX;
            state.lastY = e.clientY;
            img.style.cursor = 'grabbing';
        });

        Utils.event.on(document, 'mousemove', (e) => {
            if (!state.isDragging) return;

            const deltaX = e.clientX - state.lastX;
            const deltaY = e.clientY - state.lastY;

            state.translateX += deltaX;
            state.translateY += deltaY;
            state.lastX = e.clientX;
            state.lastY = e.clientY;

            updateTransform();
        });

        Utils.event.on(document, 'mouseup', () => {
            if (state.isDragging) {
                state.isDragging = false;
                img.style.cursor = 'grab';
            }
        });

        // 鼠标滚轮缩放
        Utils.event.on(container, 'wheel', (e) => {
            e.preventDefault();

            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newScale = state.scale * delta;

            if (newScale >= 0.1 && newScale <= 5) {
                state.scale = newScale;
                updateTransform();
            }
        });

        // 双击重置
        Utils.event.on(img, 'dblclick', () => {
            this.fitImageToContainer(img, container, state, updateTransform);
        });

        // 设置初始光标
        img.style.cursor = 'grab';
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        const loadingDiv = document.getElementById('loading-indicator') || this.createLoadingIndicator();
        loadingDiv.querySelector('.loading-text').textContent = message;
        loadingDiv.style.display = 'flex';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingDiv = document.getElementById('loading-indicator');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    /**
     * 创建加载指示器
     */
    createLoadingIndicator() {
        const loadingDiv = Utils.dom.create('div', {
            id: 'loading-indicator',
            className: 'loading-indicator',
            innerHTML: `
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            `,
            style: 'display: none;'
        });
        document.body.appendChild(loadingDiv);
        return loadingDiv;
    }





    /**
     * 分享文件
     */
    shareFile(fileId) {
        // TODO: 实现分享功能
        this.showToast('分享功能开发中...', 'info');
    }

    /**
     * 显示文件信息
     */
    showFileInfo(fileId) {
        // TODO: 实现文件信息功能
        this.showToast('文件信息功能开发中...', 'info');
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboard(event) {
        // Ctrl+A 全选
        if (event.ctrlKey && event.key === 'a') {
            event.preventDefault();
            this.selectAll();
        }
        
        // Delete 删除
        if (event.key === 'Delete') {
            this.deleteSelected();
        }
        
        // Escape 取消选择
        if (event.key === 'Escape') {
            this.clearSelection();
        }
    }
    
    /**
     * 全选文件
     */
    selectAll() {
        this.clearSelection();
        Utils.dom.$$('.file-item').forEach(item => {
            const fileId = item.dataset.fileId;
            this.selectFile(fileId, item);
        });
    }

    /**
     * 处理文件复选框变化
     */
    handleFileCheckboxChange(fileId, checked) {
        CONFIG.log('debug', `处理复选框变化: fileId=${fileId}, checked=${checked}`);
        CONFIG.log('debug', `当前选中文件数量: ${this.selectedForDownload.size}`);

        if (checked) {
            this.selectedForDownload.add(fileId);
        } else {
            this.selectedForDownload.delete(fileId);
        }

        CONFIG.log('debug', `更新后选中文件数量: ${this.selectedForDownload.size}`);
        CONFIG.log('debug', `选中的文件ID: ${Array.from(this.selectedForDownload).join(', ')}`);

        // 更新批量操作按钮状态
        this.updateBatchActionButtons();
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActionButtons() {
        const selectedCount = this.selectedForDownload.size;
        CONFIG.log('debug', `更新批量操作按钮: 选中文件数量=${selectedCount}`);

        // 如果没有批量操作按钮，创建它们
        this.ensureBatchActionButtons();

        const batchDownloadBtn = Utils.dom.$('#batch-download-btn');
        const selectAllBtn = Utils.dom.$('#select-all-btn');
        const clearSelectionBtn = Utils.dom.$('#clear-selection-btn');

        CONFIG.log('debug', `找到的按钮: batch=${!!batchDownloadBtn}, selectAll=${!!selectAllBtn}, clear=${!!clearSelectionBtn}`);

        if (batchDownloadBtn) {
            batchDownloadBtn.disabled = selectedCount === 0;
            const buttonText = selectedCount > 0 ? `下载选中的 ${selectedCount} 个文件` : '批量下载';
            batchDownloadBtn.textContent = buttonText;
            CONFIG.log('debug', `批量下载按钮更新: disabled=${batchDownloadBtn.disabled}, text="${buttonText}"`);
        }

        if (selectAllBtn) {
            selectAllBtn.style.display = selectedCount === 0 ? 'inline-block' : 'none';
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.style.display = selectedCount > 0 ? 'inline-block' : 'none';
        }
    }

    /**
     * 确保批量操作按钮存在
     */
    ensureBatchActionButtons() {
        // 检查是否已经存在批量操作按钮
        if (Utils.dom.$('#batch-download-btn')) {
            CONFIG.log('debug', '批量操作按钮已存在，跳过创建');
            return;
        }

        // 查找视图控制区域
        const viewControls = Utils.dom.$('.view-controls');
        CONFIG.log('debug', `查找视图控制区域: ${!!viewControls}`);
        if (!viewControls) {
            CONFIG.log('warn', '未找到 .view-controls 元素，无法创建批量操作按钮');
            return;
        }

        // 创建批量操作按钮容器
        const batchActionsContainer = Utils.dom.create('div', {
            className: 'batch-actions',
            innerHTML: `
                <button id="select-all-btn" class="btn btn-secondary">
                    <i class="fas fa-check-square"></i>
                    全选
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary" style="display: none;">
                    <i class="fas fa-times"></i>
                    取消选择
                </button>
                <button id="batch-download-btn" class="btn btn-primary" disabled>
                    <i class="fas fa-download"></i>
                    批量下载
                </button>
            `
        });

        viewControls.appendChild(batchActionsContainer);

        // 绑定事件
        Utils.event.on(Utils.dom.$('#select-all-btn'), 'click', () => this.selectAllForDownload());
        Utils.event.on(Utils.dom.$('#clear-selection-btn'), 'click', () => this.clearDownloadSelection());
        Utils.event.on(Utils.dom.$('#batch-download-btn'), 'click', () => this.batchDownload());
    }

    /**
     * 全选文件用于下载
     */
    selectAllForDownload() {
        // 选中所有文件（不包括文件夹）
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = true;
            const fileId = checkbox.dataset.fileId;
            this.selectedForDownload.add(fileId);
        });

        this.updateBatchActionButtons();
    }

    /**
     * 清除下载选择
     */
    clearDownloadSelection() {
        // 取消选中所有复选框
        Utils.dom.$$('.file-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });

        this.selectedForDownload.clear();
        this.updateBatchActionButtons();
    }

    /**
     * 批量下载文件
     */
    async batchDownload() {
        if (this.selectedForDownload.size === 0) {
            this.showToast('请先选择要下载的文件', 'warning');
            return;
        }

        // 检查当前文件夹的下载权限
        if (this.currentFolder && !this.currentFolder.allow_download) {
            this.showToast('⚠️ 后台关闭下载', 'error');
            CONFIG.log('info', '批量下载被拒绝：当前文件夹禁止下载');
            return;
        }

        // 防止重复下载
        if (this.isDownloading) {
            this.showToast('正在下载中，请稍候...', 'warning');
            return;
        }

        try {
            this.isDownloading = true;
            this.showToast('正在准备下载...', 'info');

            const fileIds = Array.from(this.selectedForDownload);

            // 获取正确的认证token
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            // 使用新的批量下载接口
            const response = await fetch(`${api.getBaseURL()}/download/batch`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ file_ids: fileIds })
            });

            if (response.status === 423) {
                // 批量文件被加密，需要申请密码
                const errorData = await response.json();
                this.showToast('部分文件已加密，请单独下载并申请密码', 'warning');
                return;
            }

            if (!response.ok) {
                throw new Error(`批量下载失败: ${response.status}`);
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            // 获取文件名
            const contentDisposition = response.headers.get('content-disposition');
            let filename = `batch_download_${Date.now()}.zip`;
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) filename = matches[1];
            }

            Utils.url.downloadFile(url, filename);
            URL.revokeObjectURL(url);

            this.showToast(`成功下载 ${fileIds.length} 个文件`, 'success');

            // 清除选择
            this.clearDownloadSelection();

            // 刷新下载记录
            this.refreshDownloadRecords();

        } catch (error) {
            CONFIG.log('error', 'Batch download failed:', error);

            // 解析错误信息
            let errorMessage = error.message || '批量下载失败';

            // 检查是否是下载权限错误
            if (errorMessage.includes('后台关闭下载')) {
                this.showToast('⚠️ 后台关闭下载', 'error');
                CONFIG.log('info', '批量下载被拒绝：下载权限不足');
            }
            // 检查是否是文件不存在错误
            else if (errorMessage.includes('文件不存在于磁盘') || errorMessage.includes('文件不存在')) {
                this.showToast('⚠️ 部分文件已被移动或删除，请刷新页面后重新选择', 'error');
                CONFIG.log('info', '批量下载失败：部分文件不存在');
                // 自动刷新文件列表
                setTimeout(() => {
                    this.refresh();
                }, 2000);
            }
            // 检查是否是网络访问权限错误
            else if (errorMessage.includes('内网访问已被禁用') ||
                errorMessage.includes('外网访问已被禁用') ||
                errorMessage.includes('403')) {
                // 静默处理权限错误，不显示Toast
                CONFIG.log('info', '批量下载被拒绝：网络访问权限不足');
            } else if (errorMessage.includes('⚠️')) {
                // 如果错误信息已经包含警告符号，直接显示
                this.showToast(errorMessage, 'error');
            } else if (errorMessage.includes('批量下载失败: 400')) {
                // 处理通用400错误
                this.showToast('⚠️ 批量下载请求无效 - 请重新选择文件后重试', 'error');
            } else if (errorMessage.includes('批量下载失败: 404')) {
                // 处理404错误
                this.showToast('⚠️ 部分文件不存在 - 请刷新页面后重新选择文件', 'error');
            } else if (errorMessage.includes('批量下载失败: 500')) {
                // 处理500错误
                this.showToast('⚠️ 服务器错误 - 批量下载服务暂时不可用，请稍后重试', 'error');
            } else {
                // 其他错误
                this.showToast('批量下载失败: ' + errorMessage, 'error');
            }
        } finally {
            this.isDownloading = false;
        }
    }

    /**
     * 下载文件夹
     */
    async downloadFolder(folderId) {
        // 防止重复下载 - 使用文件夹ID作为键
        const downloadKey = `folder_${folderId}`;
        if (this.isDownloading || this._activeDownloads?.has(downloadKey)) {
            CONFIG.log('warn', `文件夹 ${folderId} 正在下载中，忽略重复请求`);
            this.showToast('正在下载中，请稍候...', 'warning');
            return;
        }

        try {
            // 初始化活动下载集合
            if (!this._activeDownloads) {
                this._activeDownloads = new Set();
            }

            this.isDownloading = true;
            this._activeDownloads.add(downloadKey);

            CONFIG.log('info', `开始下载文件夹: ${folderId}`);

            // 首先检查下载权限
            const folderInfo = await this.getFolderInfo(folderId);
            if (folderInfo && folderInfo.permissions && !folderInfo.permissions.download) {
                this.showToast('⚠️ 后台关闭下载', 'error');
                CONFIG.log('info', '文件夹下载被拒绝：当前文件夹禁止下载');
                return;
            }

            // 在发送下载请求前，先检查网络访问权限
            const hasNetworkAccess = await this.checkFolderNetworkAccess(folderId);
            if (!hasNetworkAccess) {
                // 显示更明确的网络访问权限错误提示
                this.showToast('⚠️ 网络访问被禁止 - 当前文件夹不允许从您的网络位置下载文件', 'error');
                CONFIG.log('info', '文件夹下载被拒绝：网络访问权限不足');

                // 显示详细的权限信息对话框
                this.showNetworkAccessDialog();
                return;
            }

            // 检查文件夹大小，决定使用同步还是异步下载
            const folderSize = folderInfo?.total_size || 0;
            const LARGE_FOLDER_THRESHOLD = 500 * 1024 * 1024; // 500MB

            if (folderSize > LARGE_FOLDER_THRESHOLD) {
                // 使用异步下载
                await this.downloadFolderAsync(folderId, folderInfo?.name || 'folder');
                return;
            }

            this.showToast('正在打包文件夹...', 'info');

            // 调用后端API下载文件夹
            const response = await FileAPI.downloadFolder(folderId);

            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                // 获取文件名
                const contentDisposition = response.headers.get('content-disposition');
                let filename = `folder_${folderId}_${Date.now()}.zip`;
                if (contentDisposition) {
                    const matches = contentDisposition.match(/filename="(.+)"/);
                    if (matches) filename = matches[1];
                }

                Utils.url.downloadFile(url, filename);
                URL.revokeObjectURL(url);

                this.showToast('文件夹下载成功', 'success');
            } else {
                // 尝试解析服务器返回的错误信息
                let errorMessage = '文件夹下载失败';
                try {
                    const errorData = await response.json();
                    if (errorData.error) {
                        errorMessage = errorData.error;
                    }
                } catch (e) {
                    // 如果无法解析JSON，使用默认错误消息
                    CONFIG.log('warn', '无法解析错误响应:', e);
                }
                throw new Error(errorMessage);
            }

        } catch (error) {
            CONFIG.log('error', 'Folder download failed:', error);

            // 检查是否是下载权限错误
            if (error.message && error.message.includes('后台关闭下载')) {
                this.showToast('⚠️ 后台关闭下载', 'error');
                CONFIG.log('info', '文件夹下载被拒绝：下载权限不足');
                return; // 下载权限错误不显示网络访问对话框
            }
            // 检查是否是文件不存在错误
            else if (error.message && (error.message.includes('文件不存在于磁盘') || error.message.includes('文件不存在'))) {
                this.showToast('⚠️ 文件夹中部分文件已被移动或删除，请刷新页面', 'error');
                CONFIG.log('info', '文件夹下载失败：部分文件不存在');
                // 自动刷新文件列表
                setTimeout(() => {
                    this.refresh();
                }, 2000);
                return;
            }
            // 检查是否是网络访问权限错误，如果是则显示明确的错误信息
            else if (error.message && (
                error.message.includes('内网访问已被禁用') ||
                error.message.includes('外网访问已被禁用') ||
                error.message.includes('服务器关闭下载') ||
                error.message.includes('403')
            )) {
                this.showToast('⚠️ 网络访问被禁止 - 当前文件夹不允许从您的网络位置下载文件', 'error');
                CONFIG.log('info', '文件夹下载被拒绝：网络访问权限不足');
                this.showNetworkAccessDialog();
            } else {
                const errorMsg = error.message || '未知错误';
                this.showToast('文件夹下载失败: ' + errorMsg, 'error');
            }
        } finally {
            this.isDownloading = false;
            if (this._activeDownloads) {
                this._activeDownloads.delete(downloadKey);
            }
            CONFIG.log('info', `文件夹下载完成: ${folderId}`);
        }
    }

    /**
     * 异步下载文件夹
     */
    async downloadFolderAsync(folderId, folderName) {
        try {
            this.showToast('文件夹较大，正在创建异步下载任务...', 'info');

            // 创建异步下载任务
            const result = await AsyncDownloadAPI.createFolderDownload(folderId);

            if (!result.success) {
                throw new Error(result.error || '创建下载任务失败');
            }

            const taskId = result.task_id;
            this.showToast('下载任务已创建，正在后台处理...', 'success');

            // 显示进度对话框
            this.showAsyncDownloadProgress(taskId, folderName);

        } catch (error) {
            CONFIG.log('error', '异步下载创建失败:', error);
            this.showToast(`异步下载失败: ${error.message}`, 'error');
        }
    }

    /**
     * 显示异步下载进度对话框
     */
    showAsyncDownloadProgress(taskId, folderName) {
        const modal = Utils.dom.create('div', {
            className: 'modal-overlay async-download-modal',
            innerHTML: `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>异步下载进度</h3>
                        <button class="modal-close" data-action="close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="download-info">
                            <h4>${folderName}</h4>
                            <p class="task-id">任务ID: ${taskId}</p>
                        </div>

                        <div class="progress-section">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">准备中...</div>
                            <div class="progress-details">
                                <span class="processed-files">0</span> / <span class="total-files">0</span> 文件
                            </div>
                        </div>

                        <div class="status-section">
                            <div class="status-text">等待处理...</div>
                            <div class="download-actions" style="display: none;">
                                <button class="btn btn-primary" data-action="download">
                                    <i class="fas fa-download"></i>
                                    下载文件
                                </button>
                                <button class="btn btn-secondary" data-action="copy-link">
                                    <i class="fas fa-copy"></i>
                                    复制链接
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" data-action="close">关闭</button>
                        <button class="btn btn-info" data-action="refresh">刷新状态</button>
                    </div>
                </div>
            `
        });

        // 绑定事件
        Utils.event.on(modal, 'click', async (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            if (!action) return;

            switch (action) {
                case 'close':
                    modal.remove();
                    break;
                case 'refresh':
                    await this.updateAsyncDownloadProgress(modal, taskId);
                    break;
                case 'download':
                    await this.downloadAsyncFile(taskId, folderName);
                    break;
                case 'copy-link':
                    this.copyAsyncDownloadLink(taskId);
                    break;
            }
        });

        // 点击背景关闭
        Utils.event.on(modal, 'click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        document.body.appendChild(modal);

        // 开始轮询进度
        this.pollAsyncDownloadProgress(modal, taskId);
    }

    /**
     * 检查文件夹网络访问权限
     */
    async checkFolderNetworkAccess(folderId) {
        // 已禁用前端网络访问检查 - 直接允许所有下载
        CONFIG.log('info', `网络访问检查已禁用，允许下载文件夹: ${folderId}`);
        return true;
    }

    /**
     * 轮询异步下载进度
     */
    async pollAsyncDownloadProgress(modal, taskId) {
        try {
            await AsyncDownloadAPI.pollTaskStatus(taskId, (task) => {
                this.updateAsyncDownloadProgressUI(modal, task);
            });

            // 任务完成
            const downloadActions = modal.querySelector('.download-actions');
            if (downloadActions) {
                downloadActions.style.display = 'block';
            }

        } catch (error) {
            CONFIG.log('error', '异步下载轮询失败:', error);
            const statusText = modal.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = `下载失败: ${error.message}`;
                statusText.className = 'status-text error';
            }
        }
    }

    /**
     * 更新异步下载进度UI
     */
    updateAsyncDownloadProgressUI(modal, task) {
        const progressFill = modal.querySelector('.progress-fill');
        const progressText = modal.querySelector('.progress-text');
        const processedFiles = modal.querySelector('.processed-files');
        const totalFiles = modal.querySelector('.total-files');
        const statusText = modal.querySelector('.status-text');

        if (progressFill) {
            progressFill.style.width = `${task.progress || 0}%`;
        }

        if (processedFiles) {
            processedFiles.textContent = task.processed_files || 0;
        }

        if (totalFiles) {
            totalFiles.textContent = task.total_files || 0;
        }

        if (progressText) {
            progressText.textContent = `${task.progress || 0}%`;
        }

        if (statusText) {
            let statusMessage = '';
            switch (task.status) {
                case 'pending':
                    statusMessage = '等待处理...';
                    break;
                case 'processing':
                    statusMessage = '正在打包文件...';
                    break;
                case 'completed':
                    statusMessage = '打包完成，可以下载了！';
                    statusText.className = 'status-text success';
                    break;
                case 'failed':
                    statusMessage = `打包失败: ${task.error_message || '未知错误'}`;
                    statusText.className = 'status-text error';
                    break;
                default:
                    statusMessage = task.status;
            }
            statusText.textContent = statusMessage;
        }
    }

    /**
     * 下载异步生成的文件
     */
    async downloadAsyncFile(taskId, filename) {
        try {
            this.showToast('正在下载文件...', 'info');

            const response = await AsyncDownloadAPI.downloadFile(taskId);
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            Utils.url.downloadFile(url, `${filename}.zip`);
            URL.revokeObjectURL(url);

            this.showToast('文件下载成功', 'success');

        } catch (error) {
            CONFIG.log('error', '异步文件下载失败:', error);
            this.showToast(`下载失败: ${error.message}`, 'error');
        }
    }

    /**
     * 复制异步下载链接
     */
    copyAsyncDownloadLink(taskId) {
        const link = `${window.location.origin}/api/download/async/${taskId}`;

        if (navigator.clipboard) {
            navigator.clipboard.writeText(link).then(() => {
                this.showToast('下载链接已复制到剪贴板', 'success');
            }).catch(() => {
                this.fallbackCopyText(link);
            });
        } else {
            this.fallbackCopyText(link);
        }
    }

    /**
     * 备用复制文本方法
     */
    fallbackCopyText(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            this.showToast('下载链接已复制到剪贴板', 'success');
        } catch (err) {
            this.showToast('复制失败，请手动复制链接', 'error');
        }
        document.body.removeChild(textArea);
    }

    /**
     * 手动更新异步下载进度
     */
    async updateAsyncDownloadProgress(modal, taskId) {
        try {
            const result = await AsyncDownloadAPI.getTaskStatus(taskId);
            if (result.success) {
                this.updateAsyncDownloadProgressUI(modal, result.task);

                if (result.task.status === 'completed') {
                    const downloadActions = modal.querySelector('.download-actions');
                    if (downloadActions) {
                        downloadActions.style.display = 'block';
                    }
                }
            } else {
                this.showToast(`获取状态失败: ${result.error}`, 'error');
            }
        } catch (error) {
            CONFIG.log('error', '更新异步下载进度失败:', error);
            this.showToast(`更新状态失败: ${error.message}`, 'error');
        }
    }

    /**
     * 刷新下载记录
     */
    refreshDownloadRecords() {
        try {
            // 如果存在简单下载管理器，刷新它
            if (window.simpleDownloads && typeof window.simpleDownloads.loadRecords === 'function') {
                window.simpleDownloads.loadRecords();
                CONFIG.log('info', '已刷新下载记录');
            } else {
                CONFIG.log('info', '下载记录管理器不可用，跳过刷新');
            }
        } catch (error) {
            CONFIG.log('error', '刷新下载记录失败:', error);
        }
    }

    /**
     * 显示网络访问权限详细信息对话框
     */
    showNetworkAccessDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';
        dialog.innerHTML = `
            <div class="modal-content" style="max-width: 500px;">
                <div class="modal-header">
                    <h3>⚠️ 网络访问权限不足</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div style="margin-bottom: 15px;">
                        <strong>问题说明：</strong><br>
                        当前文件夹的网络访问权限设置不允许从您的网络位置下载文件。
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong>可能的原因：</strong>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                            <li>文件夹禁止了内网访问</li>
                            <li>文件夹禁止了外网访问</li>
                            <li>全局网络访问控制限制</li>
                        </ul>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong>解决方案：</strong><br>
                        请联系管理员调整文件夹的网络访问权限设置，或者使用允许的网络环境访问。
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove()">我知道了</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // 点击背景关闭对话框
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                dialog.remove();
            }
        });
    }

    /**
     * 获取文件夹列表数据
     */
    async getFoldersData() {
        try {
            // 获取正确的认证token
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            const response = await fetch(`${api.getBaseURL()}/files/folders`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                return result.data || result;
            } else {
                CONFIG.log('warn', `获取文件夹列表失败: ${response.status}`);
                return null;
            }
        } catch (error) {
            CONFIG.log('error', '获取文件夹列表异常:', error);
            return null;
        }
    }

    /**
     * 删除选中的文件
     */
    async deleteSelected() {
        if (this.selectedFiles.size === 0) return;
        
        let confirmed = false;
        if (typeof Components !== 'undefined' && Components.Confirm) {
            confirmed = await Components.Confirm.show(
                `确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`,
                '确认删除'
            );
        } else {
            confirmed = confirm(`确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`);
        }
        
        if (confirmed) {
            // TODO: 实现删除功能
            this.showToast('删除功能开发中...', 'info');
        }
    }
    
    /**
     * 刷新文件列表
     */
    refresh() {
        // 根据当前视图状态决定刷新方式
        if (this.isInSearchMode) {
            // 如果在搜索模式，重新执行搜索
            if (window.searchManager && window.searchManager.lastSearchQuery) {
                window.searchManager.performSearch(window.searchManager.lastSearchQuery);
            } else {
                this.loadFiles(this.currentFolder?.id);
            }
        } else {
            // 正常文件夹视图，刷新文件列表
            this.loadFiles(this.currentFolder?.id);
        }
    }

    /**
     * 显示Toast消息
     */
    showToast(message, type = 'info') {
        // 防重复显示机制
        if (!this._lastToastMessages) {
            this._lastToastMessages = new Map();
        }

        const messageKey = `${type}:${message}`;
        const now = Date.now();
        const lastShown = this._lastToastMessages.get(messageKey);

        if (lastShown && (now - lastShown) < 3000) {
            console.log(`FileManager Toast重复消息被阻止: ${message}`);
            return;
        }

        this._lastToastMessages.set(messageKey, now);

        if (typeof Components !== 'undefined' && Components.Toast) {
            Components.Toast.show(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 刷新单个文件的缩略图
     */
    refreshThumbnail(fileId) {
        const thumbnailContainers = Utils.dom.$$(`[id^="thumb-${fileId}"]`);
        thumbnailContainers.forEach(container => {
            const img = container.querySelector('.thumbnail-image');
            const loading = container.querySelector('.thumbnail-loading');
            const fallback = container.querySelector('.thumbnail-fallback');

            if (img && loading && fallback) {
                // 重置状态
                loading.style.display = 'block';
                img.style.display = 'none';
                fallback.style.display = 'none';

                // 重新加载缩略图（添加时间戳避免缓存）
                const thumbnailUrl = FileAPI.getThumbnailURL(fileId, 'medium');
                img.src = `${thumbnailUrl}&t=${Date.now()}`;
            }
        });
    }

    /**
     * 批量刷新缩略图
     */
    refreshAllThumbnails() {
        const imageFiles = this.files.filter(file =>
            Utils.isImageFile(file.name) && file.type !== 'folder'
        );

        imageFiles.forEach(file => {
            this.refreshThumbnail(file.id);
        });

        this.showToast('正在刷新缩略图...', 'info');
    }

    /**
     * 创建文件夹卡片
     */
    createFolderCard(file) {
        const card = Utils.dom.create('div', {
            className: 'folder-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件夹图标
        const iconDiv = Utils.dom.create('div', {
            className: 'folder-icon'
        });
        const icon = Utils.dom.create('i', {
            className: 'fas fa-folder'
        });
        iconDiv.appendChild(icon);

        // 文件夹名称
        const nameDiv = Utils.dom.create('div', {
            className: 'folder-name',
            textContent: file.name,
            title: file.name
        });

        card.appendChild(iconDiv);
        card.appendChild(nameDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.folder-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');

            // 触发文件夹打开事件
            this.handleFileAction('open', file);
        });

        // 添加双击事件
        card.addEventListener('dblclick', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleFileAction('open', file);
        });

        return card;
    }

    /**
     * 创建文件详情卡片
     */
    createFileDetailCard(file) {
        const isImage = Utils.isImageFile(file.name);
        const icon = CONFIG.FILES.getFileIcon(file.name, false);

        const card = Utils.dom.create('div', {
            className: 'file-detail-card',
            'data-file-id': file.id,
            'data-file-type': file.type,
            'data-file-path': file.path
        });

        // 文件图标
        const iconDiv = Utils.dom.create('div', {
            className: 'file-icon'
        });
        const iconElement = Utils.dom.create('i', {
            className: icon
        });
        iconDiv.appendChild(iconElement);

        // 文件信息
        const infoDiv = Utils.dom.create('div', {
            className: 'file-info'
        });

        // 文件名
        const nameDiv = Utils.dom.create('div', {
            className: 'file-name',
            textContent: file.name,
            title: file.name
        });

        // 文件元数据
        const metaDiv = Utils.dom.create('div', {
            className: 'file-meta'
        });

        // 修改时间
        const timeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const timeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '修改时间'
        });
        const timeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.modified || '-'
        });
        timeItem.appendChild(timeLabel);
        timeItem.appendChild(timeValue);

        // 文件类型
        const typeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const typeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '类型'
        });
        const typeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: this.getFileTypeDescription(file.name)
        });
        typeItem.appendChild(typeLabel);
        typeItem.appendChild(typeValue);

        // 文件大小
        const sizeItem = Utils.dom.create('div', {
            className: 'file-meta-item'
        });
        const sizeLabel = Utils.dom.create('div', {
            className: 'file-meta-label',
            textContent: '大小'
        });
        const sizeValue = Utils.dom.create('div', {
            className: 'file-meta-value',
            textContent: file.size || '-'
        });
        sizeItem.appendChild(sizeLabel);
        sizeItem.appendChild(sizeValue);

        metaDiv.appendChild(timeItem);
        metaDiv.appendChild(typeItem);
        metaDiv.appendChild(sizeItem);

        infoDiv.appendChild(nameDiv);
        infoDiv.appendChild(metaDiv);

        // 操作按钮
        const actionsDiv = Utils.dom.create('div', {
            className: 'file-actions'
        });

        // 下载按钮已移除

        // 预览按钮（仅图片）
        if (isImage) {
            const previewBtn = Utils.dom.create('button', {
                className: 'action-btn',
                'data-action': 'preview',
                title: '预览'
            });
            previewBtn.innerHTML = '<i class="fas fa-eye"></i>';
            // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）
            actionsDiv.appendChild(previewBtn);
        }

        // 收藏按钮
        const favoriteBtn = Utils.dom.create('button', {
            className: 'action-btn',
            'data-action': 'favorite',
            title: '收藏'
        });
        favoriteBtn.innerHTML = '<i class="fas fa-star"></i>';
        // 不需要直接绑定事件，使用事件委托（在bindEvents中已处理）

        actionsDiv.appendChild(favoriteBtn);

        card.appendChild(iconDiv);
        card.appendChild(infoDiv);
        card.appendChild(actionsDiv);

        // 添加点击事件
        card.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // 清除其他选中状态
            document.querySelectorAll('.file-detail-card.selected').forEach(el => {
                el.classList.remove('selected');
            });

            // 设置当前选中状态
            card.classList.add('selected');
        });

        return card;
    }

    /**
     * 获取文件类型描述
     */
    getFileTypeDescription(fileName) {
        const ext = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'jpg': 'JPG 图片文件',
            'jpeg': 'JPEG 图片文件',
            'png': 'PNG 图片文件',
            'psd': 'Photoshop文档',
            'tif': 'TIFF图片',
            'tiff': 'TIFF图片',
            'ai': 'Illustrator文档',
            'eps': 'EPS矢量图',
            'gif': 'GIF动图',
            'bmp': 'BMP图片',
            'webp': 'WebP图片',
            'svg': 'SVG矢量图'
        };
        return typeMap[ext] || '图片文件';
    }

    /**
     * 显示密码申请对话框
     */
    showPasswordRequestDialog(fileId, fileName, isFromRecord = false) {
        // 创建对话框HTML
        const dialogHtml = `
            <div class="password-request-dialog">
                <div class="dialog-header">
                    <h3>申请解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-request-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="file-info">
                        <i class="fas fa-file-archive"></i>
                        <span class="file-name">${fileName}</span>
                    </div>
                    <div class="info-text">
                        <p>该文件已加密，需要密码才能解压。</p>
                        <p>请填写申请原因（可选）：</p>
                    </div>
                    <textarea class="reason-input" placeholder="请简要说明申请密码的原因..." maxlength="200"></textarea>
                    <div class="remaining-requests">
                        <span class="remaining-count">剩余申请次数：<span id="remaining-count">-</span></span>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary" onclick="this.closest('.password-request-dialog').remove()">
                        取消
                    </button>
                    <button class="btn btn-primary" onclick="fileManager.submitPasswordRequest(${fileId}, this, ${isFromRecord})">
                        申请密码
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 聚焦到文本框
        const reasonInput = overlay.querySelector('.reason-input');
        if (reasonInput) {
            reasonInput.focus();
        }

        // 添加ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        });
    }

    /**
     * 提交密码申请
     */
    async submitPasswordRequest(fileId, buttonElement, isFromRecord = false) {
        try {
            const dialog = buttonElement.closest('.password-request-dialog');
            const reasonInput = dialog.querySelector('.reason-input');
            const reason = reasonInput.value.trim();

            // 禁用按钮
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 申请中...';

            // 调用API申请密码
            const result = await FileAPI.requestDownloadPassword(fileId, reason);

            if (result.success) {
                // 显示密码
                this.showPasswordResult(result.data.password, result.data.remaining_requests);

                // 关闭申请对话框
                dialog.closest('.dialog-overlay').remove();

                this.showToast('密码申请成功！', 'success');
            } else {
                throw new Error(result.error || '密码申请失败');
            }

        } catch (error) {
            console.error('密码申请失败:', error);
            this.showToast(error.message || '密码申请失败', 'error');

            // 恢复按钮状态
            buttonElement.disabled = false;
            buttonElement.innerHTML = '申请密码';
        }
    }

    /**
     * 显示密码结果
     */
    showPasswordResult(password, remainingRequests) {
        const dialogHtml = `
            <div class="password-result-dialog">
                <div class="dialog-header">
                    <h3>解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-result-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="password-display">
                        <label>解压密码：</label>
                        <div class="password-box">
                            <input type="text" value="${password}" readonly class="password-input">
                            <button class="copy-btn" onclick="fileManager.copyPassword('${password}', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-info">
                        <p><i class="fas fa-info-circle"></i> 请妥善保管此密码，用于解压下载的文件。</p>
                        <p><i class="fas fa-clock"></i> 密码有效期：24小时</p>
                        <p><i class="fas fa-exclamation-triangle"></i> 剩余申请次数：${remainingRequests}</p>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.closest('.password-result-dialog').remove()">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 自动选中密码
        const passwordInput = overlay.querySelector('.password-input');
        if (passwordInput) {
            passwordInput.select();
        }
    }

    /**
     * 复制密码到剪贴板
     */
    async copyPassword(password, buttonElement) {
        try {
            await navigator.clipboard.writeText(password);

            // 更新按钮状态
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i>';
            buttonElement.classList.add('copied');

            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
                buttonElement.classList.remove('copied');
            }, 2000);

            this.showToast('密码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('复制失败，请手动复制', 'error');
        }
    }

    /**
     * 切换视图
     */
    switchView(view) {
        this.currentView = view;

        // 更新导航状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-view="${view}"]`)?.closest('.menu-item');
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // 根据视图显示不同内容
        switch (view) {
            case 'downloads':
                this.showDownloadRecords();
                break;
            case 'recent':
                this.showRecentFiles();
                break;
            default:
                this.showHomeView();
                break;
        }
    }

    /**
     * 显示下载记录 - 简化版本
     */
    async showDownloadRecords() {
        console.log('显示下载记录');
        
        // 检查登录状态
        const authData = localStorage.getItem('fileShareAuth');
        if (!authData) {
            alert('请先登录');
            window.location.href = 'login.html';
            return;
        }

        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
        const downloadsMenu = document.querySelector('[data-view="downloads"]')?.closest('.menu-item');
        if (downloadsMenu) downloadsMenu.classList.add('active');

        // 显示下载记录视图
        this.showDownloadRecordsView();
        
        // 创建简单的下载记录界面
        this.createSimpleDownloadRecordsInterface();
        
        // 加载数据
        await this.loadSimpleDownloadRecords();
    }

    /**
     * 创建简单的下载记录界面
     */
    createSimpleDownloadRecordsInterface() {
        const container = document.getElementById('download-records-view');
        if (!container) return;

        container.innerHTML = `
            <div style="padding: 20px;">
                <h2 style="margin-bottom: 20px;">
                    <i class="fas fa-download"></i> 下载记录
                </h2>
                <div id="download-loading" style="text-align: center; padding: 40px; display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载...
                </div>
                <div id="download-error" style="text-align: center; padding: 40px; color: red; display: none;">
                </div>
                <div id="download-list">
                </div>
            </div>
        `;
    }

    /**
     * 加载下载记录 - 简化版本
     */
    async loadSimpleDownloadRecords() {
        const loadingEl = document.getElementById('download-loading');
        const errorEl = document.getElementById('download-error');
        const listEl = document.getElementById('download-list');

        if (loadingEl) loadingEl.style.display = 'block';
        if (errorEl) errorEl.style.display = 'none';
        if (listEl) listEl.innerHTML = '';

        try {
            const response = await fetch(`${api.getBaseURL()}/download/records`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${JSON.parse(localStorage.getItem('fileShareAuth')).token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            console.log('下载记录API响应:', data);

            if (loadingEl) loadingEl.style.display = 'none';

            if (data.success && data.records && Array.isArray(data.records) && data.records.length > 0) {
                this.renderSimpleDownloadRecords(data.records);
            } else if (data.success && (!data.records || !Array.isArray(data.records) || data.records.length === 0)) {
                if (listEl) listEl.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无下载记录</div>';
            } else {
                throw new Error(data.error || '获取下载记录失败');
            }

        } catch (error) {
            console.error('获取下载记录失败:', error);
            if (loadingEl) loadingEl.style.display = 'none';
            if (errorEl) {
                errorEl.style.display = 'block';
                errorEl.textContent = error.message || '获取下载记录失败';
            }
        }
    }

    /**
     * 渲染下载记录 - 简化版本
     */
    renderSimpleDownloadRecords(records) {
        const listEl = document.getElementById('download-list');
        if (!listEl) return;

        // 确保records是数组
        if (!Array.isArray(records)) {
            CONFIG.log('warn', 'renderSimpleDownloadRecords接收到非数组参数:', records);
            listEl.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无下载记录</div>';
            return;
        }

        let html = '<div style="background: white; border-radius: 8px; padding: 20px;">';
        
        records.forEach(record => {
            // 确保record对象存在
            if (!record || typeof record !== 'object') {
                return;
            }
            const fileSize = this.formatFileSize(record.file_size || 0);
            const downloadTime = this.formatDateTime(record.download_time);
            const typeLabel = this.getDownloadTypeLabel(record.download_type);
            const encryptionBadge = record.is_encrypted ? '<span style="background: #fef2e2; color: #d97706; padding: 2px 8px; border-radius: 4px; font-size: 12px;">🔒 加密</span>' : '';

            html += `
                <div style="border: 1px solid #e5e7eb; border-radius: 6px; padding: 15px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500; margin-bottom: 5px;">
                                <i class="fas fa-file"></i> ${record.filename}
                                ${encryptionBadge}
                            </div>
                            <div style="font-size: 14px; color: #6b7280;">
                                大小: ${fileSize} | 类型: ${typeLabel} | 时间: ${downloadTime}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        listEl.innerHTML = html;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
        if (!dateString) return '未知时间';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        } catch (e) {
            return '未知时间';
        }
    }

    /**
     * 获取下载类型标签
     */
    getDownloadTypeLabel(type) {
        const labels = {
            'single': '单文件',
            'batch': '批量下载',
            'folder': '文件夹'
        };
        return labels[type] || '未知';
    }

    /**
     * 显示密码申请对话框
     */
    showPasswordRequestDialog(fileId, fileName, isFromRecord = false) {
        // 创建对话框HTML
        const dialogHtml = `
            <div class="password-request-dialog">
                <div class="dialog-header">
                    <h3>申请解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-request-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="file-info">
                        <i class="fas fa-file-archive"></i>
                        <span class="file-name">${fileName}</span>
                    </div>
                    <div class="info-text">
                        <p>该文件已加密，需要密码才能解压。</p>
                        <p>请填写申请原因（可选）：</p>
                    </div>
                    <textarea class="reason-input" placeholder="请简要说明申请密码的原因..." maxlength="200"></textarea>
                    <div class="remaining-requests">
                        <span class="remaining-count">剩余申请次数：<span id="remaining-count">-</span></span>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary" onclick="this.closest('.password-request-dialog').remove()">
                        取消
                    </button>
                    <button class="btn btn-primary" onclick="fileManager.submitPasswordRequest(${fileId}, this, ${isFromRecord})">
                        申请密码
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 聚焦到文本框
        const reasonInput = overlay.querySelector('.reason-input');
        if (reasonInput) {
            reasonInput.focus();
        }

        // 添加ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);

        // 点击遮罩层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        });
    }

    /**
     * 提交密码申请
     */
    async submitPasswordRequest(fileId, buttonElement, isFromRecord = false) {
        try {
            const dialog = buttonElement.closest('.password-request-dialog');
            const reasonInput = dialog.querySelector('.reason-input');
            const reason = reasonInput.value.trim();

            // 禁用按钮
            buttonElement.disabled = true;
            buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 申请中...';

            // 调用API申请密码
            const result = await FileAPI.requestDownloadPassword(fileId, reason);

            if (result.success) {
                // 显示密码
                this.showPasswordResult(result.data.password, result.data.remaining_requests);

                // 关闭申请对话框
                dialog.closest('.dialog-overlay').remove();

                this.showToast('密码申请成功！', 'success');
            } else {
                throw new Error(result.error || '密码申请失败');
            }

        } catch (error) {
            console.error('密码申请失败:', error);
            this.showToast(error.message || '密码申请失败', 'error');

            // 恢复按钮状态
            buttonElement.disabled = false;
            buttonElement.innerHTML = '申请密码';
        }
    }

    /**
     * 显示密码结果
     */
    showPasswordResult(password, remainingRequests) {
        const dialogHtml = `
            <div class="password-result-dialog">
                <div class="dialog-header">
                    <h3>解压密码</h3>
                    <button class="close-btn" onclick="this.closest('.password-result-dialog').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="password-display">
                        <label>解压密码：</label>
                        <div class="password-box">
                            <input type="text" value="${password}" readonly class="password-input">
                            <button class="copy-btn" onclick="fileManager.copyPassword('${password}', this)">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="password-info">
                        <p><i class="fas fa-info-circle"></i> 请妥善保管此密码，用于解压下载的文件。</p>
                        <p><i class="fas fa-clock"></i> 密码有效期：24小时</p>
                        <p><i class="fas fa-exclamation-triangle"></i> 剩余申请次数：${remainingRequests}</p>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.closest('.password-result-dialog').remove()">
                        确定
                    </button>
                </div>
            </div>
        `;

        // 创建遮罩层
        const overlay = Utils.dom.create('div', {
            className: 'dialog-overlay',
            innerHTML: dialogHtml
        });

        // 添加到页面
        document.body.appendChild(overlay);

        // 自动选中密码
        const passwordInput = overlay.querySelector('.password-input');
        if (passwordInput) {
            passwordInput.select();
        }
    }

    /**
     * 复制密码到剪贴板
     */
    async copyPassword(password, buttonElement) {
        try {
            await navigator.clipboard.writeText(password);

            // 更新按钮状态
            const originalHtml = buttonElement.innerHTML;
            buttonElement.innerHTML = '<i class="fas fa-check"></i>';
            buttonElement.classList.add('copied');

            setTimeout(() => {
                buttonElement.innerHTML = originalHtml;
                buttonElement.classList.remove('copied');
            }, 2000);

            this.showToast('密码已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showToast('复制失败，请手动复制', 'error');
        }
    }

    /**
     * 切换视图
     */
    switchView(view) {
        this.currentView = view;

        // 更新导航状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeItem = document.querySelector(`[data-view="${view}"]`)?.closest('.menu-item');
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // 根据视图显示不同内容
        switch (view) {
            case 'downloads':
                this.showDownloadRecords();
                break;
            case 'recent':
                this.showRecentFiles();
                break;
            default:
                this.showHomeView();
                break;
        }
    }

    /**
     * 显示最近文件
     */
    async showRecentFiles() {
        // TODO: 实现最近文件显示
        this.updateBreadcrumb([
            { name: '首页', icon: 'fas fa-home' },
            { name: '最近访问', icon: 'fas fa-clock' }
        ]);

        this.showToast('最近访问功能开发中', 'info');
    }

    /**
     * 显示下载记录视图
     */
    showDownloadRecordsView() {
        // 隐藏其他视图
        const fileGrid = document.getElementById('file-grid');
        const fileList = document.getElementById('file-list');
        const downloadRecordsView = document.getElementById('download-records-view');

        if (fileGrid) fileGrid.classList.add('hidden');
        if (fileList) fileList.classList.add('hidden');
        if (downloadRecordsView) downloadRecordsView.classList.remove('hidden');
    }

    /**
     * 获取文件夹信息
     */
    async getFolderInfo(folderId) {
        try {
            const authData = localStorage.getItem('fileShareAuth');
            let token = '';
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    token = auth.token || '';
                } catch (e) {
                    console.error('解析认证数据失败:', e);
                }
            }

            // 使用现有的文件夹列表API，然后找到对应的文件夹
            const response = await fetch(`${api.getBaseURL()}/files/folders`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const folders = await response.json();
                // 查找指定ID的文件夹
                const folder = folders.find(f => f.id === parseInt(folderId));
                return folder || null;
            } else {
                CONFIG.log('warn', `获取文件夹信息失败: ${response.status}`);
                return null;
            }
        } catch (error) {
            CONFIG.log('error', '获取文件夹信息失败:', error);
            return null;
        }
    }
}

// 创建全局文件管理器实例
let fileManager;

// 全局安全的缩略图处理函数
window.safeThumbnailLoad = function(img) {
    if (window.fileManager && window.fileManager.onThumbnailLoad) {
        window.fileManager.onThumbnailLoad(img);
    }
};

window.safeThumbnailError = function(img) {
    if (window.fileManager && window.fileManager.onThumbnailError) {
        window.fileManager.onThumbnailError(img);
    }
};

document.addEventListener('DOMContentLoaded', () => {
    // 只有在没有被app.js初始化时才创建实例
    if (!window.fileManager && !window.app) {
        fileManager = new FileManager();
        window.fileManager = fileManager;
    }
});

// 全局可用
window.FileManager = FileManager;
