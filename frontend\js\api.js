/**
 * API请求处理模块
 * 提供统一的API请求接口和错误处理
 */

class APIClient {
    constructor() {
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryCount = CONFIG.API.RETRY_COUNT;
        this.retryDelay = CONFIG.API.RETRY_DELAY;

        // 请求拦截器
        this.requestInterceptors = [];
        this.responseInterceptors = [];

        // 添加默认拦截器
        this.addDefaultInterceptors();
    }

    /**
     * 动态获取API基础URL
     */
    getBaseURL() {
        // 优先使用保存的服务器地址
        try {
            const savedAuth = localStorage.getItem('fileShareAuth');
            if (savedAuth) {
                const authData = JSON.parse(savedAuth);
                if (authData.serverUrl) {
                    const baseUrl = `${authData.serverUrl}/api`;
                    CONFIG.log('debug', `使用保存的服务器地址: ${baseUrl}`);
                    return baseUrl;
                }
            }
        } catch (e) {
            CONFIG.log('warn', '读取保存的服务器地址失败:', e);
        }

        // 使用默认配置 - 确保总是返回有效的URL
        const defaultUrl = CONFIG.API.DEFAULT_SERVER_URL + '/api';
        CONFIG.log('debug', `使用默认服务器地址: ${defaultUrl}`);
        return defaultUrl;
    }

    /**
     * 获取存储的认证token
     */
    getStoredToken() {
        try {
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                const auth = JSON.parse(authData);
                return auth.token;
            }
        } catch (e) {
            CONFIG.log('warn', '获取存储的token失败:', e);
        }
        return null;
    }
    
    /**
     * 添加默认拦截器
     */
    addDefaultInterceptors() {
        // 请求拦截器 - 添加通用头部和认证
        this.addRequestInterceptor((config) => {
            config.headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...config.headers
            };

            // 添加认证头部
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    if (auth.token) {
                        config.headers['Authorization'] = `Bearer ${auth.token}`;
                    }
                } catch (e) {
                    CONFIG.log('warn', 'Invalid auth data in localStorage');
                }
            }

            // 调试日志
            if (CONFIG.DEBUG.SHOW_API_CALLS) {
                CONFIG.log('debug', `API Request: ${config.method} ${config.url}`, config);
            }

            return config;
        });
        
        // 响应拦截器 - 统一错误处理
        this.addResponseInterceptor(
            (response) => {
                // 调试日志
                if (CONFIG.DEBUG.SHOW_API_CALLS) {
                    CONFIG.log('debug', `API Response: ${response.status}`, response);
                }
                
                return response;
            },
            (error) => {
                CONFIG.log('error', 'API Error:', error);
                return Promise.reject(this.handleError(error));
            }
        );
    }
    
    /**
     * 添加请求拦截器
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     */
    addResponseInterceptor(onFulfilled, onRejected) {
        this.responseInterceptors.push({ onFulfilled, onRejected });
    }
    
    /**
     * 处理请求配置
     */
    processRequestConfig(config) {
        let processedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors) {
            processedConfig = interceptor(processedConfig);
        }
        
        return processedConfig;
    }
    
    /**
     * 处理响应
     */
    async processResponse(responsePromise) {
        try {
            let response = await responsePromise;
            
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onFulfilled) {
                    response = await interceptor.onFulfilled(response);
                }
            }
            
            return response;
        } catch (error) {
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.onRejected) {
                    error = await interceptor.onRejected(error);
                }
            }
            throw error;
        }
    }
    
    /**
     * 基础请求方法
     */
    async request(config) {
        const processedConfig = this.processRequestConfig({
            timeout: this.timeout,
            ...config
        });

        const url = processedConfig.url.startsWith('http')
            ? processedConfig.url
            : `${this.getBaseURL()}${processedConfig.url}`;

        const fetchConfig = {
            method: processedConfig.method || 'GET',
            headers: processedConfig.headers,
            signal: this.createAbortSignal(processedConfig.timeout)
        };

        // 添加请求体
        if (processedConfig.data) {
            if (processedConfig.data instanceof FormData) {
                fetchConfig.body = processedConfig.data;
                // 删除Content-Type，让浏览器自动设置
                delete fetchConfig.headers['Content-Type'];
            } else {
                fetchConfig.body = JSON.stringify(processedConfig.data);
            }
        }

        // 添加查询参数
        const finalUrl = this.buildURL(url, processedConfig.params);

        return this.processResponse(
            this.fetchWithRetry(finalUrl, fetchConfig, this.retryCount)
        );
    }
    
    /**
     * 创建超时信号
     */
    createAbortSignal(timeout) {
        const controller = new AbortController();
        setTimeout(() => controller.abort(), timeout);
        return controller.signal;
    }
    
    /**
     * 构建URL
     */
    buildURL(url, params) {
        if (!params) return url;
        
        const searchParams = new URLSearchParams();
        for (const [key, value] of Object.entries(params)) {
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        }
        
        const queryString = searchParams.toString();
        return queryString ? `${url}?${queryString}` : url;
    }
    
    /**
     * 带重试的fetch
     */
    async fetchWithRetry(url, config, retryCount) {
        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 尝试解析JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const data = await response.json();
                return { ...response, data };
            }
            
            return response;
        } catch (error) {
            if (retryCount > 0 && this.shouldRetry(error)) {
                CONFIG.log('warn', `Request failed, retrying... (${retryCount} attempts left)`, error);
                await this.delay(this.retryDelay);
                return this.fetchWithRetry(url, config, retryCount - 1);
            }
            throw error;
        }
    }
    
    /**
     * 判断是否应该重试
     */
    shouldRetry(error) {
        // 只对超时错误重试，不对网络连接错误重试
        return error.name === 'AbortError';
    }
    
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 错误处理
     */
    handleError(error) {
        let message = CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR;

        if (error.name === 'AbortError') {
            message = '请求超时，请重试';
        } else if (error.name === 'TypeError') {
            // 对于网络错误，不要显示用户消息，只记录日志
            if (error.message && error.message.includes('fetch')) {
                CONFIG.log('warn', 'Network error (fetch failed):', error.message);
                message = null; // 不显示用户消息
            } else {
                message = CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
            }
        } else if (error.message) {
            if (error.message.includes('404')) {
                message = CONFIG.ERROR_MESSAGES.FILE_NOT_FOUND;
            } else if (error.message.includes('403')) {
                message = CONFIG.ERROR_MESSAGES.PERMISSION_DENIED;
            } else if (error.message.includes('401')) {
                // 对于认证错误，不显示用户消息
                CONFIG.log('warn', 'Authentication error:', error.message);
                message = null;
            } else if (error.message.includes('500')) {
                message = CONFIG.ERROR_MESSAGES.SERVER_ERROR;
            }
        }

        return {
            ...error,
            userMessage: message
        };
    }
    
    // HTTP方法快捷方式
    get(url, config = {}) {
        return this.request({ ...config, method: 'GET', url });
    }
    
    post(url, data, config = {}) {
        return this.request({ ...config, method: 'POST', url, data });
    }
    
    put(url, data, config = {}) {
        return this.request({ ...config, method: 'PUT', url, data });
    }
    
    delete(url, config = {}) {
        return this.request({ ...config, method: 'DELETE', url });
    }
    
    patch(url, data, config = {}) {
        return this.request({ ...config, method: 'PATCH', url, data });
    }
}

// 创建API客户端实例
const api = new APIClient();

/**
 * 系统API
 */
class SystemAPI {
    /**
     * 获取系统信息
     */
    static async getSystemInfo() {
        try {
            const response = await api.get('/system/info');
            return response.data;
        } catch (error) {
            // 对于系统信息获取失败，只记录debug级别日志，不抛出错误
            CONFIG.log('debug', 'System info not available:', error.message);

            // 返回默认的系统信息而不是抛出错误
            return {
                version: '1.0.0',
                status: 'unknown',
                uptime: 0,
                services_count: 0
            };
        }
    }

    /**
     * 获取系统状态
     */
    static async getSystemStatus() {
        try {
            const response = await api.get('/server/status');
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to get system status:', error);
            throw error;
        }
    }

    /**
     * 获取在线用户列表（管理员）
     */
    static async getOnlineUsers() {
        try {
            const response = await api.get('/system/online-users');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get online users:', error);
            throw error;
        }
    }

    /**
     * 获取统计信息（管理员）
     */
    static async getStatistics() {
        try {
            const response = await api.get('/admin/stats');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get statistics:', error);
            throw error;
        }
    }

    /**
     * 获取通知列表
     */
    static async getNotifications() {
        try {
            // 暂时返回模拟数据，避免错误
            return {
                success: true,
                notifications: []
            };
        } catch (error) {
            CONFIG.log('error', 'Failed to get notifications:', error);
            throw error;
        }
    }

    /**
     * 发送通知
     */
    static async sendNotification(notification) {
        try {
            // 暂时返回成功，避免错误
            return {
                success: true
            };
        } catch (error) {
            CONFIG.log('error', 'Failed to send notification:', error);
            throw error;
        }
    }
}

/**
 * 用户API
 */
class UserAPI {
    /**
     * 获取用户信息
     */
    static async getUserInfo() {
        try {
            const response = await api.get('/user/info');
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get user info:', error);
            throw error;
        }
    }

    /**
     * 用户登出
     */
    static async logout() {
        try {
            const response = await api.post('/auth/logout');
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to logout:', error);
            throw error;
        }
    }
}

// 全局可用
window.SystemAPI = SystemAPI;
window.UserAPI = UserAPI;

/**
 * 文件API
 */
const FileAPI = {
    /**
     * 获取文件列表
     */
    async getFiles(folderId = null, params = {}) {
        const response = await api.get(CONFIG.API.ENDPOINTS.FILES, {
            params: { folder_id: folderId, ...params }
        });
        return response.data;
    },
    
    /**
     * 上传文件
     */
    async uploadFile(file, folderId = null, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);
        if (folderId) {
            formData.append('folder_id', folderId);
        }
        
        const response = await api.post(CONFIG.API.ENDPOINTS.UPLOAD, formData, {
            onUploadProgress: onProgress
        });
        return response.data;
    },
    
    /**
     * 下载文件
     */
    async downloadFile(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD.replace('{id}', fileId);
        const response = await api.get(endpoint, {
            responseType: 'blob'
        });
        return response;
    },

    /**
     * 单文件下载（压缩包形式）
     */
    async downloadSingleFile(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD_SINGLE.replace('{id}', fileId);
        const response = await api.post(endpoint);
        return response.data;
    },

    /**
     * 批量下载文件
     */
    async downloadBatchFiles(fileIds) {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_BATCH, {
            file_ids: fileIds
        });
        return response.data;
    },

    /**
     * 批量下载文件（返回blob）
     */
    async batchDownload(fileIds) {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_BATCH, {
            file_ids: fileIds
        }, {
            responseType: 'blob',
            timeout: 180000  // 3分钟超时，适用于批量下载
        });
        return response;
    },

    /**
     * 下载文件夹（返回blob）
     */
    async downloadFolder(folderId) {
        const endpoint = CONFIG.API.ENDPOINTS.DOWNLOAD_FOLDER.replace('{id}', folderId);
        const response = await api.get(endpoint, {
            responseType: 'blob',
            timeout: 300000  // 5分钟超时，适用于大文件夹下载
        });
        return response;
    },

    /**
     * 获取文件预览URL
     */
    getPreviewURL(fileId) {
        const endpoint = CONFIG.API.ENDPOINTS.PREVIEW.replace('{id}', fileId);
        return `${api.getBaseURL()}${endpoint}`;
    },

    /**
     * 获取缩略图URL
     */
    getThumbnailURL(fileId, size = 'medium') {
        const endpoint = CONFIG.API.ENDPOINTS.THUMBNAIL.replace('{id}', fileId);
        const baseUrl = `${api.getBaseURL()}${endpoint}?size=${size}`;

        // 临时不发送token，用于调试缩略图功能
        // TODO: 修复认证问题后重新启用token
        return baseUrl;

        // 添加认证token到URL参数中（因为img标签无法设置headers）
        // const authData = localStorage.getItem('fileShareAuth');
        // if (authData) {
        //     try {
        //         const auth = JSON.parse(authData);
        //         if (auth.token) {
        //             return `${baseUrl}&token=${encodeURIComponent(auth.token)}`;
        //         }
        //     } catch (e) {
        //         CONFIG.log('warn', 'Invalid auth data in localStorage');
        //     }
        // }
        //
        // return baseUrl;
    },

    /**
     * 申请下载密码
     */
    async requestDownloadPassword(fileId, reason = '') {
        const response = await api.post(CONFIG.API.ENDPOINTS.DOWNLOAD_PASSWORD, {
            file_id: fileId,
            reason: reason
        });
        return response.data;
    },

    /**
     * 获取用户密码申请记录
     */
    async getPasswordRequests(page = 1, limit = 50) {
        const response = await api.get(CONFIG.API.ENDPOINTS.PASSWORD_REQUESTS, {
            params: { page, limit }
        });
        return response.data;
    }
};

/**
 * 文件夹API
 */
const FolderAPI = {
    /**
     * 获取共享文件夹列表
     */
    async getSharedFolders() {
        const response = await api.get(CONFIG.API.ENDPOINTS.FOLDERS);
        return response.data;
    },

    /**
     * 扫描文件夹并预生成缩略图
     */
    async scanFolder(folderId, generateThumbnails = true) {
        const endpoint = CONFIG.API.ENDPOINTS.SCAN_FOLDER.replace('{id}', folderId);
        const response = await api.post(endpoint, {
            generate_thumbnails: generateThumbnails
        }, {
            timeout: 300000  // 5分钟超时
        });
        return response.data;
    },

    /**
     * 为文件夹生成缩略图
     */
    async generateThumbnails(folderId, forceRegenerate = false) {
        const endpoint = CONFIG.API.ENDPOINTS.GENERATE_FOLDER_THUMBNAILS.replace('{id}', folderId);
        const response = await api.post(endpoint, {
            force_regenerate: forceRegenerate
        }, {
            timeout: 300000  // 5分钟超时
        });
        return response.data;
    },

    /**
     * 为所有文件夹生成缩略图（管理员功能）
     */
    async generateAllThumbnails(forceRegenerate = false) {
        const response = await api.post(CONFIG.API.ENDPOINTS.GENERATE_ALL_THUMBNAILS, {
            force_regenerate: forceRegenerate
        }, {
            timeout: 600000  // 10分钟超时
        });
        return response.data;
    }
};

/**
 * 异步下载API
 */
const AsyncDownloadAPI = {
    /**
     * 创建异步文件夹下载任务
     */
    async createFolderDownload(folderId) {
        const endpoint = CONFIG.API.ENDPOINTS.ASYNC_DOWNLOAD_FOLDER.replace('{id}', folderId);
        const response = await api.post(endpoint);
        return response.data;
    },

    /**
     * 获取异步下载任务状态
     */
    async getTaskStatus(taskId) {
        const endpoint = CONFIG.API.ENDPOINTS.ASYNC_DOWNLOAD_STATUS.replace('{taskId}', taskId);
        const response = await api.get(endpoint);
        return response.data;
    },

    /**
     * 下载异步生成的文件
     */
    async downloadFile(taskId) {
        const endpoint = CONFIG.API.ENDPOINTS.ASYNC_DOWNLOAD_FILE.replace('{taskId}', taskId);
        const response = await api.get(endpoint, {
            responseType: 'blob'
        });
        return response;
    },

    /**
     * 获取用户的异步下载任务列表
     */
    async getUserTasks(limit = 50) {
        const response = await api.get(CONFIG.API.ENDPOINTS.ASYNC_DOWNLOAD_TASKS, {
            params: { limit }
        });
        return response.data;
    },

    /**
     * 轮询任务状态直到完成
     */
    async pollTaskStatus(taskId, onProgress = null, maxWaitTime = 1800000) { // 30分钟
        const startTime = Date.now();
        const pollInterval = 2000; // 2秒轮询一次

        return new Promise((resolve, reject) => {
            const poll = async () => {
                try {
                    // 检查超时
                    if (Date.now() - startTime > maxWaitTime) {
                        reject(new Error('任务等待超时'));
                        return;
                    }

                    const result = await this.getTaskStatus(taskId);

                    if (!result.success) {
                        reject(new Error(result.error || '获取任务状态失败'));
                        return;
                    }

                    const task = result.task;

                    // 调用进度回调
                    if (onProgress) {
                        onProgress(task);
                    }

                    // 检查任务状态
                    if (task.status === 'completed') {
                        resolve(task);
                    } else if (task.status === 'failed') {
                        reject(new Error(task.error_message || '任务执行失败'));
                    } else {
                        // 继续轮询
                        setTimeout(poll, pollInterval);
                    }

                } catch (error) {
                    reject(error);
                }
            };

            poll();
        });
    }
};

/**
 * 搜索API
 */
const SearchAPI = {
    /**
     * 搜索文件
     */
    async search(query, type = 'text', params = {}) {
        const response = await api.post(CONFIG.API.ENDPOINTS.SEARCH, {
            query: query,
            type: type,
            ...params
        });
        return response.data;
    },

    /**
     * 通过上传图片搜索相似图片
     */
    async searchByImage(formData, params = {}) {
        try {
            // 添加额外参数到FormData
            if (params.limit) {
                formData.append('limit', params.limit);
            }
            if (params.threshold) {
                formData.append('threshold', params.threshold);
            }

            const url = CONFIG.API.BASE_URL + CONFIG.API.ENDPOINTS.SEARCH_BY_IMAGE;
            CONFIG.log('info', 'Sending image search request to:', url);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${Utils.storage.get('token', '')}`
                },
                body: formData
            });

            CONFIG.log('info', 'Response status:', response.status);

            if (!response.ok) {
                let errorMessage = '图片搜索失败';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.error || errorMessage;
                } catch (e) {
                    errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                }
                throw new Error(errorMessage);
            }

            return await response.json();
        } catch (error) {
            CONFIG.log('error', 'Image search failed:', error);
            // 提供更详细的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络连接失败，请检查服务器是否正常运行');
            }
            throw error;
        }
    }
};



/**
 * 下载记录API
 */
const DownloadAPI = {
    /**
     * 获取用户下载记录
     */
    async getDownloadRecords(page = 1, pageSize = 50) {
        try {
            const params = { page, limit: pageSize };
            console.log('发送下载记录请求，参数:', params);

            const response = await api.get('/download/records', { params });
            console.log('下载记录API原始响应:', response);

            // 处理错误响应
            if (response && response.error) {
                console.error('API返回错误:', response.error);
                return { success: false, records: [], total: 0, error: response.error };
            }

            // 处理成功响应
            if (response && response.success === true) {
                console.log('API成功响应:', response);
                return {
                    success: true,
                    records: Array.isArray(response.records) ? response.records : [],
                    total: response.total || 0,
                    page: response.page || page,
                    limit: response.limit || pageSize
                };
            }

            // 处理其他格式的响应
            if (response && response.data) {
                console.log('使用response.data:', response.data);
                return {
                    success: true,
                    records: Array.isArray(response.data.records) ? response.data.records : [],
                    total: response.data.total || 0
                };
            }

            // 兜底处理
            console.warn('未知的响应格式，返回空结果');
            return { success: true, records: [], total: 0, error: '暂无下载记录' };
            
        } catch (error) {
            console.error('获取下载记录失败:', error);
            CONFIG.log('error', 'Failed to get download records:', error);
            
            // 如果是认证错误，返回特殊标识
            if (error.message && (error.message.includes('401') || error.message.includes('未授权'))) {
                return { success: false, records: [], total: 0, error: '请重新登录', needLogin: true };
            }
            
            return { success: false, records: [], total: 0, error: error.message || '获取下载记录失败' };
        }
    },



    /**
     * 获取下载记录统计
     */
    async getDownloadStats() {
        try {
            const response = await api.get('/download/stats');
            return response.data || response;
        } catch (error) {
            CONFIG.log('error', 'Failed to get download stats:', error);
            throw error;
        }
    },

    /**
     * 重新下载批量文件
     */
    async redownloadBatch(recordId) {
        try {
            const response = await api.post(`/download/redownload/batch/${recordId}`);
            return response.data || response;
        } catch (error) {
            CONFIG.log('error', 'Failed to redownload batch:', error);
            throw error;
        }
    },

    /**
     * 重新下载文件夹
     */
    async redownloadFolder(recordId) {
        try {
            const response = await api.post(`/download/redownload/folder/${recordId}`);
            return response.data || response;
        } catch (error) {
            CONFIG.log('error', 'Failed to redownload folder:', error);
            throw error;
        }
    },

    /**
     * 申请压缩包解压密码
     */
    async requestPackagePassword(recordId, reason) {
        try {
            const response = await api.post('/download/password/request', {
                record_id: recordId,
                reason: reason
            });
            return response.data || response;
        } catch (error) {
            CONFIG.log('error', 'Failed to request package password:', error);
            throw error;
        }
    }
};

/**
 * 统计API
 */
const StatsAPI = {
    /**
     * 获取存储统计
     */
    async getStorageStats() {
        const response = await api.get(CONFIG.API.ENDPOINTS.STORAGE);
        return response.data;
    }
};

/**
 * 配置管理API
 */
class ConfigAPI {
    /**
     * 获取公开配置（前端使用）
     */
    static async getPublicConfig() {
        try {
            const response = await api.get(CONFIG.API.ENDPOINTS.PUBLIC_CONFIG);
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get public config:', error);
            throw error;
        }
    }

    /**
     * 获取系统配置（管理员）
     */
    static async getSystemConfig() {
        try {
            const response = await api.get(CONFIG.API.ENDPOINTS.ADMIN_CONFIG);
            return response.data;
        } catch (error) {
            CONFIG.log('error', 'Failed to get system config:', error);
            throw error;
        }
    }

    /**
     * 更新系统配置（管理员）
     */
    static async updateSystemConfig(config) {
        try {
            const response = await api.put(CONFIG.API.ENDPOINTS.ADMIN_CONFIG, config);
            return response;
        } catch (error) {
            CONFIG.log('error', 'Failed to update system config:', error);
            throw error;
        }
    }

    /**
     * 应用配置到前端
     */
    static applyConfig(config) {
        try {
            // 更新API基础URL
            if (config.server) {
                const newBaseUrl = `http://${config.server.host}:${config.server.port}/api`;
                api.baseURL = newBaseUrl;
                CONFIG.API.DEFAULT_SERVER_URL = `http://${config.server.host}:${config.server.port}`;
            }

            // 更新文件配置
            if (config.file_share) {
                CONFIG.FILE.ALLOWED_EXTENSIONS = config.file_share.allowed_extensions || [];
                CONFIG.FILE.MAX_FILE_SIZE = config.file_share.max_file_size || 1073741824;
                CONFIG.FILE.THUMBNAIL_SIZES = config.file_share.thumbnail_sizes || {};
            }

            // 更新下载配置
            if (config.download) {
                CONFIG.DOWNLOAD = {
                    ...CONFIG.DOWNLOAD,
                    ...config.download
                };
            }

            // 更新搜索配置
            if (config.search) {
                CONFIG.SEARCH = {
                    ...CONFIG.SEARCH,
                    ...config.search
                };
            }

            // 更新通知配置
            if (config.notifications) {
                CONFIG.NOTIFICATIONS = {
                    ...CONFIG.NOTIFICATIONS,
                    ...config.notifications
                };
            }

            // 保存配置到本地存储
            localStorage.setItem('system_config', JSON.stringify(config));

            CONFIG.log('info', 'Configuration applied successfully');

        } catch (error) {
            CONFIG.log('error', 'Failed to apply configuration:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载配置
     */
    static loadLocalConfig() {
        try {
            const savedConfig = localStorage.getItem('system_config');
            if (savedConfig) {
                const config = JSON.parse(savedConfig);
                this.applyConfig(config);
                return config;
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to load local config:', error);
        }
        return null;
    }

    /**
     * 初始化配置（应用启动时调用）
     */
    static async initializeConfig() {
        try {
            // 先尝试加载本地配置
            this.loadLocalConfig();

            // 然后获取最新的服务器配置
            const serverConfig = await this.getPublicConfig();
            if (serverConfig) {
                this.applyConfig(serverConfig);
            }

            return serverConfig;
        } catch (error) {
            CONFIG.log('warn', 'Failed to initialize config from server, using local config');
            return this.loadLocalConfig();
        }
    }
}

// 导出API
window.api = api;
window.FileAPI = FileAPI;
window.FolderAPI = FolderAPI;
window.SearchAPI = SearchAPI;
window.DownloadAPI = DownloadAPI;
window.SystemAPI = SystemAPI;
window.UserAPI = UserAPI;
window.StatsAPI = StatsAPI;
window.ConfigAPI = ConfigAPI;
