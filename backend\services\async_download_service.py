"""
异步下载服务
处理大文件夹的异步打包和下载
"""

import os
import threading
import time
import uuid
import zipfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor

from utils.logger import setup_logger
from models.async_download import AsyncDownloadTask, DownloadStatus
from models.file_share import SharedFolder, SharedFile


class AsyncDownloadService:
    """异步下载服务"""
    
    def __init__(self, db_manager, download_service, config=None):
        self.db_manager = db_manager
        self.download_service = download_service
        self.config = config
        self.logger = setup_logger("AsyncDownloadService")
        
        # 线程池用于处理下载任务
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="AsyncDownload")
        
        # 任务状态缓存
        self.task_cache = {}
        
        # 启动清理线程
        self.start_cleanup_thread()
        
        self.logger.info("异步下载服务已启动")
    
    def create_async_download_task(self, download_type: str, target_id: int, 
                                 target_name: str, user_id: int = None,
                                 request_context: dict = None) -> Dict[str, Any]:
        """创建异步下载任务"""
        try:
            task_id = str(uuid.uuid4())
            
            with self.db_manager.get_session() as session:
                # 创建任务记录
                task = AsyncDownloadTask(
                    task_id=task_id,
                    user_id=user_id,
                    download_type=download_type,
                    target_id=target_id,
                    target_name=target_name,
                    status=DownloadStatus.PENDING.value,
                    expires_at=datetime.now() + timedelta(hours=24),  # 24小时后过期
                    request_context=request_context
                )
                
                session.add(task)
                session.commit()
                
                # 提交到线程池处理
                self.executor.submit(self._process_download_task, task_id)
                
                self.logger.info(f"创建异步下载任务: {task_id}, 类型: {download_type}, 目标: {target_name}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "message": "下载任务已创建，正在后台处理..."
                }
                
        except Exception as e:
            self.logger.error(f"创建异步下载任务失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            with self.db_manager.get_session() as session:
                task = session.query(AsyncDownloadTask).filter_by(task_id=task_id).first()
                if not task:
                    return {"success": False, "error": "任务不存在"}
                
                return {
                    "success": True,
                    "task": task.to_dict()
                }
                
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_user_tasks(self, user_id: int, limit: int = 50) -> Dict[str, Any]:
        """获取用户的下载任务列表"""
        try:
            with self.db_manager.get_session() as session:
                tasks = session.query(AsyncDownloadTask).filter_by(
                    user_id=user_id
                ).order_by(AsyncDownloadTask.created_at.desc()).limit(limit).all()
                
                return {
                    "success": True,
                    "tasks": [task.to_dict() for task in tasks]
                }
                
        except Exception as e:
            self.logger.error(f"获取用户任务列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _process_download_task(self, task_id: str):
        """处理下载任务（在后台线程中执行）"""
        try:
            with self.db_manager.get_session() as session:
                task = session.query(AsyncDownloadTask).filter_by(task_id=task_id).first()
                if not task:
                    self.logger.error(f"任务不存在: {task_id}")
                    return
                
                # 更新状态为处理中
                task.update_progress(0, DownloadStatus.PROCESSING.value)
                session.commit()
                
                self.logger.info(f"开始处理下载任务: {task_id}")
                
                if task.download_type == 'folder':
                    self._process_folder_download(task, session)
                elif task.download_type == 'batch':
                    self._process_batch_download(task, session)
                else:
                    task.mark_failed("不支持的下载类型")
                    session.commit()
                    return
                
                self.logger.info(f"下载任务处理完成: {task_id}")
                
        except Exception as e:
            self.logger.error(f"处理下载任务失败 {task_id}: {e}")
            try:
                with self.db_manager.get_session() as session:
                    task = session.query(AsyncDownloadTask).filter_by(task_id=task_id).first()
                    if task:
                        task.mark_failed(str(e))
                        session.commit()
            except:
                pass
    
    def _process_folder_download(self, task: AsyncDownloadTask, session):
        """处理文件夹下载"""
        try:
            # 获取文件夹信息
            folder = session.query(SharedFolder).filter_by(id=task.target_id).first()
            if not folder:
                task.mark_failed("文件夹不存在")
                session.commit()
                return
            
            if not os.path.exists(folder.path):
                task.mark_failed("文件夹路径不存在")
                session.commit()
                return
            
            # 获取所有文件
            files = session.query(SharedFile).filter_by(folder_id=task.target_id).all()
            valid_files = []
            total_size = 0
            
            for file_record in files:
                full_path = os.path.join(folder.path, file_record.relative_path)
                if os.path.exists(full_path):
                    valid_files.append({
                        'full_path': full_path,
                        'archive_name': file_record.relative_path,
                        'size': file_record.file_size or 0
                    })
                    total_size += file_record.file_size or 0
            
            if not valid_files:
                task.mark_failed("文件夹中没有有效的文件")
                session.commit()
                return
            
            # 更新任务信息
            task.total_files = len(valid_files)
            task.total_size = total_size
            session.commit()
            
            # 创建ZIP文件
            zip_name = f"folder_{folder.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            zip_path = os.path.join(self.download_service.temp_dir, zip_name)
            
            self._create_zip_with_progress(valid_files, zip_path, task, session)
            
        except Exception as e:
            task.mark_failed(str(e))
            session.commit()
            raise
    
    def _create_zip_with_progress(self, files: List[Dict], zip_path: str, 
                                task: AsyncDownloadTask, session):
        """创建ZIP文件并更新进度"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=1) as zipf:
                for i, file_info in enumerate(files):
                    try:
                        zipf.write(file_info['full_path'], file_info['archive_name'])
                        
                        # 更新进度
                        task.update_progress(i + 1)
                        if i % 10 == 0:  # 每10个文件提交一次
                            session.commit()
                            
                    except Exception as e:
                        self.logger.warning(f"添加文件到ZIP失败 {file_info['full_path']}: {e}")
                        continue
            
            # 获取ZIP文件大小
            zip_size = os.path.getsize(zip_path)
            
            # 生成下载URL
            download_url = f"/api/download/async/{task.task_id}"
            
            # 标记完成
            task.mark_completed(zip_path, zip_size, download_url)
            session.commit()
            
            self.logger.info(f"ZIP文件创建完成: {zip_path}, 大小: {zip_size} 字节")
            
        except Exception as e:
            if os.path.exists(zip_path):
                try:
                    os.remove(zip_path)
                except:
                    pass
            raise
    
    def start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_expired_tasks():
            while True:
                try:
                    self._cleanup_expired_tasks()
                    time.sleep(3600)  # 每小时清理一次
                except Exception as e:
                    self.logger.error(f"清理过期任务失败: {e}")
                    time.sleep(300)  # 出错后5分钟重试
        
        cleanup_thread = threading.Thread(target=cleanup_expired_tasks, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_expired_tasks(self):
        """清理过期任务"""
        try:
            with self.db_manager.get_session() as session:
                expired_tasks = session.query(AsyncDownloadTask).filter(
                    AsyncDownloadTask.expires_at < datetime.now()
                ).all()
                
                for task in expired_tasks:
                    # 删除ZIP文件
                    if task.zip_path and os.path.exists(task.zip_path):
                        try:
                            os.remove(task.zip_path)
                            self.logger.info(f"删除过期文件: {task.zip_path}")
                        except Exception as e:
                            self.logger.warning(f"删除过期文件失败: {e}")
                    
                    # 删除任务记录
                    session.delete(task)
                
                if expired_tasks:
                    session.commit()
                    self.logger.info(f"清理了 {len(expired_tasks)} 个过期任务")
                    
        except Exception as e:
            self.logger.error(f"清理过期任务失败: {e}")
    
    def download_async_file(self, task_id: str) -> Dict[str, Any]:
        """下载异步生成的文件"""
        try:
            with self.db_manager.get_session() as session:
                task = session.query(AsyncDownloadTask).filter_by(task_id=task_id).first()
                if not task:
                    return {"success": False, "error": "任务不存在"}
                
                if task.status != DownloadStatus.COMPLETED.value:
                    return {"success": False, "error": "任务尚未完成"}
                
                if not task.zip_path or not os.path.exists(task.zip_path):
                    return {"success": False, "error": "文件不存在或已过期"}
                
                return {
                    "success": True,
                    "file_path": task.zip_path,
                    "filename": os.path.basename(task.zip_path),
                    "size": task.zip_size
                }
                
        except Exception as e:
            self.logger.error(f"获取异步下载文件失败: {e}")
            return {"success": False, "error": str(e)}
