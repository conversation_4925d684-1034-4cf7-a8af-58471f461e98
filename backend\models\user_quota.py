#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户配额和限流管理模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, BigInteger, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from config.database import Base
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class UserQuota(Base):
    """用户配额模型"""
    
    __tablename__ = 'user_quotas'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, unique=True, comment='用户ID')
    
    # 搜索配额
    daily_search_limit = Column(Integer, default=100, comment='每日搜索次数限制')
    hourly_search_limit = Column(Integer, default=20, comment='每小时搜索次数限制')
    
    # 下载配额
    daily_download_limit = Column(Integer, default=50, comment='每日下载次数限制')
    hourly_download_limit = Column(Integer, default=10, comment='每小时下载次数限制')
    daily_download_size_limit = Column(BigInteger, default=1073741824, comment='每日下载大小限制（字节）')  # 1GB
    
    # 当前使用量
    today_searches = Column(Integer, default=0, comment='今日搜索次数')
    today_downloads = Column(Integer, default=0, comment='今日下载次数')
    today_download_size = Column(BigInteger, default=0, comment='今日下载大小')
    
    # 限制状态
    is_search_limited = Column(Boolean, default=False, comment='是否搜索受限')
    is_download_limited = Column(Boolean, default=False, comment='是否下载受限')
    search_limit_until = Column(DateTime, nullable=True, comment='搜索限制到期时间')
    download_limit_until = Column(DateTime, nullable=True, comment='下载限制到期时间')
    
    # 违规记录
    violation_count = Column(Integer, default=0, comment='违规次数')
    last_violation = Column(DateTime, nullable=True, comment='最后违规时间')
    
    # 时间戳
    last_reset = Column(DateTime, default=func.now(), comment='最后重置时间')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    user = relationship("User", backref="quota")
    
    def __init__(self, user_id: int, **kwargs):
        self.user_id = user_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def reset_daily_quota(self):
        """重置每日配额"""
        self.today_searches = 0
        self.today_downloads = 0
        self.today_download_size = 0
        self.last_reset = datetime.now()
    
    def can_search(self) -> tuple[bool, str]:
        """检查是否可以搜索"""
        if self.is_search_limited and self.search_limit_until and datetime.now() < self.search_limit_until:
            return False, f"搜索功能被限制到 {self.search_limit_until.strftime('%Y-%m-%d %H:%M:%S')}"
        
        if self.today_searches >= self.daily_search_limit:
            return False, "今日搜索次数已达上限"
        
        return True, ""
    
    def can_download(self, file_size: int = 0) -> tuple[bool, str]:
        """检查是否可以下载"""
        if self.is_download_limited and self.download_limit_until and datetime.now() < self.download_limit_until:
            return False, f"下载功能被限制到 {self.download_limit_until.strftime('%Y-%m-%d %H:%M:%S')}"
        
        if self.today_downloads >= self.daily_download_limit:
            return False, "今日下载次数已达上限"
        
        if self.today_download_size + file_size > self.daily_download_size_limit:
            return False, "今日下载大小已达上限"
        
        return True, ""
    
    def add_search(self):
        """增加搜索次数"""
        if self.today_searches is None:
            self.today_searches = 0
        self.today_searches += 1
    
    def add_download(self, file_size: int = 0):
        """增加下载次数和大小"""
        if self.today_downloads is None:
            self.today_downloads = 0
        if self.today_download_size is None:
            self.today_download_size = 0
        
        self.today_downloads += 1
        self.today_download_size += file_size
    
    def add_violation(self, limit_hours: int = 24):
        """增加违规记录"""
        if self.violation_count is None:
            self.violation_count = 0
        
        self.violation_count += 1
        self.last_violation = datetime.now()
        
        # 根据违规次数设置限制时间
        if self.violation_count >= 3:
            # 3次违规，限制下载和搜索
            limit_time = datetime.now() + timedelta(hours=limit_hours)
            self.is_search_limited = True
            self.is_download_limited = True
            self.search_limit_until = limit_time
            self.download_limit_until = limit_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'daily_search_limit': self.daily_search_limit,
            'hourly_search_limit': self.hourly_search_limit,
            'daily_download_limit': self.daily_download_limit,
            'hourly_download_limit': self.hourly_download_limit,
            'daily_download_size_limit': self.daily_download_size_limit,
            'today_searches': self.today_searches,
            'today_downloads': self.today_downloads,
            'today_download_size': self.today_download_size,
            'is_search_limited': self.is_search_limited,
            'is_download_limited': self.is_download_limited,
            'search_limit_until': self.search_limit_until.isoformat() if self.search_limit_until else None,
            'download_limit_until': self.download_limit_until.isoformat() if self.download_limit_until else None,
            'violation_count': self.violation_count,
            'last_violation': self.last_violation.isoformat() if self.last_violation else None,
            'last_reset': self.last_reset.isoformat() if self.last_reset else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class UserBan(Base):
    """用户封禁记录模型"""

    __tablename__ = 'user_bans'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')

    # 封禁信息
    ban_type = Column(String(20), default='login', comment='封禁类型: login/search/download/all')
    reason = Column(Text, nullable=True, comment='封禁原因')
    duration_hours = Column(Integer, default=24, comment='封禁时长（小时）')
    is_permanent = Column(Boolean, default=False, comment='是否永久封禁')

    # 状态信息
    is_active = Column(Boolean, default=True, comment='是否生效')
    banned_at = Column(DateTime, default=func.now(), comment='封禁时间')
    expires_at = Column(DateTime, nullable=True, comment='到期时间')
    unbanned_at = Column(DateTime, nullable=True, comment='解封时间')
    
    # 操作信息
    banned_by = Column(Integer, ForeignKey('users.id'), nullable=True, comment='操作管理员ID')
    unbanned_by = Column(Integer, ForeignKey('users.id'), nullable=True, comment='解封管理员ID')
    admin_note = Column(Text, nullable=True, comment='管理员备注')
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id], backref="bans")
    admin = relationship("User", foreign_keys=[banned_by])
    
    def __init__(self, user_id: int, ban_type: str = 'login', duration_hours: int = 24, **kwargs):
        self.user_id = user_id
        self.ban_type = ban_type
        self.duration_hours = duration_hours
        self.is_permanent = kwargs.get('is_permanent', False)

        if self.is_permanent:
            self.expires_at = None
        else:
            self.expires_at = datetime.now() + timedelta(hours=duration_hours)

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        if self.is_permanent:
            return False

        if not self.expires_at:
            return True

        return datetime.now() > self.expires_at
    
    def unban(self, admin_id: int = None):
        """解封用户"""
        self.is_active = False
        self.unbanned_at = datetime.now()
        if admin_id:
            self.unbanned_by = admin_id
    
    def get_remaining_time(self) -> Optional[timedelta]:
        """获取剩余时间"""
        if self.is_permanent:
            return None

        if self.is_expired():
            return timedelta(0)

        if not self.expires_at:
            return timedelta(0)

        return self.expires_at - datetime.now()

    def extend_ban(self, additional_hours: int):
        """延长封禁时间"""
        if self.is_permanent:
            return

        if self.expires_at:
            self.expires_at += timedelta(hours=additional_hours)
            self.duration_hours += additional_hours

    def make_permanent(self):
        """设为永久封禁"""
        self.is_permanent = True
        self.expires_at = None
        self.duration_hours = 0

    def get_ban_status_text(self) -> str:
        """获取封禁状态文本"""
        if not self.is_active:
            return "已解封"

        if self.is_permanent:
            return "永久封禁"

        if self.is_expired():
            return "已过期"

        remaining = self.get_remaining_time()
        if remaining:
            days = remaining.days
            hours = remaining.seconds // 3600
            minutes = (remaining.seconds % 3600) // 60

            if days > 0:
                return f"剩余{days}天{hours}小时"
            elif hours > 0:
                return f"剩余{hours}小时{minutes}分钟"
            else:
                return f"剩余{minutes}分钟"

        return "即将到期"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        remaining_time = self.get_remaining_time()

        # 格式化剩余时间显示
        remaining_time_text = ""
        if self.is_permanent:
            remaining_time_text = "永久"
        elif not self.is_active:
            remaining_time_text = "已解封"
        elif self.is_expired():
            remaining_time_text = "已过期"
        elif remaining_time:
            days = remaining_time.days
            hours = remaining_time.seconds // 3600
            minutes = (remaining_time.seconds % 3600) // 60

            if days > 0:
                remaining_time_text = f"{days}天{hours}小时"
            elif hours > 0:
                remaining_time_text = f"{hours}小时{minutes}分钟"
            else:
                remaining_time_text = f"{minutes}分钟"
        else:
            remaining_time_text = "即将到期"

        return {
            'id': self.id,
            'user_id': self.user_id,
            'ban_type': self.ban_type,
            'reason': self.reason,
            'duration_hours': self.duration_hours,
            'is_permanent': self.is_permanent,
            'is_active': self.is_active,
            'banned_at': self.banned_at.isoformat() if self.banned_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'unbanned_at': self.unbanned_at.isoformat() if self.unbanned_at else None,
            'banned_by': self.banned_by,
            'unbanned_by': self.unbanned_by,
            'admin_note': self.admin_note,
            'remaining_seconds': int(remaining_time.total_seconds()) if remaining_time else None,
            'remaining_time': remaining_time_text,  # 添加格式化的剩余时间文本
            'status_text': self.get_ban_status_text(),
            'is_expired': self.is_expired()
        }
