<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE locale [
<!ELEMENT locale (exception)>
<!ELEMENT exception (ANY)+>
<!ELEMENT warning (message)+>
<!ELEMENT error (message)+>
<!ELEMENT fatalerror (message)+>
<!ELEMENT message (#PCDATA)>
<!ATTLIST locale name CDATA #REQUIRED>
<!ATTLIST message name CDATA #REQUIRED>
]>
<locale name="english">
  <exception>
    <blob>
      <error>
        <message name="UnableToOpenBlob">
          unable to open image
        </message>
        <message name="UnableToOpenFile">
          unable to open file
        </message>
        <message name="UnableToReadBlob">
          unable to read blob
        </message>
        <message name="UnableToWriteBlob">
          unable to write blob
        </message>
        <message name="UnrecognizedImageFormat">
          unrecognized image format
        </message>
        <message name="ZeroLengthBlobNotPermitted">
          zero-length blob not permitted
        </message>
      </error>
    </blob>
    <cache>
      <error>
        <message name="CacheResourcesExhausted">
          cache resources exhausted
        </message>
        <message name="IncompatibleAPI">
          incompatible API
        </message>
        <message name="NoPixelsDefinedInCache">
          no pixels defined in cache
        </message>
        <message name="PixelCacheIsNotOpen">
          pixel cache is not open
        </message>
        <message name="PixelsAreNotAuthentic">
          pixels are not authentic
        </message>
        <message name="UnableToCloneCache">
          unable to clone cache
        </message>
        <message name="UnableToExtendCache">
          unable to extend cache
        </message>
        <message name="UnableToGetCacheNexus">
          unable to get cache nexus
        </message>
        <message name="UnableToOpenPixelCache">
          unable to open pixel cache
        </message>
        <message name="UnableToPersistPixelCache">
          unable to persist pixel cache
        </message>
        <message name="UnableToReadPixelCache">
          unable to read pixel cache
        </message>
        <message name="UnableToWritePixelCache">
          unable to write pixel cache
        </message>
      </error>
      <fatalerror>
        <message name="UnableToAcquireCacheView">
          unable to acquire cache view
        </message>
        <message name="UnableToExtendPixelCache">
          unable to extend pixel cache
        </message>
      </fatalerror>
    </cache>
    <coder>
      <error>
        <message name="ColormapTypeNotSupported">
          colormap type not supported
        </message>
        <message name="ColorspaceModelIsNotSupported">
          colorspace model is not supported
        </message>
        <message name="CompressNotSupported">
          compression not supported
        </message>
        <message name="DataEncodingSchemeIsNotSupported">
          data encoding scheme is not supported
        </message>
        <message name="DataStorageTypeIsNotSupported">
          data storage type is not supported
        </message>
        <message name="DeltaPNGNotSupported">
          delta-PNG is not supported
        </message>
        <message name="EncryptedWPGImageFileNotSupported">
          encrypted WPG image file not supported
        </message>
        <message name="FractalCompressNotSupported">
          fractal compression not supported
        </message>
        <message name="ImageColumnOrRowSizeIsNotSupported">
          image column or row size is not supported
        </message>
        <message name="ImageDoesNotHaveAClipMask">
          image does not have a clip mask
        </message>
        <message name="ImageDoesNotHaveAnAlphaChannel">
          image does not have an alpha channel
        </message>
        <message name="ImageDoesNotHaveAMaskChannel">
          image does not have an mask channel
        </message>
        <message name="ImageDoesNotHaveAThumbnail">
          image does not have a EXIF thumbnail
        </message>
        <message name="ImageIsNotTiled">
          image is not tiled
        </message>
        <message name="IrregularChannelGeometryNotSupported">
          irregular channel geometry not supported
        </message>
        <message name="JNGCompressNotSupported">
          JNG compression not supported
        </message>
        <message name="JPEGCompressNotSupported">
          JPEG compression not supported
        </message>
        <message name="JPEGEmbeddingFailed">
          JPEG embedding failed
        </message>
        <message name="LocationTypeIsNotSupported">
          location type is not supported
        </message>
        <message name="MapStorageTypeIsNotSupported">
          map storage type is not supported
        </message>
        <message name="MultidimensionalMatricesAreNotSupported">
          multi-dimensional matrices are not supported
        </message>
        <message name="MultipleRecordListNotSupported">
          multiple record list not supported
        </message>
        <message name="NoBitmapOnClipboard">
          no bitmap on clipboard
        </message>
        <message name="NoAPP1DataIsAvailable">
          no APP1 data is available
        </message>
        <message name="No8BIMDataIsAvailable">
          no 8BIM data is available
        </message>
        <message name="NoColorProfileIsAvailable">
          no color profile is available
        </message>
        <message name="NoDataReturned">
          no data returned
        </message>
        <message name="NoImageVectorGraphics">
          no image vector graphics; unable to generate SVG
        </message>
        <message name="NoIPTCProfileAvailable">
          no IPTC profile available
        </message>
        <message name="NumberOfImagesIsNotSupported">
          number of images is not supported
        </message>
        <message name="OnlyContinuousTonePictureSupported">
          only continuous tone picture supported
        </message>
        <message name="OnlyLevelZerofilesSupported">
          only level zero files Supported
        </message>
        <message name="PNGCompressNotSupported">
          PNG compression not supported
        </message>
        <message name="RLECompressNotSupported">
          RLE compression not supported
        </message>
        <message name="UnableToCopyProfile">
          unable to copy profile
        </message>
        <message name="UnableToCreateBitmap">
          unable to create bitmap
        </message>
        <message name="UnableToCreateADC">
          unable to create a DC
        </message>
        <message name="UnableToDecompressImage">
          unable to decompress image
        </message>
        <message name="UnableToWriteMPEGParameters">
          unable to write MPEG parameters
        </message>
        <message name="UnableToZipCompressImage">
          unable to zip-compress image
        </message>
        <message name="ZIPCompressNotSupported">
          ZIP compression not supported
        </message>
      </error>
      <warning>
        <message name="ExifProfileSizeExceedsLimit">
          exif profile size exceeds limit and will be truncated
        </message>
        <message name="LosslessToLossyJPEGConversion">
          lossless to lossy JPEG conversion
        </message>
      </warning>
    </coder>
    <configure>
      <error>
        <message name="IncludeElementNestedTooDeeply">
          include element nested too deeply
        </message>
        <message name="PolicyValidationException">
          security policy failed to validate
        </message>
      </error>
      <warning>
        <message name="UnableToOpenConfigureFile">
          unable to access configure file
        </message>
        <message name="UnableToOpenModuleFile">
          unable to open module file
        </message>
      </warning>
    </configure>
    <corrupt>
      <image>
        <error>
          <message name="AnErrorHasOccurredReadingFromFile">
            an error has occurred reading from file
          </message>
          <message name="AnErrorHasOccurredWritingToFile">
            an error has occurred writing to file
          </message>
          <message name="CipherSupportNotEnabled">
            cipher support not enabled
          </message>
          <message name="ColormapExceeds256Colors">
            colormap exceeded 256 colors
          </message>
          <message name="CorruptImage">
            corrupt image
          </message>
          <message name="FileFormatVersionMismatch">
            file format version mismatch
          </message>
          <message name="ImageDepthNotSupported">
            image depth not supported
          </message>
          <message name="ImageFileDoesNotContainAnyImageData">
            image file does not contain any image data
          </message>
          <message name="ImageTypeNotSupported">
            image type not supported
          </message>
          <message name="ImproperImageHeader">
            improper image header
          </message>
          <message name="InsufficientImageDataInFile">
            insufficient image data in file
          </message>
          <message name="InvalidColormapIndex">
            invalid colormap index
          </message>
          <message name="InvalidPixel">
            invalid pixel
          </message>
          <message name="LengthAndFilesizeDoNotMatch">
            length and filesize do not match
          </message>
          <message name="MaximumChannelsExceeded">
            maximum channels exceeded
          </message>
          <message name="MissingImageChannel">
            missing image channel
          </message>
          <message name="NegativeOrZeroImageSize">
            negative or zero image size
          </message>
          <message name="NonOS2HeaderSizeError">
            non OS2 BMP header size less than 40
          </message>
          <message name="NotEnoughPixelData">
            not enough pixel data
          </message>
          <message name="NotEnoughTiles">
            not enough tiles found in level
          </message>
          <message name="TooMuchImageDataInFile">
            too much image data in file
          </message>
          <message name="StaticPlanesValueNotEqualToOne">
            static planes value not equal to 1
          </message>
          <message name="UnableToReadExtensionBlock">
            unable to read extension block
          </message>
          <message name="UnableToReadImageHeader">
            unable to read image header
          </message>
          <message name="UnableToReadImageData">
            unable to read image data
          </message>
          <message name="UnableToRunlengthDecodeImage">
            unable to runlength decode image
          </message>
          <message name="UnableToUncompressImage">
            unable to uncompress image
          </message>
          <message name="UnexpectedEndOfFile">
            unexpected end-of-file
          </message>
          <message name="UnexpectedSamplingFactor">
            unexpected sampling factor
          </message>
          <message name="UnknownPatternType">
            unknown pattern type
          </message>
          <message name="UnrecognizedAlphaChannelOption">
            unrecognized alpha channel option
          </message>
          <message name="UnrecognizedImageCompression">
            unrecognized compression
          </message>
          <message name="UnrecognizedNumberOfColors">
            unrecognized number of colors
          </message>
          <message name="UnsupportedBitsPerPixel">
            unsupported bits per pixel
          </message>
        </error>
        <fatalerror>
          <message name="UnableToPersistKey">
            unable to persist key
          </message>
        </fatalerror>
        <warning>
          <message name="InsufficientImageDataInFile">
            insufficient image data in file
          </message>
          <message name="LengthAndFilesizeDoNotMatch">
            length and filesize do not match
          </message>
          <message name="SkipToSyncByte">
            corrupt PCD image, skipping to sync byte
          </message>
        </warning>
      </image>
    </corrupt>
    <delegate>
      <error>
        <message name="DelegateFailed">
          delegate failed
        </message>
        <message name="FailedToComputeOutputSize">
          failed to compute output size
        </message>
        <message name="FailedToRenderFile">
          failed to render file
        </message>
        <message name="FailedToScanFile">
          failed to scan file
        </message>
        <message name="NoTagFound">
          no tag found
        </message>
        <message name="PCLDelegateFailed">
          PCL delegate failed
        </message>
        <message name="PostscriptDelegateFailed">
          Postscript delegate failed
        </message>
        <message name="UnableToCreateImage">
          unable to create image
        </message>
        <message name="UnableToDecodeImageFile">
          unable to decode image file
        </message>
        <message name="UnableToEncodeImageFile">
          unable to encode image file
        </message>
        <message name="UnableToInitializeFPXLibrary">
          unable to initialize FPX library
        </message>
        <message name="UnableToInitializeWMFLibrary">
          unable to initialize WMF library
        </message>
        <message name="UnableToManageJP2Stream">
          unable to manage JP2 stream
        </message>
        <message name="UnableToReadAspectRatio">
          unable to read aspect ratio
        </message>
        <message name="UnableToReadSummaryInfo">
          unable to read summary info
        </message>
        <message name="UnableToSetAffineMatrix">
          unable to set affine matrix
        </message>
        <message name="UnableToSetAspectRatio">
          unable to set aspect ratio
        </message>
        <message name="UnableToSetColorTwist">
          unable to set color twist
        </message>
        <message name="UnableToSetContrast">
          unable to set contrast
        </message>
        <message name="UnableToSetFilteringValue">
          unable to set filtering value
        </message>
        <message name="UnableToSetImageTitle">
          unable to set image title
        </message>
        <message name="UnableToSetJPEGLevel">
          unable to set JPEG level
        </message>
        <message name="UnableToSetRegionOfInterest">
          unable to set region of interest
        </message>
        <message name="UnableToSetSummaryInfo">
          unable to set summary info
        </message>
        <message name="UnableToWriteSVGFormat">
          unable to write SVG format
        </message>
        <message name="XPSDelegateFailed">
          XPS delegate failed
        </message>
      </error>
    </delegate>
    <draw>
      <error>
        <message name="AlreadyPushingPatternDefinition">
          already pushing pattern definition
        </message>
        <message name="NonconformingDrawingPrimitiveDefinition">
          non-conforming drawing primitive definition
        </message>
        <message name="NotARelativeURL">
          not a relative URL
        </message>
        <message name="NotCurrentlyPushingPatternDefinition">
          not currently pushing pattern definition
        </message>
        <message name="SegmentStackOverflow">
          segment stack overflow
        </message>
        <message name="TooManyBezierCoordinates">
          too many bezier coordinates
        </message>
        <message name="UnableToPrint">
          unable to print
        </message>
        <message name="UnbalancedGraphicContextPushPop">
          unbalanced graphic context push-pop
        </message>
        <message name="URLNotFound">
          URL not found
        </message>
        <message name="VectorGraphicsNestedTooDeeply">
          vector graphics nested too deeply
        </message>
      </error>
    </draw>
    <file>
      <open>
        <error>
          <message name="AnErrorHasOccurredReadingFromFile">
            an error has occurred reading from file
          </message>
          <message name="UnableToCreateTemporaryFile">
            unable to create temporary file
          </message>
          <message name="UnableToOpenFile">
            unable to open file
          </message>
          <message name="UnableToWriteFile">
            unable to write file
          </message>
        </error>
      </open>
    </file>
    <image>
      <error>
        <message name="AngleIsDiscontinuous">
          angle is discontinuous
        </message>
        <message name="ColormappedImageRequired">
          colormapped image required
        </message>
        <message name="ColorSeparatedImageRequired">
          color separated image required
        </message>
        <message name="ColorspaceColorProfileMismatch">
          color profile operates on another colorspace
        </message>
        <message name="ImageDepthNotSupported">
          image depth not supported
        </message>
        <message name="ImageSequenceRequired">
          image sequence is required
        </message>
        <message name="ImageMorphologyDiffers">
          image morphology differs
        </message>
        <message name="ImageListRequired">
          image list is required
        </message>
        <message name="ImageSizeDiffers">
          image size differs
        </message>
        <message name="LeftAndRightImageSizesDiffer">
          left and right image sizes differ
        </message>
        <message name="NegativeOrZeroImageSize">
          negative or zero image size
        </message>
        <message name="NoImagesWereFound">
          no images were found
        </message>
        <message name="NoImagesWereLoaded">
          no images were loaded
        </message>
        <message name="TooManyClusters">
          too many cluster
        </message>
        <message name="UnableToCreateColorTransform">
          unable to create color transform
        </message>
        <message name="WidthOrHeightExceedsLimit">
          width or height exceeds limit
        </message>
      </error>
      <warning>
        <message name="AssociateProfile">
          associate profile with image, a source and destination color profile required for transform
        </message>
        <message name="ImagesTooDissimilar">
          images too dissimilar
        </message>
        <message name="UnableToTransformColorspace">
          unable to transform colorspace
        </message>
      </warning>
    </image>
    <filter>
      <error>
        <message name="FilterFailed">
          filter failed
        </message>
      </error>
    </filter>
    <missing>
      <delegate>
        <error>
          <message name="DelegateLibrarySupportNotBuiltIn">
            delegate library support not built-in
          </message>
          <message name="NoDecodeDelegateForThisImageFormat">
            no decode delegate for this image format
          </message>
          <message name="NoEncodeDelegateForThisImageFormat">
            no encode delegate for this image format
          </message>
        </error>
        <warning>
          <message name="DelegateLibrarySupportNotBuiltIn">
            delegate library support not built-in
          </message>
          <message name="FreeTypeLibraryIsNotAvailable">
            FreeType library is not available
          </message>
          <message name="LCMSLibraryIsNotAvailable">
            LCMS color profile library is not available
          </message>
          <message name="NoEncodeDelegateForThisImageFormat">
            no encode delegate for this image format
          </message>
        </warning>
      </delegate>
    </missing>
    <module>
      <error>
        <message name="ImageCoderSignatureMismatch">
          image coder signature mismatch
        </message>
        <message name="ImageFilterSignatureMismatch">
          image filter signature mismatch
        </message>
        <message name="UnableToLoadModule">
          unable to load module
        </message>
        <message name="UnableToRegisterImageFormat">
          unable to register image format
        </message>
      </error>
      <fatalerror>
        <message name="UnableToInitializeModuleLoader">
          unable to initialize module loader
        </message>
      </fatalerror>
      <warning>
        <message name="UnableToCloseModule">
          unable to close module
        </message>
      </warning>
    </module>
    <option>
      <error>
        <message name="ClutImageRequired">
          color lookup table image required
        </message>
        <message name="CompositeImageRequired">
          composite image required
        </message>
        <message name="CurlyBracesNestedTooDeeply">
          curly braces nested too deeply
        </message>
        <message name="DeprecatedOptionNoCode">
          option deprecated, unable to execute
        </message>
        <message name="DivideByZero">
          divide by zero
        </message>
        <message name="FrameIsLessThanImageSize">
          frame is less than image size
        </message>
        <message name="GeometryDimensionsAreZero">
          geometry dimensions are zero
        </message>
        <message name="GeometryDoesNotContainImage">
          geometry does not contain image
        </message>
        <message name="ImageSequenceRequired">
          image sequence is required
        </message>
        <message name="InterpretPropertyFailure">
          failure to interpret image property escapes
        </message>
        <message name="InvalidArgument">
          invalid argument for option
        </message>
        <message name="InvalidUseOfOption">
          invalid use of option
        </message>
        <message name="InvalidGeometry">
          invalid geometry
        </message>
        <message name="InvalidImageIndex">
          invalid image index
        </message>
        <message name="InvalidNumberList">
          invalid list of numbers
        </message>
        <message name="InvalidSetting">
          invalid setting
        </message>
        <message name="ImagesAreNotTheSameSize">
          images are not the same size
        </message>
        <message name="ImagePagesAreNotCoalesced">
          image pages are not coalesced
        </message>
        <message name="ImageSizeMustExceedBevelWidth">
          size must exceed bevel width
        </message>
        <message name="ImageWidthsOrHeightsDiffer">
          image widths or heights differ
        </message>
        <message name="KernelWidthMustBeAnOddNumber">
          kernel width must be an odd number
        </message>
        <message name="LabelExpected">
          label expected
        </message>
        <message name="MapImageRequired">
          map image required
        </message>
        <message name="MissingArgument">
          missing required argument
        </message>
        <message name="MissingAnImageFilename">
          missing an image filename
        </message>
        <message name="MissingExpression">
          missing expression
        </message>
        <message name="MissingNullSeparator">
          missing Null Image List Separator
        </message>
        <message name="MissingOutputFilename">
          missing output filename
        </message>
        <message name="MustSpecifyAnImageName">
          must specify an image name
        </message>
        <message name="MustSpecifyImageDepth">
          must specify image depth
        </message>
        <message name="MustSpecifyImageSize">
          must specify image size
        </message>
        <message name="NoBlobDefined">
          no Binary Large OBjects defined
        </message>
        <message name="NoClipPathDefined">
          no clip path defined
        </message>
        <message name="NoImagesForWrite">
          no images for write
        </message>
        <message name="NoImagesFound">
          no images found for operation
        </message>
        <message name="NoImagesDefined">
          no images defined
        </message>
        <message name="NoImageVectorGraphics">
          no image vector graphics
        </message>
        <message name="NoSuchImage">
          no such image
        </message>
        <message name="NoSuchImageChannel">
          no such image channel
        </message>
        <message name="NoSuchOption">
          no such option
        </message>
        <message name="NonZeroWidthAndHeightRequired">
          non-zero width and height required
        </message>
        <message name="NotEnoughParameters">
          not enough parameters
        </message>
        <message name="ParenthesisNestedTooDeeply">
          parenthesis nested too deeply
        </message>
        <message name="ReferenceImageRequired">
          reference image required
        </message>
        <message name="ReferenceIsNotMyType">
          reference is not my type
        </message>
        <message name="SetReadOnlyProperty">
          attempt to set read-only property
        </message>
        <message name="SteganoImageRequired">
          stegano image required
        </message>
        <message name="StereoImageRequired">
          stereo image required
        </message>
        <message name="SubimageSpecificationReturnsNoImages">
          subimage specification returns no images
        </message>
        <message name="TwoOrMoreImagesRequired">
          two or more images required
        </message>
        <message name="UnableToAccessPath">
          unable to access file path
        </message>
        <message name="UnableToOpenFile">
          unable to open file
        </message>
        <message name="UnableToParseExpression">
          unable to parse expression
        </message>
        <message name="UnableToParseKernel">
          unable to parse kernel string
        </message>
        <message name="UnbalancedBraces">
          unbalanced braces
        </message>
        <message name="UnbalancedParenthesis">
          unbalanced parenthesis
        </message>
        <message name="UndefinedVariable">
          undefined variable
        </message>
        <message name="UnrecognizedAttribute">
          unrecognized attribute
        </message>
        <message name="UnrecognizedChannelType">
          unrecognized channel type
        </message>
        <message name="UnrecognizedColor">
          unrecognized color
        </message>
        <message name="UnrecognizedColorspace">
          unrecognized colorspace
        </message>
        <message name="UnrecognizedComposeOperator">
          unrecognized compose operator
        </message>
        <message name="UnrecognizedCompressType">
          unrecognized compress type
        </message>
        <message name="UnrecognizedDirectionType">
          unrecognized direction type
        </message>
        <message name="UnrecognizedDisposeMethod">
          unrecognized dispose method
        </message>
        <message name="UnrecognizedDistortMethod">
          unrecognized distortion method
        </message>
        <message name="UnrecognizedDitherMethod">
          unrecognized dither method
        </message>
        <message name="UnrecognizedEndianType">
          unrecognized endian type
        </message>
        <message name="UnrecognizedElement">
          unrecognized element
        </message>
        <message name="UnrecognizedEvaluateOperator">
          unrecognized evaluate operator
        </message>
        <message name="UnrecognizedEventType">
          unrecognized event type
        </message>
        <message name="UnrecognizedFunction">
          unrecognized function
        </message>
        <message name="UnrecognizedGravityType">
          unrecognized gravity type
        </message>
        <message name="UnrecognizedImageCompression">
          unrecognized image compression
        </message>
        <message name="UnrecognizedImageFilter">
          unrecognized image filter
        </message>
        <message name="UnrecognizedImageFormat">
          unrecognized image format
        </message>
        <message name="UnrecognizedImageMode">
          unrecognized image mode
        </message>
        <message name="UnrecognizedImageOrientation">
          unrecognized image orientation
        </message>
        <message name="UnrecognizedImageType">
          unrecognized image type
        </message>
        <message name="UnrecognizedIntentType">
          unrecognized intent type
        </message>
        <message name="UnrecognizedInterlaceType">
          unrecognized interlace type
        </message>
        <message name="UnrecognizedInterpolateMethod">
          unrecognized interpolate method
        </message>
        <message name="UnrecognizedKernelType">
          unrecognized kernel type
        </message>
        <message name="UnrecognizedListType">
          unrecognized list type
        </message>
        <message name="UnrecognizedMetricType">
          unrecognized metric type
        </message>
        <message name="UnrecognizedModeType">
          unrecognized mode type
        </message>
        <message name="UnrecognizedMorphologyMethod">
          unrecognized morphology method
        </message>
        <message name="UnrecognizedOption">
          unrecognized option
        </message>
        <message name="UnrecognizedPerlMagickMethod">
          unrecognized PerlMagick method
        </message>
        <message name="UnrecognizedPixelMap">
          unrecognized pixel map
        </message>
        <message name="UnrecognizedPreviewType">
          unrecognized preview type
        </message>
        <message name="UnrecognizedResourceType">
          unrecognized resource type
        </message>
        <message name="UnrecognizedSparseColorMethod">
          unrecognized sparse color method
        </message>
        <message name="UnrecognizedStorageType">
          unrecognized storage type
        </message>
        <message name="UnrecognizedStretchType">
          unrecognized stretch type
        </message>
        <message name="UnrecognizedStyleType">
          unrecognized style type
        </message>
        <message name="UnrecognizedType">
          unrecognized type
        </message>
        <message name="UnrecognizedUnitsType">
          unrecognized units type
        </message>
        <message name="UnrecognizedValidateType">
          unrecognized validate type
        </message>
        <message name="UnrecognizedVirtualPixelMethod">
          unrecognized virtual pixel method
        </message>
        <message name="XmlInvalidAttribute">
          XML invalid attribute
        </message>
        <message name="XmlInvalidContent">
          XML invalid content
        </message>
        <message name="XmlMissingAttribute">
          XML missing required attribute
        </message>
        <message name="XmlMissingContent">
          XML missing required content
        </message>
        <message name="XmlMissingElement">
          XML missing required element
        </message>
      </error>
      <fatalerror>
        <message name="FilenameTruncated">
          image filename truncated
        </message>
        <message name="MissingAnImageFilename">
          missing an image filename
        </message>
        <message name="ScriptIsBinary">
          script is binary
        </message>
        <message name="ScriptTokenMemoryFailed">
          script token too big
        </message>
        <message name="ScriptUnbalancedQuotes">
          script token with unbalanced quotes
        </message>
        <message name="UnableToOpenScript">
          unable to open script
        </message>
        <message name="UnrecognizedColormapType">
          unrecognized colormap type
        </message>
        <message name="UnrecognizedColorspaceType">
          unrecognized colorspace type
        </message>
        <message name="UnrecognizedEndianType">
          unrecognized endian type
        </message>
        <message name="UnrecognizedImageCompressionType">
          unrecognized compression type
        </message>
        <message name="UnrecognizedImageType">
          unrecognized image type
        </message>
        <message name="UnrecognizedInterlaceType">
          unrecognized interlace type
        </message>
        <message name="UnrecognizedListType">
          unrecognized list type
        </message>
        <message name="UnrecognizedDisposeMethod">
          unrecognized dispose method
        </message>
        <message name="UnrecognizedOption">
          unrecognized option
        </message>
        <message name="UnrecognizedResourceType">
          unrecognized resource type
        </message>
        <message name="UnrecognizedVirtualPixelMethod">
          unrecognized virtual pixel method
        </message>
      </fatalerror>
      <warning>
        <message name="ReplacedOption">
          option has been replaced
        </message>
        <message name="GeometryDoesNotContainImage">
          geometry does not contain image
        </message>
        <message name="InterpretPropertyFailure">
          failure in interpret image property escapes
        </message>
        <message name="InvalidSetting">
          invalid setting
        </message>
        <message name="NoImageForProperty">
          no image to apply a property
        </message>
        <message name="NoImageInfoForProperty">
          no image info (wand) to apply a property
        </message>
        <message name="NoSuchElement">
          no such element in list
        </message>
        <message name="SetReadOnlyProperty">
          attempt to set read-only property
        </message>
        <message name="UnknownImageProperty">
          unknown image property
        </message>
        <message name="UnrecognizedColor">
          unrecognized color
        </message>
        <message name="ZeroTimeAnimation">
          animation only contains zero time delays
        </message>
      </warning>
    </option>
    <policy>
      <error>
        <message name="NotAuthorized">
          attempt to perform an operation not allowed by the security policy
        </message>
      </error>
    </policy>
    <registry>
      <error>
        <message name="UnableToGetRegistryID">
          unable to get registry ID
        </message>
        <message name="UnableToSetRegistry">
          unable to set registry
        </message>
      </error>
    </registry>
    <resource>
      <limit>
        <error>
          <message name="ListLengthExceedsLimit">
            list length exceeds limit
          </message>
          <message name="MemoryAllocationFailed">
            memory allocation failed
          </message>
          <message name="PixelCacheAllocationFailed">
            pixel cache allocation failed
          </message>
          <message name="TimeLimitExceeded">
            time limit exceeded
          </message>
          <message name="TooManyExceptions">
            too many exceptions
          </message>
          <message name="TooManyObjects">
            too many objects
          </message>
          <message name="UnableToAcquireString">
            unable to acquire string
          </message>
          <message name="UnableToAllocateColormap">
            unable to allocate colormap
          </message>
          <message name="UnableToConvertFont">
            unable to convert font
          </message>
          <message name="UnableToCreateColormap">
            unable to create colormap
          </message>
          <message name="UnableToDitherImage">
            unable to dither image
          </message>
          <message name="UnableToClonePackageInfo">
            unable to clone package info
          </message>
          <message name="UnableToGetPackageInfo">
            unable to get package info
          </message>
        </error>
        <fatalerror>
          <message name="UnableToAllocateDashPattern">
            unable to allocate dash pattern
          </message>
          <message name="UnableToAllocateDerivatives">
            unable to allocate derivates
          </message>
          <message name="UnableToAllocateGammaMap">
            unable to allocate gamma map
          </message>
          <message name="UnableToAllocateImage">
            unable to allocate image
          </message>
          <message name="UnableToAllocateImagePixels">
            unable to allocate image pixels
          </message>
          <message name="UnableToDestroySemaphore">
            unable to destroy semaphore
          </message>
          <message name="UnableToInstantiateSemaphore">
            unable to instantiate semaphore
          </message>
          <message name="UnableToAcquireString">
            unable to allocate string
          </message>
          <message name="MemoryAllocationFailed">
            Memory allocation failed
          </message>
          <message name="UnableToConcatenateString">
            unable to concatenate string
          </message>
          <message name="UnableToConvertText">
            unable to convert text
          </message>
          <message name="UnableToCreateColormap">
            unable to create colormap
          </message>
          <message name="UnableToCloneImage">
            unable to clone image
          </message>
          <message name="UnableToDisplayImage">
            unable to display image
          </message>
          <message name="UnableToEscapeString">
            unable to escape string
          </message>
          <message name="UnableToInterpretMSLImage">
            unable to interpret MSL image
          </message>
          <message name="UnableToLockSemaphore">
            unable to lock semaphore
          </message>
          <message name="UnableToUnlockSemaphore">
            unable to unlock semaphore
          </message>
        </fatalerror>
        <warning>
          <message name="MemoryAllocationFailed">
            memory allocation failed
          </message>
          <message name="ProfileSizeExceedsLimit">
            profile size exceeds limit
          </message>
        </warning>
      </limit>
    </resource>
    <type>
      <error>
        <message name="FontSubstitutionRequired">
          font substitution required
        </message>
        <message name="UnableToGetTypeMetrics">
          unable to get type metrics
        </message>
        <message name="UnableToInitializeFreetypeLibrary">
          unable to initialize freetype library
        </message>
        <message name="UnableToReadFont">
          unable to read font
        </message>
        <message name="UnrecognizedFontEncoding">
          unrecognized font encoding
        </message>
      </error>
      <warning>
        <message name="UnableToReadFont">
          unable to read font
        </message>
      </warning>
    </type>
    <stream>
      <error>
        <message name="ImageDoesNotContainTheStreamGeometry">
          image does not contain the stream geometry
        </message>
        <message name="NoStreamHandlerIsDefined">
          no stream handler is defined
        </message>
        <message name="PixelCacheIsNotOpen">
          pixel cache is not open
        </message>
      </error>
    </stream>
    <wand>
      <error>
        <message name="InvalidColormapIndex">
          invalid colormap index
        </message>
        <message name="ZeroRegionSize">
          zero region size
        </message>
        <message name="UnableToOpenFile">
          unable to open file
        </message>
        <message name="QuantumDepthMismatch">
          wand quantum depth does not match that of the core API
        </message>
        <message name="ContainsNoImages">
          wand contains no images
        </message>
        <message name="ContainsNoIterators">
          wand contains no iterators
        </message>
      </error>
    </wand>
    <xserver>
      <error>
        <message name="ColorIsNotKnownToServer">
          color is not known to server
        </message>
        <message name="NoWindowWithSpecifiedIDExists">
          no window with specified ID exists
        </message>
        <message name="StandardColormapIsNotInitialized">
          standard Colormap is not initialized
        </message>
        <message name="UnableToConnectToRemoteDisplay">
          unable to connect to remote display
        </message>
        <message name="UnableToCreateBitmap">
          unable to create bitmap
        </message>
        <message name="UnableToCreateColormap">
          unable to create colormap
        </message>
        <message name="UnableToCreatePixmap">
          unable to create pixmap
        </message>
        <message name="UnableToCreateProperty">
          unable to create property
        </message>
        <message name="UnableToCreateStandardColormap">
          unable to create standard colormap
        </message>
        <message name="UnableToDisplayImageInfo">
          unable to display image info
        </message>
        <message name="UnableToGetProperty">
          unable to get property
        </message>
        <message name="UnableToGetStandardColormap">
          unable to get Standard Colormap
        </message>
        <message name="UnableToGetVisual">
          unable to get visual
        </message>
        <message name="UnableToGrabMouse">
          unable to grab mouse
        </message>
        <message name="UnableToLoadFont">
          unable to load font
        </message>
        <message name="UnableToMatchVisualToStandardColormap">
          unable to match visual to Standard Colormap
        </message>
        <message name="UnableToOpenXServer">
          unable to open X server
        </message>
        <message name="UnableToReadXWindowAttributes">
          unable to read X window attributes
        </message>
        <message name="UnableToReadXWindowImage">
          unable to read X window image
        </message>
        <message name="UnrecognizedColormapType">
          unrecognized colormap type
        </message>
        <message name="UnrecognizedGravityType">
          unrecognized gravity type
        </message>
        <message name="UnrecognizedVisualSpecifier">
          unrecognized visual specifier
        </message>
      </error>
      <fatalerror>
        <message name="UnableToCreateCursor">
          unable to create X cursor
        </message>
        <message name="UnableToCreateGraphicContext">
          unable to create graphic context
        </message>
        <message name="UnableToCreateStandardColormap">
          unable to create standard colormap
        </message>
        <message name="UnableToCreateTextProperty">
          unable to create text property
        </message>
        <message name="UnableToCreateXWindow">
          unable to create X window
        </message>
        <message name="UnableToCreateXImage">
          unable to create X image
        </message>
        <message name="UnableToCreateXPixmap">
          unable to create X pixmap
        </message>
        <message name="UnableToDisplayImage">
          unable to display image
        </message>
        <message name="UnableToGetVisual">
          unable to get visual
        </message>
        <message name="UnableToGetPixelInfo">
          unable to get pixel info
        </message>
        <message name="UnableToLoadFont">
          unable to load font
        </message>
        <message name="UnableToMakeXWindow">
          unable to make X window
        </message>
        <message name="UnableToOpenXServer">
          unable to open X server
        </message>
        <message name="UnableToViewFonts">
          unable to view fonts
        </message>
      </fatalerror>
      <warning>
        <message name="UsingDefaultVisual">
          using default visual
        </message>
        <message name="UnableToGetVisual">
          unable to get visual
        </message>
      </warning>
    </xserver>
  </exception>
  <monitor>
    <AddNoise>
      <message name="Image">
        add noise to image
      </message>
    </AddNoise>
    <Append>
      <message name="Image">
        append image sequence
      </message>
    </Append>
    <assign>
      <message name="Image">
        assign image colors
      </message>
    </assign>
    <Average>
      <message name="Image">
        average image sequence
      </message>
    </Average>
    <Blur>
      <message name="Image">
        blur image
      </message>
    </Blur>
    <Chop>
      <message name="Image">
        chop image
      </message>
    </Chop>
    <Classify>
      <message name="Image">
        classify image colors
      </message>
    </Classify>
    <ColorReplace>
      <message name="Image">
        replace color in image
      </message>
    </ColorReplace>
    <Colorize>
      <message name="Image">
        colorize image
      </message>
    </Colorize>
    <Combine>
      <message name="Image">
        combine image
      </message>
    </Combine>
    <ContrastStretch>
      <message name="Image">
         contrast-stretch image
      </message>
    </ContrastStretch>
    <Convolve>
      <message name="Image">
        convolve image
      </message>
    </Convolve>
    <Crop>
      <message name="Image">
        crop image
      </message>
    </Crop>
    <Decode>
      <message name="Image">
        decode image
      </message>
    </Decode>
    <Despeckle>
      <message name="Image">
        despeckle image
      </message>
    </Despeckle>
    <Distort>
      <message name="Image">
        distort image
      </message>
    </Distort>
    <Dither>
      <message name="Image">
        dither image colors
      </message>
    </Dither>
    <DullContrast>
      <message name="Image">
        dull image contrast
      </message>
    </DullContrast>
    <Encode>
      <message name="Image">
        encode image
      </message>
    </Encode>
    <Equalize>
      <message name="Image">
        equalize image
      </message>
    </Equalize>
    <Flip>
      <message name="Image">
        flip image
      </message>
    </Flip>
    <Flop>
      <message name="Image">
        flop image
      </message>
    </Flop>
    <Frame>
      <message name="Image">
        add frame to image
      </message>
    </Frame>
    <Fx>
      <message name="Image">
        fx image
      </message>
    </Fx>
    <GammaCorrect>
      <message name="Image">
        gamma correct image
      </message>
    </GammaCorrect>
    <Histogram>
      <message name="Image">
        compute image histogram
      </message>
    </Histogram>
    <Implode>
      <message name="Image">
        implode image
      </message>
    </Implode>
    <Level>
      <message name="Image">
        level image
      </message>
    </Level>
    <Load>
      <message name="Image">
        load image
      </message>
      <message name="Images">
        load images
      </message>
    </Load>
    <Magnify>
      <message name="Image">
        magnify image
      </message>
    </Magnify>
    <MedianFilter>
      <message name="Image">
        filter image with neighborhood ranking
      </message>
    </MedianFilter>
    <Minify>
      <message name="Image">
        minify image
      </message>
    </Minify>
    <Modulate>
      <message name="Image">
        modulate image
      </message>
    </Modulate>
    <Mogrify>
      <message name="Image">
        mogrify image
      </message>
    </Mogrify>
    <Montage>
      <message name="Image">
        montage image
      </message>
    </Montage>
    <Morph>
      <message name="Image">
        morph image sequence
      </message>
    </Morph>
    <Mosaic>
      <message name="Image">
        mosaic image
      </message>
    </Mosaic>
    <Negate>
      <message name="Image">
        negate image
      </message>
    </Negate>
    <OilPaint>
      <message name="Image">
        oil paint image
      </message>
    </OilPaint>
    <Opaque>
      <message name="Image">
        set opaque color in image
      </message>
    </Opaque>
    <Plasma>
      <message name="Image">
        plasma image
      </message>
    </Plasma>
    <Preview>
      <message name="Image">
        preview image
      </message>
    </Preview>
    <Raise>
      <message name="Image">
        raise image
      </message>
    </Raise>
    <Recolor>
      <message name="Image">
        recolor color image
      </message>
    </Recolor>
    <Reduce>
      <message name="Image">
        reduce image colors
      </message>
    </Reduce>
    <ReduceNoise>
      <message name="Image">
        reduce the image noise
      </message>
    </ReduceNoise>
    <Render>
      <message name="Image">
        render image
      </message>
    </Render>
    <Resize>
      <message name="Image">
        resize image
      </message>
    </Resize>
    <RGBTransform>
      <message name="Image">
        RGB transform image
      </message>
    </RGBTransform>
    <Roll>
      <message name="Image">
        roll image
      </message>
    </Roll>
    <Rotate>
      <message name="Image">
        rotate image
      </message>
    </Rotate>
    <Sample>
      <message name="Image">
        sample image
      </message>
    </Sample>
    <Save>
      <message name="Image">
        save image
      </message>
      <message name="Images">
        save images
      </message>
    </Save>
    <Scale>
      <message name="Image">
        scale image
      </message>
    </Scale>
    <Segment>
      <message name="Image">
        segment image
      </message>
    </Segment>
    <Separate>
      <message name="Image">
        extract a channel from image
      </message>
    </Separate>
    <SepiaTone>
      <message name="Image">
        sepia-tone image
      </message>
    </SepiaTone>
    <Shade>
      <message name="Image">
        shade image
      </message>
    </Shade>
    <Sharpen>
      <message name="Image">
        sharpen image
      </message>
    </Sharpen>
    <SharpenContrast>
      <message name="Image">
        sharpen image contrast
      </message>
    </SharpenContrast>
    <SigmoidalContrast>
      <message name="Image">
        sigmoidal contrast image
      </message>
    </SigmoidalContrast>
    <Solarize>
      <message name="Image">
        solarize image
      </message>
    </Solarize>
    <Splice>
      <message name="Image">
        splice image
      </message>
    </Splice>
    <Spread>
      <message name="Image">
        spread image
      </message>
    </Spread>
    <Stegano>
      <message name="Image">
        stegano image
      </message>
    </Stegano>
    <Stereo>
      <message name="Image">
        stereo image
      </message>
    </Stereo>
    <Swirl>
      <message name="Image">
        swirl image
      </message>
    </Swirl>
    <Texture>
      <message name="Image">
        texture image
      </message>
    </Texture>
    <Threshold>
      <message name="Image">
        threshold image
      </message>
    </Threshold>
    <Tile>
      <message name="Image">
        tile image
      </message>
    </Tile>
    <Tint>
      <message name="Image">
        tint image
      </message>
    </Tint>
    <TransformRGB>
      <message name="Image">
        transform RGB image
      </message>
    </TransformRGB>
    <Transparent>
      <message name="Image">
        set transparent color in image
      </message>
    </Transparent>
    <Wave>
      <message name="Image">
        wave image
      </message>
    </Wave>
    <Write>
      <message name="Image">
        write image
      </message>
    </Write>
    <XShear>
      <message name="Image">
        x shear image
      </message>
    </XShear>
    <YShear>
      <message name="Image">
        y shear image
      </message>
    </YShear>
  </monitor>
</locale>
