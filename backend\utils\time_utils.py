#!/usr/bin/env python3
"""
时间处理工具类 - 统一处理中国时间
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import Union, Optional

# 中国时区 (UTC+8)
CHINA_TZ = timezone(timedelta(hours=8))

class TimeUtils:
    """时间处理工具类"""
    
    @staticmethod
    def get_china_timezone():
        """获取中国时区"""
        return CHINA_TZ
    
    @staticmethod
    def now_china():
        """获取当前中国时间"""
        return datetime.now(CHINA_TZ)
    
    @staticmethod
    def utc_to_china(utc_dt: datetime) -> datetime:
        """将UTC时间转换为中国时间"""
        if utc_dt.tzinfo is None:
            # 如果没有时区信息，假设是UTC时间
            utc_dt = utc_dt.replace(tzinfo=timezone.utc)
        return utc_dt.astimezone(CHINA_TZ)
    
    @staticmethod
    def china_to_utc(china_dt: datetime) -> datetime:
        """将中国时间转换为UTC时间"""
        if china_dt.tzinfo is None:
            # 如果没有时区信息，假设是中国时间
            china_dt = china_dt.replace(tzinfo=CHINA_TZ)
        return china_dt.astimezone(timezone.utc)
    
    @staticmethod
    def parse_to_china_time(time_input: Union[str, datetime, None]) -> Optional[datetime]:
        """解析各种时间格式并转换为中国时间"""
        if not time_input:
            return None
            
        try:
            if isinstance(time_input, str):
                # 处理不同的时间字符串格式
                if 'T' in time_input:
                    # ISO格式: 2025-07-02T14:49:46 或 2025-07-02T14:49:46.123456
                    if '.' in time_input:
                        # 去掉微秒部分
                        time_input = time_input.split('.')[0]
                    if time_input.endswith('Z'):
                        # UTC时间标记
                        time_input = time_input[:-1]
                        dt = datetime.fromisoformat(time_input)
                        dt = dt.replace(tzinfo=timezone.utc)
                        return dt.astimezone(CHINA_TZ)
                    else:
                        dt = datetime.fromisoformat(time_input)
                        if dt.tzinfo is None:
                            # 假设是UTC时间
                            dt = dt.replace(tzinfo=timezone.utc)
                            return dt.astimezone(CHINA_TZ)
                        else:
                            return dt.astimezone(CHINA_TZ)
                else:
                    # 标准格式: 2025-07-02 14:49:46
                    dt = datetime.strptime(time_input[:19], '%Y-%m-%d %H:%M:%S')
                    # 假设是UTC时间，转换为中国时间
                    dt = dt.replace(tzinfo=timezone.utc)
                    return dt.astimezone(CHINA_TZ)
            elif isinstance(time_input, datetime):
                if time_input.tzinfo is None:
                    # 假设是UTC时间
                    time_input = time_input.replace(tzinfo=timezone.utc)
                return time_input.astimezone(CHINA_TZ)
            else:
                return None
        except Exception as e:
            print(f"时间解析失败: {e}, 输入: {time_input}")
            return None
    
    @staticmethod
    def format_china_time(time_input: Union[str, datetime, None], 
                         format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化为中国时间字符串"""
        if not time_input:
            return ""
            
        china_time = TimeUtils.parse_to_china_time(time_input)
        if china_time:
            return china_time.strftime(format_str)
        else:
            return str(time_input) if time_input else ""
    
    @staticmethod
    def format_china_datetime_display(time_input: Union[str, datetime, None]) -> str:
        """格式化为显示用的中国时间字符串"""
        return TimeUtils.format_china_time(time_input, '%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def format_china_date_display(time_input: Union[str, datetime, None]) -> str:
        """格式化为显示用的中国日期字符串"""
        return TimeUtils.format_china_time(time_input, '%Y-%m-%d')
    
    @staticmethod
    def format_china_time_display(time_input: Union[str, datetime, None]) -> str:
        """格式化为显示用的中国时间字符串（仅时间）"""
        return TimeUtils.format_china_time(time_input, '%H:%M:%S')
    
    @staticmethod
    def get_relative_time_china(time_input: Union[str, datetime, None]) -> str:
        """获取相对于当前中国时间的相对时间描述"""
        if not time_input:
            return "未知时间"
            
        target_time = TimeUtils.parse_to_china_time(time_input)
        if not target_time:
            return "无效时间"
            
        now = TimeUtils.now_china()
        diff = now - target_time
        
        if diff.total_seconds() < 60:
            return "刚刚"
        elif diff.total_seconds() < 3600:
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes}分钟前"
        elif diff.total_seconds() < 86400:
            hours = int(diff.total_seconds() / 3600)
            return f"{hours}小时前"
        elif diff.days < 7:
            return f"{diff.days}天前"
        else:
            return TimeUtils.format_china_datetime_display(target_time)
    
    @staticmethod
    def get_remaining_time_china(end_time: Union[str, datetime, None]) -> str:
        """获取到指定时间的剩余时间（中国时间）"""
        if not end_time:
            return "未知"
            
        target_time = TimeUtils.parse_to_china_time(end_time)
        if not target_time:
            return "无效时间"
            
        now = TimeUtils.now_china()
        diff = target_time - now
        
        if diff.total_seconds() <= 0:
            return "已过期"
            
        days = diff.days
        hours = int(diff.seconds / 3600)
        minutes = int((diff.seconds % 3600) / 60)
        
        if days > 0:
            return f"{days}天{hours}小时"
        elif hours > 0:
            return f"{hours}小时{minutes}分钟"
        else:
            return f"{minutes}分钟"

# 设置系统时区为中国时区
def set_system_timezone_china():
    """设置系统时区为中国时区"""
    try:
        # 在Windows上设置时区环境变量
        os.environ['TZ'] = 'Asia/Shanghai'
        if hasattr(sys, 'platform') and sys.platform == 'win32':
            import time
            time.tzset() if hasattr(time, 'tzset') else None
    except Exception as e:
        print(f"设置系统时区失败: {e}")

# 初始化时设置时区
set_system_timezone_china()
