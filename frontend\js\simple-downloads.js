/**
 * 简单下载记录管理
 */
class SimpleDownloads {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalRecords = 0;
        this.totalPages = 0;
        this.filters = {
            type: '',
            dateFrom: '',
            dateTo: '',
            filename: ''
        };
        this.init();
    }

    init() {
        console.log('初始化简单下载记录管理');
        this.bindEvents();
    }

    bindEvents() {
        // 监听下载记录菜单点击
        document.addEventListener('click', (e) => {
            const downloadLink = e.target.closest('[data-view="downloads"]');
            if (downloadLink) {
                e.preventDefault();
                this.showDownloads();
            }
        });
    }

    async show() {
        return this.showDownloads();
    }

    async showDownloads() {
        console.log('=== 调试模式：显示下载记录 ===');

        // 检查登录状态
        const authData = localStorage.getItem('fileShareAuth');
        console.log('认证数据:', authData);
        
        if (!authData) {
            console.error('未找到认证数据');
            alert('请先登录');
            window.location.href = 'login.html';
            return;
        }

        try {
            const parsedAuth = JSON.parse(authData);
            console.log('解析后的认证数据:', parsedAuth);
        } catch (e) {
            console.error('认证数据解析失败:', e);
        }

        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
        const downloadsMenu = document.querySelector('[data-view="downloads"]')?.closest('.menu-item');
        if (downloadsMenu) downloadsMenu.classList.add('active');

        // 隐藏其他视图
        this.hideAllViews();

        // 显示下载记录视图
        const container = document.getElementById('download-records-view');
        if (container) {
            container.classList.remove('hidden');
            container.style.display = 'block';
            this.createInterface(container);
            await this.loadRecords();
        }
    }

    hideAllViews() {
        const views = ['file-grid', 'file-list'];
        views.forEach(viewId => {
            const view = document.getElementById(viewId);
            if (view) {
                view.classList.add('hidden');
                view.style.display = 'none';
            }
        });
    }

    createInterface(container) {
        container.innerHTML = `
            <div class="download-records-container">
                <div class="download-records-header">
                    <h2><i class="fas fa-download"></i> 我的下载记录</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="simpleDownloads.toggleFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button class="btn btn-primary" onclick="simpleDownloads.refreshRecords()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 筛选面板 -->
                <div id="filter-panel" class="filter-panel" style="display: none;">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>下载类型：</label>
                            <select id="filter-type">
                                <option value="">全部类型</option>
                                <option value="single">单文件</option>
                                <option value="batch">批量下载</option>
                                <option value="folder">文件夹</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>时间范围：</label>
                            <input type="date" id="filter-date-from" placeholder="开始日期">
                            <span>至</span>
                            <input type="date" id="filter-date-to" placeholder="结束日期">
                        </div>
                        <div class="filter-group">
                            <label>文件名：</label>
                            <input type="text" id="filter-filename" placeholder="搜索文件名">
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-primary" onclick="simpleDownloads.applyFilters()">
                                <i class="fas fa-search"></i> 应用筛选
                            </button>
                            <button class="btn btn-secondary" onclick="simpleDownloads.clearFilters()">
                                <i class="fas fa-times"></i> 清除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div id="download-stats" class="download-stats">
                    <div class="stat-item">
                        <span class="stat-label">总下载次数：</span>
                        <span class="stat-value" id="total-downloads">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总下载大小：</span>
                        <span class="stat-value" id="total-size">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">最近下载：</span>
                        <span class="stat-value" id="last-download">-</span>
                    </div>
                </div>

                <div id="download-loading" class="loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载...
                </div>

                <div id="download-error" class="error" style="display: none; color: red;">
                </div>

                <div id="download-list">
                </div>

                <!-- 分页控件 -->
                <div id="pagination" class="pagination-container" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text">显示第 1-10 条，共 0 条记录</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-sm" id="prev-page" onclick="simpleDownloads.prevPage()" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span id="page-info">第 1 页，共 1 页</span>
                        <button class="btn btn-sm" id="next-page" onclick="simpleDownloads.nextPage()" disabled>
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async loadRecords() {
        const loadingEl = document.getElementById('download-loading');
        const errorEl = document.getElementById('download-error');
        const listEl = document.getElementById('download-list');

        // 显示加载状态
        if (loadingEl) loadingEl.style.display = 'block';
        if (errorEl) errorEl.style.display = 'none';
        if (listEl) listEl.innerHTML = '';

        try {
            const authData = JSON.parse(localStorage.getItem('fileShareAuth'));
            console.log('=== 调试：准备发送API请求 ===');
            console.log('使用的token:', authData.token);

            // 构建查询参数
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize
            });

            // 添加筛选条件
            if (this.filters.type) {
                params.append('type', this.filters.type);
            }
            if (this.filters.dateFrom) {
                params.append('date_from', this.filters.dateFrom);
            }
            if (this.filters.dateTo) {
                params.append('date_to', this.filters.dateTo);
            }
            if (this.filters.filename) {
                params.append('filename', this.filters.filename);
            }

            const requestUrl = `http://localhost:8086/api/download/records?${params}`;
            console.log('请求URL:', requestUrl);

            const response = await fetch(requestUrl, {
                headers: {
                    'Authorization': `Bearer ${authData.token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('响应状态:', response.status, response.statusText);
            
            const data = await response.json();
            console.log('=== 调试：API响应数据 ===', data);

            // 隐藏加载状态
            if (loadingEl) loadingEl.style.display = 'none';

            if (data.success) {
                const records = data.records || [];

                if (records.length > 0) {
                    this.renderRecords(records);
                    this.updateStats(records);
                } else {
                    if (listEl) listEl.innerHTML = '<div class="empty">暂无下载记录</div>';
                    this.updateStats([]);
                }

                // 更新分页信息
                this.updatePagination(
                    data.total || 0,
                    data.page || this.currentPage,
                    data.limit || this.pageSize
                );
            } else {
                throw new Error(data.error || '获取失败');
            }

        } catch (error) {
            console.error('加载失败:', error);
            if (loadingEl) loadingEl.style.display = 'none';
            if (errorEl) {
                errorEl.style.display = 'block';
                errorEl.textContent = error.message;
            }
        }
    }

    renderRecords(records) {
        const listEl = document.getElementById('download-list');
        if (!listEl) return;

        let html = '';
        records.forEach(record => {
            const size = this.formatSize(record.file_size);
            const time = this.formatTime(record.download_time);
            const type = this.getTypeLabel(record.download_type);
            const encrypted = record.is_encrypted ? '<span class="badge">🔒</span>' : '';

            html += `
                <div class="record-item">
                    <div class="record-info">
                        <div class="record-name">
                            <i class="fas fa-file"></i>
                            ${record.filename}
                            ${encrypted}
                        </div>
                        <div class="record-meta">
                            ${size} | ${type} | ${time}
                        </div>
                    </div>
                </div>
            `;
        });

        listEl.innerHTML = html;
    }

    formatSize(bytes) {
        if (!bytes) return '0 B';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    formatTime(timeString) {
        if (!timeString) return '未知时间';
        try {
            return new Date(timeString).toLocaleString('zh-CN');
        } catch (e) {
            return '未知时间';
        }
    }

    getTypeLabel(type) {
        const types = {
            'single': '单文件',
            'batch': '批量',
            'folder': '文件夹'
        };
        return types[type] || '未知';
    }

    // 筛选相关方法
    toggleFilters() {
        const panel = document.getElementById('filter-panel');
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }

    applyFilters() {
        // 获取筛选条件
        this.filters.type = document.getElementById('filter-type')?.value || '';
        this.filters.dateFrom = document.getElementById('filter-date-from')?.value || '';
        this.filters.dateTo = document.getElementById('filter-date-to')?.value || '';
        this.filters.filename = document.getElementById('filter-filename')?.value || '';

        // 重置到第一页
        this.currentPage = 1;

        // 重新加载数据
        this.loadRecords();
    }

    clearFilters() {
        // 清空筛选条件
        this.filters = {
            type: '',
            dateFrom: '',
            dateTo: '',
            filename: ''
        };

        // 清空表单
        const filterType = document.getElementById('filter-type');
        const filterDateFrom = document.getElementById('filter-date-from');
        const filterDateTo = document.getElementById('filter-date-to');
        const filterFilename = document.getElementById('filter-filename');

        if (filterType) filterType.value = '';
        if (filterDateFrom) filterDateFrom.value = '';
        if (filterDateTo) filterDateTo.value = '';
        if (filterFilename) filterFilename.value = '';

        // 重置到第一页
        this.currentPage = 1;

        // 重新加载数据
        this.loadRecords();
    }

    refreshRecords() {
        this.loadRecords();
    }

    // 分页相关方法
    prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.loadRecords();
        }
    }

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.loadRecords();
        }
    }

    updatePagination(total, page, limit) {
        this.totalRecords = total;
        this.totalPages = Math.ceil(total / limit);

        const paginationContainer = document.getElementById('pagination');
        const paginationInfo = document.getElementById('pagination-info-text');
        const pageInfo = document.getElementById('page-info');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        if (total > 0) {
            const start = (page - 1) * limit + 1;
            const end = Math.min(page * limit, total);

            if (paginationInfo) {
                paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
            }

            if (pageInfo) {
                pageInfo.textContent = `第 ${page} 页，共 ${this.totalPages} 页`;
            }

            if (prevBtn) {
                prevBtn.disabled = page <= 1;
            }

            if (nextBtn) {
                nextBtn.disabled = page >= this.totalPages;
            }

            if (paginationContainer) {
                paginationContainer.style.display = 'block';
            }
        } else {
            if (paginationContainer) {
                paginationContainer.style.display = 'none';
            }
        }
    }

    updateStats(records) {
        const totalDownloads = records.length;
        let totalSize = 0;
        let lastDownload = null;

        records.forEach(record => {
            totalSize += record.file_size || 0;
            if (!lastDownload || new Date(record.download_time) > new Date(lastDownload)) {
                lastDownload = record.download_time;
            }
        });

        const totalDownloadsEl = document.getElementById('total-downloads');
        const totalSizeEl = document.getElementById('total-size');
        const lastDownloadEl = document.getElementById('last-download');

        if (totalDownloadsEl) {
            totalDownloadsEl.textContent = totalDownloads;
        }

        if (totalSizeEl) {
            totalSizeEl.textContent = this.formatSize(totalSize);
        }

        if (lastDownloadEl) {
            lastDownloadEl.textContent = lastDownload ?
                new Date(lastDownload).toLocaleString('zh-CN') : '无';
        }
    }
}

// 初始化
window.simpleDownloads = new SimpleDownloads(); 