#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索索引模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
from datetime import datetime
from typing import Dict, Any

class SearchIndex(Base):
    """搜索索引模型"""
    
    __tablename__ = 'search_index'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('shared_files.id', ondelete='CASCADE'), nullable=False, comment='文件ID')
    index_type = Column(String(20), nullable=False, comment='索引类型: text/image')
    index_data = Column(Text, nullable=False, comment='索引数据(JSON格式)')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 添加检查约束
    __table_args__ = (
        CheckConstraint("index_type IN ('text', 'image')", name='check_index_type'),
    )
    
    # 关联关系
    file = relationship("SharedFile", backref="search_indexes")
    
    def __init__(self, file_id: int, index_type: str, index_data: str):
        self.file_id = file_id
        self.index_type = index_type
        self.index_data = index_data
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'index_type': self.index_type,
            'index_data': self.index_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<SearchIndex(id={self.id}, file_id={self.file_id}, type={self.index_type})>"
