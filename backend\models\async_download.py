"""
异步下载任务模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, BigInteger, JSON
from datetime import datetime
import enum

# 导入共享的Base
from config.database import Base

class DownloadStatus(enum.Enum):
    """下载状态枚举"""
    PENDING = "pending"      # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    EXPIRED = "expired"      # 已过期

class AsyncDownloadTask(Base):
    """异步下载任务"""
    __tablename__ = 'async_download_tasks'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 任务基本信息
    task_id = Column(String(64), unique=True, nullable=False, comment='任务唯一标识')
    user_id = Column(Integer, nullable=True, comment='用户ID')
    
    # 下载目标信息
    download_type = Column(String(20), nullable=False, comment='下载类型: folder, batch, single')
    target_id = Column(Integer, nullable=False, comment='目标ID（文件夹ID或文件ID）')
    target_name = Column(String(255), nullable=False, comment='目标名称')
    
    # 任务状态
    status = Column(String(20), default=DownloadStatus.PENDING.value, comment='任务状态')
    progress = Column(Integer, default=0, comment='进度百分比 0-100')
    
    # 文件信息
    total_files = Column(Integer, default=0, comment='总文件数')
    processed_files = Column(Integer, default=0, comment='已处理文件数')
    total_size = Column(BigInteger, default=0, comment='总文件大小（字节）')
    
    # 结果信息
    zip_path = Column(String(500), nullable=True, comment='生成的ZIP文件路径')
    zip_size = Column(BigInteger, default=0, comment='ZIP文件大小（字节）')
    download_url = Column(String(500), nullable=True, comment='下载URL')
    
    # 加密信息
    is_encrypted = Column(Boolean, default=False, comment='是否加密')
    password = Column(String(50), nullable=True, comment='解压密码')
    
    # 错误信息
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    started_at = Column(DateTime, nullable=True, comment='开始处理时间')
    completed_at = Column(DateTime, nullable=True, comment='完成时间')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    
    # 请求上下文
    request_context = Column(JSON, nullable=True, comment='请求上下文信息')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'user_id': self.user_id,
            'download_type': self.download_type,
            'target_id': self.target_id,
            'target_name': self.target_name,
            'status': self.status,
            'progress': self.progress,
            'total_files': self.total_files,
            'processed_files': self.processed_files,
            'total_size': self.total_size,
            'zip_path': self.zip_path,
            'zip_size': self.zip_size,
            'download_url': self.download_url,
            'is_encrypted': self.is_encrypted,
            'password': self.password,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'request_context': self.request_context
        }
    
    def update_progress(self, processed_files: int, status: str = None):
        """更新进度"""
        self.processed_files = processed_files
        if self.total_files > 0:
            self.progress = min(100, int((processed_files / self.total_files) * 100))
        
        if status:
            self.status = status
            
        if status == DownloadStatus.PROCESSING.value and not self.started_at:
            self.started_at = datetime.now()
        elif status == DownloadStatus.COMPLETED.value:
            self.completed_at = datetime.now()
            self.progress = 100
    
    def mark_failed(self, error_message: str):
        """标记为失败"""
        self.status = DownloadStatus.FAILED.value
        self.error_message = error_message
        if not self.started_at:
            self.started_at = datetime.now()
        self.completed_at = datetime.now()
    
    def mark_completed(self, zip_path: str, zip_size: int, download_url: str):
        """标记为完成"""
        self.status = DownloadStatus.COMPLETED.value
        self.zip_path = zip_path
        self.zip_size = zip_size
        self.download_url = download_url
        self.completed_at = datetime.now()
        self.progress = 100
