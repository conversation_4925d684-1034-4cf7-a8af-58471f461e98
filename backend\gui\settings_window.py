#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from typing import Dict, Any

class SettingsWindow:
    """设置窗口类"""
    
    def __init__(self, parent, server):
        self.parent = parent
        self.server = server
        self.window = None
        self.settings = {}
        
        # 默认设置
        self.default_settings = {
            'server': {
                'host': '0.0.0.0',
                'port': 8080,
                'debug': False,
                'max_connections': 100
            },
            'database': {
                'host': 'localhost',
                'port': 3306,
                'username': 'root',
                'password': '123456',
                'database': 'file_share_system'
            },
            'file': {
                'max_file_size': 100,  # MB
                'allowed_extensions': ['.jpg', '.png', '.pdf', '.doc', '.txt'],
                'thumbnail_size': 200,
                'scan_interval': 300  # 秒
            },
            'security': {
                'session_timeout': 3600,  # 秒
                'max_login_attempts': 5,
                'password_min_length': 6,
                'enable_encryption': True
            },
            'notification': {
                'enable_email': False,
                'smtp_server': '',
                'smtp_port': 587,
                'email_username': '',
                'email_password': ''
            }
        }
        
        self.load_settings()
        self.create_window()
    
    def create_window(self):
        """创建设置窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("系统设置")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 服务器设置标签页
        self.create_server_tab()
        
        # 数据库设置标签页
        self.create_database_tab()
        
        # 文件设置标签页
        self.create_file_tab()
        
        # 安全设置标签页
        self.create_security_tab()

        # 搜索设置标签页
        self.create_search_tab()

        # 通知设置标签页
        self.create_notification_tab()

        # 控制按钮
        self.create_control_buttons(main_frame)
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)


    
    def create_server_tab(self):
        """创建服务器设置标签页"""
        server_frame = ttk.Frame(self.notebook)
        self.notebook.add(server_frame, text="服务器设置")
        
        # 服务器配置框架
        config_frame = ttk.LabelFrame(server_frame, text="服务器配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 主机地址
        ttk.Label(config_frame, text="主机地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.host_var = tk.StringVar(value=self.settings.get('server', {}).get('host', '0.0.0.0'))
        ttk.Entry(config_frame, textvariable=self.host_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # API端口
        ttk.Label(config_frame, text="API端口:").grid(row=1, column=0, sticky=tk.W, pady=5)
        port_frame = ttk.Frame(config_frame)
        port_frame.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        self.port_var = tk.StringVar(value=str(self.settings.get('server', {}).get('port', 8081)))
        ttk.Entry(port_frame, textvariable=self.port_var, width=20).pack(side=tk.LEFT)
        ttk.Button(port_frame, text="检查端口", command=self.check_port).pack(side=tk.LEFT, padx=(5, 0))

        # 前端端口
        ttk.Label(config_frame, text="前端端口:").grid(row=2, column=0, sticky=tk.W, pady=5)
        frontend_port_frame = ttk.Frame(config_frame)
        frontend_port_frame.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        self.frontend_port_var = tk.StringVar(value=str(self.settings.get('server', {}).get('frontend_port', 8082)))
        ttk.Entry(frontend_port_frame, textvariable=self.frontend_port_var, width=20).pack(side=tk.LEFT)
        ttk.Button(frontend_port_frame, text="检查端口", command=self.check_frontend_port).pack(side=tk.LEFT, padx=(5, 0))

        # 最大连接数
        ttk.Label(config_frame, text="最大连接数:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.max_conn_var = tk.StringVar(value=str(self.settings.get('server', {}).get('max_connections', 100)))
        ttk.Entry(config_frame, textvariable=self.max_conn_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 调试模式
        self.debug_var = tk.BooleanVar(value=self.settings.get('server', {}).get('debug', False))
        ttk.Checkbutton(config_frame, text="启用调试模式", variable=self.debug_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)
    
    def create_database_tab(self):
        """创建数据库设置标签页"""
        db_frame = ttk.Frame(self.notebook)
        self.notebook.add(db_frame, text="数据库设置")
        
        # 数据库配置框架
        config_frame = ttk.LabelFrame(db_frame, text="SQLite数据库配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 数据库路径
        ttk.Label(config_frame, text="数据库文件路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.db_path_var = tk.StringVar(value=self.settings.get('database', {}).get('path', 'backend/data/file_share_system.db'))
        ttk.Entry(config_frame, textvariable=self.db_path_var, width=50).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 浏览按钮
        ttk.Button(config_frame, text="浏览...", command=self.browse_database_path).grid(row=0, column=2, padx=(5, 0), pady=5)
        
        # 数据库信息显示
        info_frame = ttk.LabelFrame(db_frame, text="数据库信息", padding=15)
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.db_info_text = tk.Text(info_frame, height=8, width=60, state=tk.DISABLED)
        self.db_info_text.pack(fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(db_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 测试连接按钮
        ttk.Button(button_frame, text="检查连接", command=self.test_database_connection).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新信息", command=self.refresh_database_info).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="备份数据库", command=self.backup_database).pack(side=tk.LEFT, padx=(0, 5))
        
        # 危险操作框架
        danger_frame = ttk.LabelFrame(db_frame, text="⚠️ 危险操作", padding=15)
        danger_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(danger_frame, text="重置数据库将删除所有数据，包括用户、文件记录等", foreground="red").pack(pady=5)
        ttk.Button(danger_frame, text="🗑️ 重置数据库", command=self.reset_database, 
                  style="Danger.TButton").pack(pady=5)
    
    def create_file_tab(self):
        """创建文件设置标签页"""
        file_frame = ttk.Frame(self.notebook)
        self.notebook.add(file_frame, text="文件设置")
        
        # 文件配置框架
        config_frame = ttk.LabelFrame(file_frame, text="文件配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 最大文件大小
        ttk.Label(config_frame, text="最大文件大小 (MB):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.max_size_var = tk.StringVar(value=str(self.settings.get('file', {}).get('max_file_size', 100)))
        ttk.Entry(config_frame, textvariable=self.max_size_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 缩略图大小
        ttk.Label(config_frame, text="缩略图大小 (像素):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.thumb_size_var = tk.StringVar(value=str(self.settings.get('file', {}).get('thumbnail_size', 200)))
        ttk.Entry(config_frame, textvariable=self.thumb_size_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 扫描间隔
        ttk.Label(config_frame, text="文件扫描间隔 (秒):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.scan_interval_var = tk.StringVar(value=str(self.settings.get('file', {}).get('scan_interval', 300)))
        ttk.Entry(config_frame, textvariable=self.scan_interval_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 允许的文件扩展名
        ttk.Label(config_frame, text="允许的文件扩展名:").grid(row=3, column=0, sticky=tk.W, pady=5)
        extensions = ', '.join(self.settings.get('file', {}).get('allowed_extensions', ['.jpg', '.png', '.pdf']))
        self.extensions_var = tk.StringVar(value=extensions)
        ttk.Entry(config_frame, textvariable=self.extensions_var, width=50).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
    
    def create_security_tab(self):
        """创建安全设置标签页"""
        security_frame = ttk.Frame(self.notebook)
        self.notebook.add(security_frame, text="安全设置")
        
        # 安全配置框架
        config_frame = ttk.LabelFrame(security_frame, text="安全配置", padding=15)
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 会话超时
        ttk.Label(config_frame, text="会话超时 (秒):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.session_timeout_var = tk.StringVar(value=str(self.settings.get('security', {}).get('session_timeout', 3600)))
        ttk.Entry(config_frame, textvariable=self.session_timeout_var, width=30).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 最大登录尝试次数
        ttk.Label(config_frame, text="最大登录尝试次数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.max_attempts_var = tk.StringVar(value=str(self.settings.get('security', {}).get('max_login_attempts', 5)))
        ttk.Entry(config_frame, textvariable=self.max_attempts_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 密码最小长度
        ttk.Label(config_frame, text="密码最小长度:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.min_pass_len_var = tk.StringVar(value=str(self.settings.get('security', {}).get('password_min_length', 6)))
        ttk.Entry(config_frame, textvariable=self.min_pass_len_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 启用加密
        self.enable_encryption_var = tk.BooleanVar(value=self.settings.get('security', {}).get('enable_encryption', True))
        ttk.Checkbutton(config_frame, text="启用文件加密", variable=self.enable_encryption_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

    def create_search_tab(self):
        """创建搜索设置标签页"""
        search_frame = ttk.Frame(self.notebook)
        self.notebook.add(search_frame, text="搜索设置")

        # 搜索功能配置框架
        search_config_frame = ttk.LabelFrame(search_frame, text="搜索功能配置", padding=15)
        search_config_frame.pack(fill=tk.X, padx=10, pady=10)

        # 文本搜索开关
        self.enable_text_search_var = tk.BooleanVar(
            value=self.server.settings.get('search.enable_text_search', True) if hasattr(self.server, 'settings') else True
        )
        ttk.Checkbutton(search_config_frame, text="启用文本搜索",
                       variable=self.enable_text_search_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 图像搜索开关
        self.enable_image_search_var = tk.BooleanVar(
            value=self.server.settings.get('search.enable_image_search', True) if hasattr(self.server, 'settings') else True
        )
        ttk.Checkbutton(search_config_frame, text="启用图像搜索",
                       variable=self.enable_image_search_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 以图搜图功能开关
        self.enable_image_upload_search_var = tk.BooleanVar(
            value=self.server.settings.get('search.enable_image_upload_search', True) if hasattr(self.server, 'settings') else True
        )
        ttk.Checkbutton(search_config_frame, text="启用以图搜图功能",
                       variable=self.enable_image_upload_search_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        # 搜索参数配置框架
        search_params_frame = ttk.LabelFrame(search_frame, text="搜索参数配置", padding=15)
        search_params_frame.pack(fill=tk.X, padx=10, pady=10)

        # 最大搜索结果数
        ttk.Label(search_params_frame, text="最大搜索结果数:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.max_search_results_var = tk.StringVar(
            value=str(self.server.settings.get('search.max_search_results', 1000) if hasattr(self.server, 'settings') else 1000)
        )
        ttk.Entry(search_params_frame, textvariable=self.max_search_results_var, width=20).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 图像相似度阈值
        ttk.Label(search_params_frame, text="图像相似度阈值 (0.0-1.0):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.image_search_threshold_var = tk.StringVar(
            value=str(self.server.settings.get('search.image_search_threshold', 0.7) if hasattr(self.server, 'settings') else 0.7)
        )
        ttk.Entry(search_params_frame, textvariable=self.image_search_threshold_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 最大上传图片大小
        ttk.Label(search_params_frame, text="最大上传图片大小 (MB):").grid(row=2, column=0, sticky=tk.W, pady=5)
        max_upload_size_mb = (self.server.settings.get('search.max_upload_image_size', 10485760) if hasattr(self.server, 'settings') else 10485760) // (1024 * 1024)
        self.max_upload_image_size_var = tk.StringVar(value=str(max_upload_size_mb))
        ttk.Entry(search_params_frame, textvariable=self.max_upload_image_size_var, width=20).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 搜索索引配置框架
        search_index_frame = ttk.LabelFrame(search_frame, text="搜索索引配置", padding=15)
        search_index_frame.pack(fill=tk.X, padx=10, pady=10)

        # 索引路径
        ttk.Label(search_index_frame, text="搜索索引路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.search_index_path_var = tk.StringVar(
            value=self.server.settings.get('search.index_path', './data/search_index') if hasattr(self.server, 'settings') else './data/search_index'
        )
        ttk.Entry(search_index_frame, textvariable=self.search_index_path_var, width=50).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        # 索引操作按钮
        index_button_frame = ttk.Frame(search_index_frame)
        index_button_frame.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=10)

        ttk.Button(index_button_frame, text="重建搜索索引", command=self.rebuild_search_index).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(index_button_frame, text="清空搜索索引", command=self.clear_search_index).pack(side=tk.LEFT, padx=(0, 5))

    def create_notification_tab(self):
        """创建通知设置标签页"""
        notification_frame = ttk.Frame(self.notebook)
        self.notebook.add(notification_frame, text="通知设置")
        
        # 邮件通知配置框架
        email_frame = ttk.LabelFrame(notification_frame, text="邮件通知配置", padding=15)
        email_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 启用邮件通知
        self.enable_email_var = tk.BooleanVar(value=self.settings.get('notification', {}).get('enable_email', False))
        ttk.Checkbutton(email_frame, text="启用邮件通知", variable=self.enable_email_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # SMTP服务器
        ttk.Label(email_frame, text="SMTP服务器:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.smtp_server_var = tk.StringVar(value=self.settings.get('notification', {}).get('smtp_server', ''))
        ttk.Entry(email_frame, textvariable=self.smtp_server_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # SMTP端口
        ttk.Label(email_frame, text="SMTP端口:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.smtp_port_var = tk.StringVar(value=str(self.settings.get('notification', {}).get('smtp_port', 587)))
        ttk.Entry(email_frame, textvariable=self.smtp_port_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 邮箱用户名
        ttk.Label(email_frame, text="邮箱用户名:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_user_var = tk.StringVar(value=self.settings.get('notification', {}).get('email_username', ''))
        ttk.Entry(email_frame, textvariable=self.email_user_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # 邮箱密码
        ttk.Label(email_frame, text="邮箱密码:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.email_pass_var = tk.StringVar(value=self.settings.get('notification', {}).get('email_password', ''))
        ttk.Entry(email_frame, textvariable=self.email_pass_var, width=30, show="*").grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置默认", command=self.reset_defaults).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导入设置", command=self.import_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出设置", command=self.export_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=self.on_closing).pack(side=tk.RIGHT)
    
    def load_settings(self):
        """加载设置"""
        try:
            # 从服务器的设置系统加载
            if hasattr(self.server, 'settings'):
                self.settings = {
                    'server': {
                        'host': self.server.settings.get('server.host', '0.0.0.0'),
                        'port': self.server.settings.get('server.port', 8081),
                        'frontend_port': self.server.settings.get('server.frontend_port', 8082),
                        'debug': self.server.settings.get('server.debug', False),
                        'max_connections': self.server.settings.get('server.max_workers', 10)
                    },
                    'database': {
                        'host': self.server.settings.get('database.host', 'localhost'),
                        'port': self.server.settings.get('database.port', 3306),
                        'username': self.server.settings.get('database.username', 'root'),
                        'password': self.server.settings.get('database.password', '123456'),
                        'database': self.server.settings.get('database.database', 'file_share_system')
                    },
                    'file': {
                        'max_file_size': int(self.server.settings.get('file_share.max_file_size', 1024*1024*1024) / (1024*1024)),
                        'allowed_extensions': self.server.settings.get('file_share.allowed_extensions', ['.jpg', '.png', '.pdf']),
                        'thumbnail_size': 200,
                        'scan_interval': 300
                    },
                    'security': {
                        'session_timeout': self.server.settings.get('security.session_timeout', 3600),
                        'max_login_attempts': self.server.settings.get('security.max_login_attempts', 5),
                        'password_min_length': 6,
                        'enable_encryption': True
                    },
                    'notification': {
                        'enable_email': False,
                        'smtp_server': '',
                        'smtp_port': 587,
                        'email_username': '',
                        'email_password': ''
                    }
                }
            else:
                self.settings = self.default_settings.copy()
        except Exception as e:
            print(f"加载设置失败: {e}")
            self.settings = self.default_settings.copy()
    
    def save_settings(self):
        """保存设置"""
        try:
            # 检查端口号是否改变
            old_port = self.server.settings.get('server.port', 8081)
            new_port = int(self.port_var.get())
            old_frontend_port = self.server.settings.get('server.frontend_port', 8082)
            new_frontend_port = int(self.frontend_port_var.get())
            port_changed = old_port != new_port or old_frontend_port != new_frontend_port

            # 更新服务器设置系统
            if hasattr(self.server, 'settings'):
                # 服务器设置
                self.server.settings.set('server.host', self.host_var.get())
                self.server.settings.set('server.port', new_port)
                self.server.settings.set('server.frontend_port', new_frontend_port)
                self.server.settings.set('server.debug', self.debug_var.get())
                self.server.settings.set('server.max_workers', int(self.max_conn_var.get()))

                # 数据库设置
                self.server.settings.set('database.host', self.db_host_var.get())
                self.server.settings.set('database.port', int(self.db_port_var.get()))
                self.server.settings.set('database.username', self.db_user_var.get())
                self.server.settings.set('database.password', self.db_pass_var.get())
                self.server.settings.set('database.database', self.db_name_var.get())

                # 文件设置
                max_size_bytes = int(self.max_size_var.get()) * 1024 * 1024
                self.server.settings.set('file_share.max_file_size', max_size_bytes)
                extensions = [ext.strip() for ext in self.extensions_var.get().split(',')]
                self.server.settings.set('file_share.allowed_extensions', extensions)

                # 安全设置
                self.server.settings.set('security.session_timeout', int(self.session_timeout_var.get()))
                self.server.settings.set('security.max_login_attempts', int(self.max_attempts_var.get()))

                # 搜索设置
                if hasattr(self, 'enable_text_search_var'):
                    self.server.settings.set('search.enable_text_search', self.enable_text_search_var.get())
                if hasattr(self, 'enable_image_search_var'):
                    self.server.settings.set('search.enable_image_search', self.enable_image_search_var.get())
                if hasattr(self, 'enable_image_upload_search_var'):
                    self.server.settings.set('search.enable_image_upload_search', self.enable_image_upload_search_var.get())
                if hasattr(self, 'max_search_results_var'):
                    self.server.settings.set('search.max_search_results', int(self.max_search_results_var.get()))
                if hasattr(self, 'image_search_threshold_var'):
                    self.server.settings.set('search.image_search_threshold', float(self.image_search_threshold_var.get()))
                if hasattr(self, 'max_upload_image_size_var'):
                    max_upload_size_bytes = int(self.max_upload_image_size_var.get()) * 1024 * 1024
                    self.server.settings.set('search.max_upload_image_size', max_upload_size_bytes)
                if hasattr(self, 'search_index_path_var'):
                    self.server.settings.set('search.index_path', self.search_index_path_var.get())

                # 保存设置到文件
                self.server.settings.save_settings()

            # 如果端口改变，询问是否重启服务器
            if port_changed:
                port_info = []
                if old_port != new_port:
                    port_info.append(f"API端口: {old_port} → {new_port}")
                if old_frontend_port != new_frontend_port:
                    port_info.append(f"前端端口: {old_frontend_port} → {new_frontend_port}")

                result = messagebox.askyesno("端口已更改",
                                           f"端口配置已更改:\n" + "\n".join(port_info) +
                                           f"\n\n是否立即重启服务器以应用新端口？\n"
                                           f"(选择'否'将在下次启动时生效)")

                if result:
                    self.restart_servers()
                else:
                    messagebox.showinfo("设置已保存", "设置已保存！\n新端口将在下次启动时生效。")
            else:
                messagebox.showinfo("设置已保存", "设置已保存并应用！")

        except ValueError as e:
            messagebox.showerror("输入错误", f"请检查输入的数值是否正确: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def restart_servers(self):
        """重启服务器"""
        try:
            # 停止当前服务器
            if hasattr(self.server, 'stop_server'):
                self.server.stop_server()

            # 等待一下确保端口释放
            import time
            time.sleep(2)

            # 重新启动服务器
            if hasattr(self.server, 'start_server'):
                if self.server.start_server():
                    messagebox.showinfo("重启成功",
                                      f"服务器已重启！\n"
                                      f"API端口: {self.port_var.get()}\n"
                                      f"前端端口: {self.frontend_port_var.get()}")
                else:
                    messagebox.showerror("重启失败", "服务器重启失败，请手动重启系统")
            else:
                messagebox.showwarning("警告", "服务器未初始化，无法重启")

        except Exception as e:
            messagebox.showerror("重启失败", f"重启服务器失败: {e}\n请手动重启系统")

    def restart_api_server(self):
        """重启API服务器（保持兼容性）"""
        self.restart_servers()

    def check_port(self):
        """检查API端口是否可用"""
        self._check_port_availability(self.port_var.get(), "API端口")

    def check_frontend_port(self):
        """检查前端端口是否可用"""
        self._check_port_availability(self.frontend_port_var.get(), "前端端口")

    def _check_port_availability(self, port_str, port_name):
        """检查端口是否可用的通用方法"""
        try:
            port = int(port_str)
            if port < 1 or port > 65535:
                messagebox.showerror("端口错误", f"{port_name}号必须在 1-65535 之间")
                return

            # 检查端口是否被占用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)

            result = sock.connect_ex(('localhost', port))
            sock.close()

            if result == 0:
                messagebox.showwarning("端口被占用", f"{port_name} {port} 已被其他程序占用！\n请选择其他端口。")
            else:
                messagebox.showinfo("端口可用", f"{port_name} {port} 可用！")

        except ValueError:
            messagebox.showerror("输入错误", f"请输入有效的{port_name}号")
        except Exception as e:
            messagebox.showerror("检查失败", f"{port_name}检查失败: {e}")
    
    def reset_defaults(self):
        """重置为默认设置"""
        result = messagebox.askyesno("确认", "确定要重置为默认设置吗？")
        if result:
            self.settings = self.default_settings.copy()
            self.update_ui_values()
            messagebox.showinfo("成功", "已重置为默认设置")
    
    def update_ui_values(self):
        """更新界面值"""
        # 更新服务器设置
        self.host_var.set(self.settings.get('server', {}).get('host', '0.0.0.0'))
        self.port_var.set(str(self.settings.get('server', {}).get('port', 8081)))
        self.frontend_port_var.set(str(self.settings.get('server', {}).get('frontend_port', 8082)))
        self.max_conn_var.set(str(self.settings.get('server', {}).get('max_connections', 100)))
        self.debug_var.set(self.settings.get('server', {}).get('debug', False))
        
        # 更新数据库设置
        self.db_path_var.set(self.settings.get('database', {}).get('path', 'backend/data/file_share_system.db'))
        
        # 更新文件设置
        self.max_size_var.set(str(self.settings.get('file', {}).get('max_file_size', 100)))
        self.thumb_size_var.set(str(self.settings.get('file', {}).get('thumbnail_size', 200)))
        self.scan_interval_var.set(str(self.settings.get('file', {}).get('scan_interval', 300)))
        extensions = ', '.join(self.settings.get('file', {}).get('allowed_extensions', ['.jpg', '.png', '.pdf']))
        self.extensions_var.set(extensions)

        # 更新安全设置
        self.session_timeout_var.set(str(self.settings.get('security', {}).get('session_timeout', 3600)))
        self.max_attempts_var.set(str(self.settings.get('security', {}).get('max_login_attempts', 5)))
        self.min_pass_len_var.set(str(self.settings.get('security', {}).get('password_min_length', 6)))
        self.enable_encryption_var.set(self.settings.get('security', {}).get('enable_encryption', True))

        # 更新通知设置
        self.enable_email_var.set(self.settings.get('notification', {}).get('enable_email', False))
        self.smtp_server_var.set(self.settings.get('notification', {}).get('smtp_server', ''))
        self.smtp_port_var.set(str(self.settings.get('notification', {}).get('smtp_port', 587)))
        self.email_user_var.set(self.settings.get('notification', {}).get('email_username', ''))
        self.email_pass_var.set(self.settings.get('notification', {}).get('email_password', ''))
    
    def browse_database_path(self):
        """浏览数据库文件路径"""
        from tkinter import filedialog
        try:
            file_path = filedialog.asksaveasfilename(
                title="选择或创建数据库文件",
                defaultextension=".db",
                filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
            )
            if file_path:
                self.db_path_var.set(file_path)
        except Exception as e:
            messagebox.showerror("错误", f"选择文件失败: {e}")

    def test_database_connection(self):
        """测试数据库连接 - SQLite版本"""
        try:
            import sqlite3
            import os
            
            # 检查SQLite数据库文件
            db_path = self.db_path_var.get()
            if os.path.exists(db_path):
                connection = sqlite3.connect(db_path)
                cursor = connection.cursor()
                
                # 检查数据库版本
                cursor.execute("SELECT sqlite_version()")
                version = cursor.fetchone()[0]
                
                # 检查表的数量
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                
                connection.close()
                messagebox.showinfo("成功", f"SQLite数据库连接成功！\n版本: {version}\n表数量: {table_count}")
            else:
                messagebox.showwarning("提示", "SQLite数据库文件不存在，系统将在启动时自动创建")
        except Exception as e:
            messagebox.showerror("错误", f"数据库连接测试失败: {e}")

    def refresh_database_info(self):
        """刷新数据库信息"""
        try:
            import sqlite3
            import os
            
            db_path = self.db_path_var.get()
            
            # 清空文本框
            self.db_info_text.config(state=tk.NORMAL)
            self.db_info_text.delete(1.0, tk.END)
            
            if os.path.exists(db_path):
                connection = sqlite3.connect(db_path)
                cursor = connection.cursor()
                
                # 获取数据库信息
                info_text = f"数据库路径: {db_path}\n"
                info_text += f"文件大小: {os.path.getsize(db_path) / 1024:.2f} KB\n"
                
                # SQLite版本
                cursor.execute("SELECT sqlite_version()")
                version = cursor.fetchone()[0]
                info_text += f"SQLite版本: {version}\n\n"
                
                # 表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
                tables = cursor.fetchall()
                info_text += f"数据表 ({len(tables)} 个):\n"
                
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    info_text += f"  - {table_name}: {count} 条记录\n"
                
                connection.close()
            else:
                info_text = f"数据库文件不存在: {db_path}\n"
                info_text += "系统将在启动时自动创建数据库文件"
            
            self.db_info_text.insert(1.0, info_text)
            self.db_info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.db_info_text.config(state=tk.NORMAL)
            self.db_info_text.delete(1.0, tk.END)
            self.db_info_text.insert(1.0, f"获取数据库信息失败: {e}")
            self.db_info_text.config(state=tk.DISABLED)

    def backup_database(self):
        """备份数据库"""
        from tkinter import filedialog
        import shutil
        try:
            db_path = self.db_path_var.get()
            if not os.path.exists(db_path):
                messagebox.showwarning("警告", "数据库文件不存在，无法备份")
                return
            
            backup_path = filedialog.asksaveasfilename(
                title="保存数据库备份",
                defaultextension=".db",
                filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
            )
            
            if backup_path:
                shutil.copy2(db_path, backup_path)
                messagebox.showinfo("成功", f"数据库备份成功！\n备份文件: {backup_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"备份数据库失败: {e}")
    
    def import_settings(self):
        """导入设置"""
        file_path = filedialog.askopenfilename(
            title="选择设置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)
                self.settings = imported_settings
                self.update_ui_values()
                messagebox.showinfo("成功", "设置导入成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导入设置失败: {e}")
    
    def export_settings(self):
        """导出设置"""
        file_path = filedialog.asksaveasfilename(
            title="保存设置文件",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.settings, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", "设置导出成功！")
            except Exception as e:
                messagebox.showerror("错误", f"导出设置失败: {e}")
    
    def reset_database(self):
        """重置数据库"""
        import sqlite3
        import os
        
        # 确认对话框
        result = messagebox.askyesnocancel(
            "⚠️ 危险操作确认", 
            "重置数据库将永久删除以下所有数据：\n\n"
            "• 所有用户账户（除admin外）\n"
            "• 所有文件共享记录\n"
            "• 所有下载记录\n"
            "• 所有活动日志\n"
            "• 所有权限设置\n\n"
            "此操作无法撤销！\n\n"
            "确定要继续吗？"
        )
        
        if not result:
            return
            
        # 二次确认
        confirm_text = messagebox.askstring(
            "最终确认",
            "请输入 'RESET' 来确认重置数据库："
        )
        
        if confirm_text != "RESET":
            messagebox.showinfo("取消", "操作已取消")
            return
            
        try:
            db_path = self.db_path_var.get()
            
            # 停止服务器（如果正在运行）
            if hasattr(self.server, 'running') and self.server.running:
                messagebox.showinfo("提示", "正在停止服务器...")
                self.server.stop_server()
                
            # 删除数据库文件
            if os.path.exists(db_path):
                os.remove(db_path)
                
            # 重新创建数据库
            from backend.init_database_sqlite import create_database
            if create_database():
                messagebox.showinfo("成功", 
                    "数据库重置成功！\n\n"
                    "默认管理员账户：\n"
                    "用户名：admin\n"
                    "密码：admin123\n\n"
                    "请重新启动服务器")
                
                # 刷新数据库信息显示
                self.refresh_database_info()
            else:
                messagebox.showerror("错误", "重新创建数据库失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"重置数据库失败: {e}")

    def rebuild_search_index(self):
        """重建搜索索引"""
        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认重建索引",
                "重建搜索索引将删除现有索引并重新扫描所有文件。\n"
                "这可能需要一些时间，确定要继续吗？"
            )

            if result:
                messagebox.showinfo("提示", "搜索索引重建功能需要在服务器运行时执行。\n请在主界面使用相关功能。")

        except Exception as e:
            messagebox.showerror("错误", f"重建搜索索引失败: {e}")

    def clear_search_index(self):
        """清空搜索索引"""
        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认清空索引",
                "清空搜索索引将删除所有搜索索引数据。\n"
                "确定要继续吗？"
            )

            if result:
                import os
                import shutil

                index_path = self.search_index_path_var.get()
                if os.path.exists(index_path):
                    shutil.rmtree(index_path)
                    messagebox.showinfo("成功", "搜索索引已清空！")
                else:
                    messagebox.showinfo("提示", "搜索索引目录不存在。")

        except Exception as e:
            messagebox.showerror("错误", f"清空搜索索引失败: {e}")

    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None
