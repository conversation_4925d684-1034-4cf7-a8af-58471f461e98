#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移：修复user_sessions表结构
"""

import sqlite3
import os
from datetime import datetime

def migrate_database(db_path: str):
    """执行数据库迁移"""
    print(f"开始修复user_sessions表: {db_path}")
    
    # 备份数据库
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 检查user_sessions表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_sessions'")
        if not cursor.fetchone():
            print("user_sessions表不存在，创建新表...")
            create_user_sessions_table(cursor)
        else:
            print("user_sessions表已存在，检查字段...")
            
            # 2. 检查表结构
            cursor.execute("PRAGMA table_info(user_sessions)")
            columns = {column[1]: column[2] for column in cursor.fetchall()}
            print(f"当前字段: {list(columns.keys())}")
            
            # 3. 检查是否需要添加缺失的字段
            required_fields = {
                'total_page_views': 'INTEGER DEFAULT 0',
                'total_searches': 'INTEGER DEFAULT 0', 
                'total_downloads': 'INTEGER DEFAULT 0',
                'login_method': 'VARCHAR(20) DEFAULT "web"',
                'last_activity': 'DATETIME',
                'is_active': 'BOOLEAN DEFAULT 1',
                'logout_reason': 'VARCHAR(50)'
            }
            
            missing_fields = []
            for field, field_type in required_fields.items():
                if field not in columns:
                    missing_fields.append((field, field_type))
            
            if missing_fields:
                print(f"需要添加的字段: {[field[0] for field in missing_fields]}")
                
                # 添加缺失的字段
                for field, field_type in missing_fields:
                    try:
                        cursor.execute(f"ALTER TABLE user_sessions ADD COLUMN {field} {field_type}")
                        print(f"✅ 添加字段: {field}")
                    except sqlite3.OperationalError as e:
                        if "duplicate column name" in str(e).lower():
                            print(f"⚠️ 字段 {field} 已存在")
                        else:
                            print(f"❌ 添加字段 {field} 失败: {e}")
            else:
                print("✅ 所有必需字段都已存在")
        
        conn.commit()
        print("user_sessions表修复完成！")
        
        # 4. 验证修复结果
        print("\n验证修复结果:")
        cursor.execute("PRAGMA table_info(user_sessions)")
        print("user_sessions 表字段:")
        for column in cursor.fetchall():
            print(f"  - {column[1]} ({column[2]})")
            
    except Exception as e:
        print(f"修复失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def create_user_sessions_table(cursor):
    """创建user_sessions表"""
    create_sql = """
    CREATE TABLE user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        session_id VARCHAR(100) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        login_method VARCHAR(20) DEFAULT 'web',
        total_page_views INTEGER DEFAULT 0,
        total_searches INTEGER DEFAULT 0,
        total_downloads INTEGER DEFAULT 0,
        login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        logout_time DATETIME,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        logout_reason VARCHAR(50),
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    """
    cursor.execute(create_sql)
    print("✅ user_sessions表创建成功")

def main():
    """主函数"""
    # 数据库路径 - 使用绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    db_path = os.path.join(project_root, "data", "file_share_system.db")

    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"数据库路径: {db_path}")

    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，请先运行系统初始化")
        return

    migrate_database(db_path)

if __name__ == "__main__":
    main()
