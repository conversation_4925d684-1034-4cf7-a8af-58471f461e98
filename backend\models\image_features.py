#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像特征模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, BLOB, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
import hashlib
import json
import numpy as np
from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

@dataclass
class ImageFeatures:
    """图像特征数据结构"""
    file_id: int
    color_histogram: Optional[np.ndarray] = None  # 颜色直方图 (256,)
    texture_features: Optional[np.ndarray] = None  # 纹理特征 (128,)
    edge_features: Optional[np.ndarray] = None     # 边缘特征 (64,)
    keypoints: Optional[List] = None              # 关键点
    descriptors: Optional[np.ndarray] = None      # 关键点描述符
    image_hash: Optional[str] = None              # 感知哈希
    extraction_time: Optional[datetime] = None
    feature_version: str = "1.0"                  # 特征提取算法版本

@dataclass
class SearchOptions:
    """搜索选项配置"""
    similarity_threshold: float = 0.7    # 相似度阈值
    max_results: int = 50               # 最大结果数
    enable_partial_match: bool = True   # 启用部分匹配
    color_weight: float = 0.3           # 颜色特征权重
    texture_weight: float = 0.3         # 纹理特征权重
    keypoint_weight: float = 0.4        # 关键点特征权重
    folder_filter: Optional[List[int]] = None     # 文件夹过滤
    file_type_filter: Optional[List[str]] = None  # 文件类型过滤

@dataclass
class ImageMatch:
    """图像匹配结果"""
    file_id: int
    filename: str
    folder_id: int
    similarity_score: float
    match_type: str  # 'full', 'partial', 'color', 'texture'
    thumbnail_path: Optional[str] = None
    file_size: Optional[int] = None
    image_dimensions: Optional[tuple] = None

@dataclass
class SearchResult:
    """搜索结果"""
    query_image_path: str
    total_results: int
    search_time: float
    results: List[ImageMatch]

class ImageFeatureIndex(Base):
    """图像特征索引模型"""
    
    __tablename__ = 'image_features'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('shared_files.id', ondelete='CASCADE'), nullable=False)
    
    # 特征标识
    feature_hash = Column(String(64), nullable=False, comment='特征哈希值')
    image_hash = Column(String(64), nullable=True, comment='感知哈希')
    
    # 特征数据（BLOB存储）
    color_histogram = Column(BLOB, nullable=True, comment='颜色直方图特征')
    texture_features = Column(BLOB, nullable=True, comment='纹理特征')
    edge_features = Column(BLOB, nullable=True, comment='边缘特征')
    
    # 关键点信息
    keypoint_count = Column(Integer, default=0, comment='关键点数量')
    descriptors_path = Column(Text, nullable=True, comment='描述符文件路径')
    
    # 元数据
    extraction_time = Column(DateTime, nullable=True, comment='特征提取时间')
    feature_version = Column(String(20), default='1.0', comment='特征版本')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    file = relationship("SharedFile", backref="image_features")
    
    def __init__(self, file_id: int, **kwargs):
        self.file_id = file_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_features(self, features: ImageFeatures):
        """设置图像特征"""
        try:
            # 生成特征哈希
            self.feature_hash = self._generate_feature_hash(features)
            self.image_hash = features.image_hash
            
            # 存储特征向量（序列化为bytes）
            if features.color_histogram is not None:
                self.color_histogram = features.color_histogram.tobytes()
            
            if features.texture_features is not None:
                self.texture_features = features.texture_features.tobytes()
            
            if features.edge_features is not None:
                self.edge_features = features.edge_features.tobytes()
            
            # 关键点信息
            if features.keypoints is not None:
                self.keypoint_count = len(features.keypoints)
            
            # 元数据
            self.extraction_time = features.extraction_time or datetime.now()
            self.feature_version = features.feature_version
            
            return True
            
        except Exception as e:
            print(f"设置图像特征失败: {e}")
            return False
    
    def get_features(self) -> Optional[ImageFeatures]:
        """获取图像特征"""
        try:
            features = ImageFeatures(file_id=self.file_id)
            
            # 反序列化特征向量
            if self.color_histogram:
                features.color_histogram = np.frombuffer(self.color_histogram, dtype=np.float32)
            
            if self.texture_features:
                features.texture_features = np.frombuffer(self.texture_features, dtype=np.float32)
            
            if self.edge_features:
                features.edge_features = np.frombuffer(self.edge_features, dtype=np.float32)
            
            # 设置其他属性
            features.image_hash = self.image_hash
            features.extraction_time = self.extraction_time
            features.feature_version = self.feature_version
            
            return features
            
        except Exception as e:
            print(f"获取图像特征失败: {e}")
            return None
    
    def _generate_feature_hash(self, features: ImageFeatures) -> str:
        """生成特征哈希值"""
        try:
            hash_input = f"{self.file_id}_{features.feature_version}"
            
            # 添加特征向量的哈希
            if features.color_histogram is not None:
                hash_input += f"_color_{hash(features.color_histogram.tobytes())}"
            
            if features.texture_features is not None:
                hash_input += f"_texture_{hash(features.texture_features.tobytes())}"
            
            if features.edge_features is not None:
                hash_input += f"_edge_{hash(features.edge_features.tobytes())}"
            
            return hashlib.md5(hash_input.encode()).hexdigest()
            
        except Exception as e:
            print(f"生成特征哈希失败: {e}")
            return hashlib.md5(f"{self.file_id}_{datetime.now()}".encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'feature_hash': self.feature_hash,
            'image_hash': self.image_hash,
            'keypoint_count': self.keypoint_count,
            'descriptors_path': self.descriptors_path,
            'extraction_time': self.extraction_time.isoformat() if self.extraction_time else None,
            'feature_version': self.feature_version,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'has_color_features': self.color_histogram is not None,
            'has_texture_features': self.texture_features is not None,
            'has_edge_features': self.edge_features is not None
        }

class ImageSearchHistory(Base):
    """图像搜索历史模型"""
    
    __tablename__ = 'image_search_history'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query_image_hash = Column(String(64), nullable=True, comment='查询图像哈希')
    query_image_path = Column(Text, nullable=True, comment='查询图像路径')
    search_options = Column(Text, nullable=True, comment='搜索选项JSON')
    result_count = Column(Integer, default=0, comment='结果数量')
    search_time = Column(Float, default=0.0, comment='搜索耗时(秒)')
    user_id = Column(Integer, nullable=True, comment='用户ID')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_search_options(self, options: SearchOptions):
        """设置搜索选项"""
        try:
            options_dict = {
                'similarity_threshold': options.similarity_threshold,
                'max_results': options.max_results,
                'enable_partial_match': options.enable_partial_match,
                'color_weight': options.color_weight,
                'texture_weight': options.texture_weight,
                'keypoint_weight': options.keypoint_weight,
                'folder_filter': options.folder_filter,
                'file_type_filter': options.file_type_filter
            }
            self.search_options = json.dumps(options_dict)
        except Exception as e:
            print(f"设置搜索选项失败: {e}")
            self.search_options = "{}"
    
    def get_search_options(self) -> Optional[SearchOptions]:
        """获取搜索选项"""
        try:
            if not self.search_options:
                return SearchOptions()
            
            options_dict = json.loads(self.search_options)
            return SearchOptions(**options_dict)
            
        except Exception as e:
            print(f"获取搜索选项失败: {e}")
            return SearchOptions()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'query_image_hash': self.query_image_hash,
            'query_image_path': self.query_image_path,
            'search_options': json.loads(self.search_options) if self.search_options else {},
            'result_count': self.result_count,
            'search_time': self.search_time,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class ImageSearchCache(Base):
    """图像搜索缓存模型"""
    
    __tablename__ = 'image_search_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query_hash = Column(String(64), nullable=False, comment='查询哈希')
    options_hash = Column(String(64), nullable=False, comment='选项哈希')
    results = Column(Text, nullable=True, comment='搜索结果JSON')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    
    def __init__(self, query_hash: str, options_hash: str, **kwargs):
        self.query_hash = query_hash
        self.options_hash = options_hash
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def set_results(self, search_result: SearchResult):
        """设置搜索结果"""
        try:
            result_dict = {
                'query_image_path': search_result.query_image_path,
                'total_results': search_result.total_results,
                'search_time': search_result.search_time,
                'results': [
                    {
                        'file_id': match.file_id,
                        'filename': match.filename,
                        'folder_id': match.folder_id,
                        'similarity_score': match.similarity_score,
                        'match_type': match.match_type,
                        'thumbnail_path': match.thumbnail_path,
                        'file_size': match.file_size,
                        'image_dimensions': match.image_dimensions
                    }
                    for match in search_result.results
                ]
            }
            self.results = json.dumps(result_dict)
        except Exception as e:
            print(f"设置搜索结果失败: {e}")
            self.results = "{}"
    
    def get_results(self) -> Optional[SearchResult]:
        """获取搜索结果"""
        try:
            if not self.results:
                return None
            
            result_dict = json.loads(self.results)
            
            matches = []
            for match_data in result_dict.get('results', []):
                match = ImageMatch(
                    file_id=match_data['file_id'],
                    filename=match_data['filename'],
                    folder_id=match_data['folder_id'],
                    similarity_score=match_data['similarity_score'],
                    match_type=match_data['match_type'],
                    thumbnail_path=match_data.get('thumbnail_path'),
                    file_size=match_data.get('file_size'),
                    image_dimensions=match_data.get('image_dimensions')
                )
                matches.append(match)
            
            return SearchResult(
                query_image_path=result_dict['query_image_path'],
                total_results=result_dict['total_results'],
                search_time=result_dict['search_time'],
                results=matches
            )
            
        except Exception as e:
            print(f"获取搜索结果失败: {e}")
            return None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'query_hash': self.query_hash,
            'options_hash': self.options_hash,
            'results': json.loads(self.results) if self.results else {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired()
        }