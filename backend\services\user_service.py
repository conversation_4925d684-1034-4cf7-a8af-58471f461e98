#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理服务
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

from models.user import User
from utils.logger import setup_logger, DatabaseLogger, SecurityLogger

class UserService:
    """用户管理服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("UserService")
        self.db_logger = DatabaseLogger(db_manager) if db_manager else None
        self.security_logger = SecurityLogger()
        
        # 用户会话缓存
        self.active_sessions = {}
        
        # 登录失败记录
        self.failed_attempts = {}
    
    def create_user(self, username: str, password: str, email: str = None, 
                   full_name: str = None, user_group: str = 'user', 
                   is_admin: bool = False, **kwargs) -> Dict[str, Any]:
        """创建用户"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "数据库服务不可用，无法创建用户"}
            
            with self.db_manager.get_session() as session:
                # 检查用户名是否已存在
                existing_user = session.query(User).filter_by(username=username).first()
                if existing_user:
                    return {
                        "success": False, 
                        "error": f"⚠️ 用户名 '{username}' 已经存在！",
                        "error_type": "duplicate_username",
                        "suggestion": "请选择其他用户名或检查是否该用户已被创建"
                    }
                
                # 检查邮箱是否已存在
                if email:
                    existing_email = session.query(User).filter_by(email=email).first()
                    if existing_email:
                        return {
                            "success": False, 
                            "error": f"⚠️ 邮箱地址 '{email}' 已被其他用户使用！",
                            "error_type": "duplicate_email",
                            "suggestion": f"该邮箱已被用户 '{existing_email.username}' 使用，请使用其他邮箱地址"
                        }
                
                # 创建新用户
                user = User(
                    username=username,
                    password=password,
                    email=email,
                    full_name=full_name,
                    user_group=user_group,
                    is_admin=is_admin,
                    **kwargs
                )
                
                session.add(user)
                session.commit()
                
                self.logger.info(f"用户创建成功: {username}")
                
                return {
                    "success": True,
                    "message": f"✅ 用户 '{username}' 创建成功！",
                    "user_id": user.id,
                    "username": user.username
                }
                
        except Exception as e:
            error_message = str(e)
            self.logger.error(f"创建用户失败: {e}")
            
            # 检查是否是数据库约束错误
            if "UNIQUE constraint failed" in error_message:
                if "users.username" in error_message:
                    return {
                        "success": False, 
                        "error": f"❌ 数据库错误：用户名 '{username}' 已存在！",
                        "error_type": "database_constraint",
                        "suggestion": "请刷新页面并检查用户列表，或选择其他用户名"
                    }
                elif "users.email" in error_message:
                    return {
                        "success": False, 
                        "error": f"❌ 数据库错误：邮箱地址 '{email}' 已被使用！",
                        "error_type": "database_constraint", 
                        "suggestion": "请使用其他邮箱地址"
                    }
                else:
                    return {
                        "success": False, 
                        "error": "❌ 数据完整性错误：存在重复的用户信息！",
                        "error_type": "database_constraint",
                        "suggestion": "请检查用户名和邮箱是否已被使用"
                    }
            
            return {
                "success": False, 
                "error": f"❌ 创建用户时发生错误: {error_message}",
                "error_type": "system_error"
            }
    
    def authenticate_user(self, username: str, password: str, 
                         ip_address: str = None, user_agent: str = None) -> Dict[str, Any]:
        """用户认证"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "数据库不可用"}
            
            # 检查IP是否被临时禁用
            if self._is_ip_blocked(ip_address):
                self.security_logger.log_failed_login(username, ip_address, "IP被临时禁用")
                return {"success": False, "error": "IP地址被临时禁用，请稍后再试"}
            
            with self.db_manager.get_session() as session:
                user = session.query(User).filter_by(username=username).first()
                
                if not user:
                    self._record_failed_attempt(ip_address)
                    self.security_logger.log_failed_login(username, ip_address, "用户不存在")
                    return {"success": False, "error": "用户名或密码错误"}
                
                # 检查用户是否被禁用（旧系统）
                if user.is_banned_now():
                    self.security_logger.log_failed_login(username, ip_address, "用户被禁用")
                    return {"success": False, "error": "账户已被禁用"}

                # 检查用户是否被封禁（新系统）
                try:
                    from services.user_behavior_service import UserBehaviorService
                    behavior_service = UserBehaviorService(self.db_manager)
                    ban_check = behavior_service.check_user_ban(user.id, 'login')

                    if ban_check.get('is_banned'):
                        ban_info = ban_check.get('ban_info', {})
                        reason = ban_info.get('reason', '违规操作')
                        remaining_time = ban_info.get('remaining_time', '')

                        error_msg = f"账户已被封禁"
                        if remaining_time:
                            error_msg += f"，剩余时间：{remaining_time}"
                        if reason:
                            error_msg += f"，原因：{reason}"

                        self.security_logger.log_failed_login(username, ip_address, f"用户被封禁: {reason}")
                        return {"success": False, "error": error_msg}
                except Exception as e:
                    self.logger.warning(f"检查用户封禁状态失败: {e}")
                    # 如果封禁检查失败，为了安全起见，继续登录流程但记录警告
                
                # 检查密码
                if not user.check_password(password):
                    user.record_login_failure()
                    self._record_failed_attempt(ip_address)
                    
                    # 检查是否需要禁用账户
                    if user.failed_login_attempts >= 5:
                        user.ban_user(duration_minutes=30)
                        self.logger.warning(f"用户 {username} 因多次登录失败被临时禁用")
                    
                    session.commit()
                    self.security_logger.log_failed_login(username, ip_address, "密码错误")
                    return {"success": False, "error": "用户名或密码错误"}
                
                # 登录成功
                user.record_login_success()
                session_token = user.generate_session_token()
                session.commit()

                # 缓存会话
                self.active_sessions[session_token] = {
                    'user_id': user.id,
                    'username': user.username,
                    'login_time': datetime.now(),
                    'ip_address': ip_address,
                    'user_agent': user_agent
                }

                # 创建在线用户记录
                try:
                    from services.user_behavior_service import UserBehaviorService
                    behavior_service = UserBehaviorService(self.db_manager)
                    behavior_service.create_or_update_online_user(
                        user_id=user.id,
                        session_id=session_token,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        current_page='login'
                    )
                except Exception as e:
                    self.logger.warning(f"创建在线用户记录失败: {e}")

                # 记录登录日志
                if self.db_logger:
                    self.db_logger.log_login(user.id, True, ip_address, user_agent)
                
                self.logger.info(f"用户登录成功: {username}")
                
                return {
                    "success": True,
                    "user": user.to_dict(),
                    "session_token": session_token
                }
                
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return {"success": False, "error": "认证服务异常"}
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """验证会话"""
        try:
            if not session_token or not self.db_manager:
                return None
            
            # 检查缓存
            if session_token in self.active_sessions:
                session_info = self.active_sessions[session_token]
                
                # 验证数据库中的会话
                with self.db_manager.get_session() as session:
                    user = session.query(User).filter_by(id=session_info['user_id']).first()
                    
                    if user and user.session_token == session_token and user.is_session_valid():
                        # 检查用户是否被封禁
                        if user.is_banned_now():
                            # 用户被禁用，清除会话
                            del self.active_sessions[session_token]
                            return None

                        # 检查新封禁系统
                        try:
                            from services.user_behavior_service import UserBehaviorService
                            behavior_service = UserBehaviorService(self.db_manager)
                            ban_check = behavior_service.check_user_ban(user.id, 'login')

                            if ban_check.get('is_banned'):
                                # 用户被封禁，清除会话
                                del self.active_sessions[session_token]
                                return None
                        except Exception as e:
                            self.logger.warning(f"检查用户封禁状态失败: {e}")

                        return {
                            'user_id': user.id,
                            'username': user.username,
                            'permissions': user.get_permissions(),
                            'is_admin': user.is_admin
                        }
                    else:
                        # 会话无效，清除缓存
                        del self.active_sessions[session_token]
            
            return None
            
        except Exception as e:
            self.logger.error(f"会话验证失败: {e}")
            return None
    
    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        try:
            if not session_token or not self.db_manager:
                return False
            
            # 清除数据库中的会话
            with self.db_manager.get_session() as session:
                user = session.query(User).filter_by(session_token=session_token).first()
                if user:
                    user.clear_session()
                    session.commit()
            
            # 清除缓存
            if session_token in self.active_sessions:
                del self.active_sessions[session_token]
            
            return True
            
        except Exception as e:
            self.logger.error(f"用户登出失败: {e}")
            return False
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户信息"""
        try:
            if not self.db_manager:
                return None
            
            with self.db_manager.get_session() as session:
                user = session.query(User).filter_by(id=user_id).first()
                return user.to_dict() if user else None
                
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
    
    def get_user_list(self, page: int = 1, page_size: int = 20, 
                     search_query: str = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "数据库不可用"}
            
            with self.db_manager.get_session() as session:
                query = session.query(User)
                
                # 搜索过滤
                if search_query:
                    query = query.filter(
                        User.username.contains(search_query) |
                        User.full_name.contains(search_query) |
                        User.email.contains(search_query)
                    )
                
                # 分页
                total_count = query.count()
                offset = (page - 1) * page_size
                users = query.offset(offset).limit(page_size).all()
                
                users_data = [user.to_dict() for user in users]
                
                return {
                    "success": True,
                    "users": users_data,
                    "total_count": total_count,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
                
        except Exception as e:
            self.logger.error(f"获取用户列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def update_user(self, user_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "数据库不可用"}
            
            with self.db_manager.get_session() as session:
                user = session.query(User).filter_by(id=user_id).first()
                if not user:
                    return {"success": False, "error": "用户不存在"}
                
                # 更新密码
                if 'password' in updates:
                    user.set_password(updates['password'])
                    del updates['password']
                
                # 更新其他属性
                for key, value in updates.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                
                session.commit()
                
                self.logger.info(f"用户信息更新成功: {user.username}")
                
                return {"success": True}
                
        except Exception as e:
            self.logger.error(f"更新用户信息失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_user(self, user_id: int) -> Dict[str, Any]:
        """删除用户"""
        try:
            if not self.db_manager:
                return {"success": False, "error": "数据库不可用"}

            with self.db_manager.get_session() as session:
                user = session.query(User).filter_by(id=user_id).first()
                if not user:
                    return {"success": False, "error": "用户不存在"}

                username = user.username

                # 先清理相关的记录，避免外键约束问题
                try:
                    # 清理在线用户记录
                    from models.online_user import OnlineUser
                    session.query(OnlineUser).filter_by(user_id=user_id).delete()

                    # 清理用户会话记录
                    from models.online_user import UserSession
                    session.query(UserSession).filter_by(user_id=user_id).delete()

                    # 清理搜索记录
                    try:
                        from models.search_record import SearchRecord
                        session.query(SearchRecord).filter_by(user_id=user_id).delete()
                    except ImportError:
                        pass  # 如果模型不存在，跳过

                    # 清理下载记录
                    try:
                        from models.download_record import DownloadRecord
                        session.query(DownloadRecord).filter_by(user_id=user_id).delete()
                    except ImportError:
                        pass  # 如果模型不存在，跳过

                    # 清理用户行为记录
                    try:
                        from models.user_behavior import ActivityLog
                        session.query(ActivityLog).filter_by(user_id=user_id).delete()
                    except ImportError:
                        pass  # 如果模型不存在，跳过

                    # 清理用户封禁记录
                    try:
                        from models.user_behavior import UserBan
                        session.query(UserBan).filter_by(user_id=user_id).delete()
                    except ImportError:
                        pass  # 如果模型不存在，跳过

                except Exception as cleanup_error:
                    self.logger.warning(f"清理用户相关记录时出现警告: {cleanup_error}")

                # 删除用户记录
                session.delete(user)
                session.commit()

                # 清理内存中的会话
                tokens_to_remove = []
                for token, session_info in self.active_sessions.items():
                    if session_info.get('user_id') == user_id:
                        tokens_to_remove.append(token)

                for token in tokens_to_remove:
                    del self.active_sessions[token]

                self.logger.info(f"用户删除成功: {username}")

                return {"success": True}

        except Exception as e:
            self.logger.error(f"删除用户失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_online_users(self) -> List[Dict[str, Any]]:
        """获取在线用户列表"""
        try:
            online_users = []
            current_time = datetime.now()
            
            for token, session_info in list(self.active_sessions.items()):
                # 检查会话是否过期
                login_time = session_info.get('login_time', current_time)
                if current_time - login_time > timedelta(hours=24):
                    del self.active_sessions[token]
                    continue
                
                online_users.append({
                    'user_id': session_info['user_id'],
                    'username': session_info['username'],
                    'login_time': login_time.isoformat(),
                    'ip_address': session_info.get('ip_address'),
                    'online_duration': str(current_time - login_time)
                })
            
            return online_users
            
        except Exception as e:
            self.logger.error(f"获取在线用户失败: {e}")
            return []
    
    def _is_ip_blocked(self, ip_address: str) -> bool:
        """检查IP是否被阻止"""
        if not ip_address:
            return False
        
        if ip_address in self.failed_attempts:
            attempts = self.failed_attempts[ip_address]
            if attempts['count'] >= 10:  # 10次失败后阻止
                if datetime.now() - attempts['last_attempt'] < timedelta(minutes=30):
                    return True
                else:
                    # 重置计数
                    del self.failed_attempts[ip_address]
        
        return False
    
    def _record_failed_attempt(self, ip_address: str):
        """记录失败尝试"""
        if not ip_address:
            return
        
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = {'count': 0, 'last_attempt': datetime.now()}
        
        self.failed_attempts[ip_address]['count'] += 1
        self.failed_attempts[ip_address]['last_attempt'] = datetime.now()
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            if not self.db_manager:
                return {}
            
            with self.db_manager.get_session() as session:
                total_users = session.query(User).count()
                active_users = session.query(User).filter_by(is_active=True).count()
                admin_users = session.query(User).filter_by(is_admin=True).count()
                banned_users = session.query(User).filter_by(is_banned=True).count()
                
                return {
                    'total_users': total_users,
                    'active_users': active_users,
                    'admin_users': admin_users,
                    'banned_users': banned_users,
                    'online_users': len(self.active_sessions)
                }
                
        except Exception as e:
            self.logger.error(f"获取用户统计失败: {e}")
            return {}
