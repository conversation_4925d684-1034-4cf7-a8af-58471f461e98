2025-07-31 09:24:30 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-31 09:24:30 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-31 09:24:30 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-31 01:24:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-31 01:24:48 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:24:48 - APIServer - INFO - 客户端连接: mwh9xVW1mQXlSCNzAAAB
2025-07-31 01:24:48 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:24:48 - APIServer - INFO - 客户端连接: MTAZWitlfKlkezyaAAAD
2025-07-31 01:25:02 - APIServer - INFO - === 调试模式：收到下载记录请求 ===
2025-07-31 01:25:02 - APIServer - INFO - 收到的token: lQ9VlyGM7bVELftU7Yu6...
2025-07-31 01:25:02 - APIServer - INFO - 用户验证结果: None
2025-07-31 01:25:02 - APIServer - WARNING - 用户验证失败，返回401
2025-07-31 01:25:02 - APIServer - INFO - === 调试模式：收到下载记录请求 ===
2025-07-31 01:25:02 - APIServer - INFO - 收到的token: lQ9VlyGM7bVELftU7Yu6...
2025-07-31 01:25:02 - APIServer - INFO - 用户验证结果: None
2025-07-31 01:25:02 - APIServer - WARNING - 用户验证失败，返回401
2025-07-31 01:25:02 - APIServer - INFO - === 调试模式：收到下载记录请求 ===
2025-07-31 01:25:02 - APIServer - INFO - 收到的token: lQ9VlyGM7bVELftU7Yu6...
2025-07-31 01:25:02 - APIServer - INFO - 用户验证结果: None
2025-07-31 01:25:02 - APIServer - WARNING - 用户验证失败，返回401
2025-07-31 01:25:02 - APIServer - INFO - === 调试模式：收到下载记录请求 ===
2025-07-31 01:25:02 - APIServer - INFO - 收到的token: lQ9VlyGM7bVELftU7Yu6...
2025-07-31 01:25:02 - APIServer - INFO - 用户验证结果: None
2025-07-31 01:25:02 - APIServer - WARNING - 用户验证失败，返回401
2025-07-31 01:25:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:04 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:10 - APIServer - INFO - 客户端断开连接: mwh9xVW1mQXlSCNzAAAB
2025-07-31 01:25:10 - APIServer - INFO - 客户端断开连接: MTAZWitlfKlkezyaAAAD
2025-07-31 01:25:16 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:16 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 21, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T00:56:25', 'updated_at': '2025-07-31T01:12:44', 'last_scanned': '2025-07-31T01:12:44.734318', 'file_count': 2}, {'id': 23, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:19:49', 'updated_at': '2025-07-31T01:19:53', 'last_scanned': '2025-07-31T01:19:53.357413', 'file_count': 2}]}
2025-07-31 01:25:16 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-31 01:25:16 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:25:16 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 21, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T00:56:25', 'updated_at': '2025-07-31T01:12:44', 'last_scanned': '2025-07-31T01:12:44.734318', 'file_count': 2}, {'id': 23, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:19:49', 'updated_at': '2025-07-31T01:19:53', 'last_scanned': '2025-07-31T01:19:53.357413', 'file_count': 2}]}
2025-07-31 01:25:16 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-31 01:25:16 - APIServer - INFO - 客户端连接: ZapjNv9G5ultZfHAAAAF
2025-07-31 01:25:16 - APIServer - INFO - 客户端连接: ekLGy6ce5eV1cng8AAAH
2025-07-31 01:25:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1211
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件ID: 1211, 文件信息: {'id': 1211, 'folder_id': 23, 'filename': '乡村振兴22.jpg', 'relative_path': '乡村振兴22.jpg', 'file_size': 4610118, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2023-03-18T10:22:47.411272', 'created_at': '2025-07-31T01:19:49', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\乡村振兴22.jpg', 'current_size': 4610118, 'current_modified': '2023-03-18T10:22:47.411272', 'exists': True}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 23
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件名: 乡村振兴22.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\乡村振兴22.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图服务可用: True
2025-07-31 01:25:19 - APIServer - INFO - 开始生成缩略图: C:\321\2\乡村振兴22.jpg, 尺寸: medium
2025-07-31 01:25:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-31 01:25:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\acd50e70a9e5d4bd1a461ae453e828fe_medium.jpg
2025-07-31 01:25:19 - APIServer - WARNING - 缩略图请求没有token，允许访问用于调试: 1212
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件ID: 1212, 文件信息: {'id': 1212, 'folder_id': 23, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-31T01:19:49', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 23
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-31 01:25:19 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-31 01:25:19 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图服务可用: True
2025-07-31 01:25:19 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: medium
2025-07-31 01:25:19 - APIServer - INFO - 缩略图路径: data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-31 01:25:19 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-31 01:25:19 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\medium\6c5c9f4cc7aa3b0e25274583445b7f4b_medium.jpg
2025-07-31 01:25:21 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-07-31 01:25:21 - APIServer - ERROR - 获取文件预览失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件ID: 1212, 文件信息: {'id': 1212, 'folder_id': 23, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-31T01:19:49', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 23
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图服务可用: True
2025-07-31 01:25:21 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: large
2025-07-31 01:25:21 - APIServer - INFO - 缩略图路径: data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:25:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件ID: 1212, 文件信息: {'id': 1212, 'folder_id': 23, 'filename': '系列图1.jpg', 'relative_path': '系列图1.jpg', 'file_size': 3511876, 'file_hash': None, 'mime_type': None, 'extension': '.jpg', 'file_type': {'is_image': True, 'is_video': False, 'is_document': False}, 'thumbnail': {'has_thumbnail': False, 'thumbnail_path': None}, 'image_info': {'width': None, 'height': None, 'format': None}, 'statistics': {'view_count': 0, 'download_count': 0}, 'timestamps': {'file_modified': '2022-09-25T09:54:08', 'created_at': '2025-07-31T01:19:49', 'last_accessed': None}, 'full_path': 'C:\\321\\2\\系列图1.jpg', 'current_size': 3511876, 'current_modified': '2022-09-25T09:54:08', 'exists': True}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹ID: 23
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹权限结果: {'success': True, 'permissions': {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 文件夹权限: {'network_access': {'internal': True, 'external': False}, 'file_operations': {'read': True, 'download': True, 'write': False, 'delete': False, 'replace': False, 'show_details': True}, 'download_limits': {'max_file_size_mb': 100, 'max_batch_files': 10}, 'encryption': {'encrypt_after_downloads': 5}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 网络服务可用: True
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - IP地址: 127.0.0.1
2025-07-31 01:25:21 - APIServer - INFO - 缩略图权限检查 - 访问检查结果: {'allowed': True, 'access_type': 'internal', 'is_internal': True, 'reason': '文件夹允许内网访问', 'ip_address': '127.0.0.1', 'folder_settings': {'allow_internal': True, 'allow_external': False}}
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件名: 系列图1.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件信息字段: ['id', 'folder_id', 'filename', 'relative_path', 'file_size', 'file_hash', 'mime_type', 'extension', 'file_type', 'thumbnail', 'image_info', 'statistics', 'timestamps', 'full_path', 'current_size', 'current_modified', 'exists']
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 文件扩展名: jpg, 允许的扩展名: ['jpg', 'jpeg', 'png', 'psd', 'tif', 'tiff', 'ai', 'eps', 'gif', 'bmp', 'webp', 'svg']
2025-07-31 01:25:21 - APIServer - INFO - 缩略图请求 - 完整路径: C:\321\2\系列图1.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图服务可用: True
2025-07-31 01:25:21 - APIServer - INFO - 开始生成缩略图: C:\321\2\系列图1.jpg, 尺寸: large
2025-07-31 01:25:21 - APIServer - INFO - 缩略图路径: data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:25:21 - APIServer - INFO - 转换为绝对路径: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:25:21 - APIServer - INFO - 缩略图生成成功，返回文件: C:\Users\<USER>\Desktop\Net\backend\data\thumbnails\large\6c5c9f4cc7aa3b0e25274583445b7f4b_large.jpg
2025-07-31 01:26:31 - APIServer - INFO - API服务器已停止
2025-07-31 09:27:37 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-31 09:27:37 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-31 09:27:37 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-31 01:27:37 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-31 01:27:38 - APIServer - INFO - 客户端连接: a1ctLy56EAAodLQnAAAB
2025-07-31 01:27:41 - APIServer - INFO - 客户端连接: r1d9Im9ZuHj9T59PAAAD
2025-07-31 01:27:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:27:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 21, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T00:56:25', 'updated_at': '2025-07-31T01:12:44', 'last_scanned': '2025-07-31T01:12:44.734318', 'file_count': 2}, {'id': 23, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:19:49', 'updated_at': '2025-07-31T01:19:53', 'last_scanned': '2025-07-31T01:19:53.357413', 'file_count': 2}]}
2025-07-31 01:27:42 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-31 01:27:42 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:27:42 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': [{'id': 21, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T00:56:25', 'updated_at': '2025-07-31T01:12:44', 'last_scanned': '2025-07-31T01:12:44.734318', 'file_count': 2}, {'id': 23, 'name': '2', 'path': 'C:\\321\\2', 'description': None, 'is_active': True, 'permissions': {'read': True, 'write': False, 'delete': False, 'upload': False, 'download': True}, 'network_access': {'internal': True, 'external': False}, 'display_settings': {'show_details': True, 'enable_thumbnail': True}, 'limits': {'max_file_size': 0, 'allowed_extensions': None}, 'statistics': {'file_count': 2, 'total_size': 8121994, 'access_count': 0, 'download_count': 0}, 'created_at': '2025-07-31T01:19:49', 'updated_at': '2025-07-31T01:19:53', 'last_scanned': '2025-07-31T01:19:53.357413', 'file_count': 2}]}
2025-07-31 01:27:42 - APIServer - INFO - 返回 2 个有权限的文件夹
2025-07-31 01:27:42 - APIServer - INFO - 客户端连接: sdUY3oIQVMh5xb5RAAAF
2025-07-31 01:27:42 - APIServer - INFO - 客户端连接: sKrel1z0vnjZOcAKAAAH
2025-07-31 01:28:19 - APIServer - INFO - 客户端断开连接: sdUY3oIQVMh5xb5RAAAF
2025-07-31 01:28:19 - APIServer - INFO - 客户端断开连接: sKrel1z0vnjZOcAKAAAH
2025-07-31 01:28:19 - APIServer - INFO - 客户端断开连接: a1ctLy56EAAodLQnAAAB
2025-07-31 01:28:19 - APIServer - INFO - 客户端断开连接: r1d9Im9ZuHj9T59PAAAD
2025-07-31 01:28:30 - APIServer - INFO - API服务器已停止
2025-07-31 09:31:50 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-31 09:31:50 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-31 09:31:50 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-31 01:31:51 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-31 01:33:31 - APIServer - INFO - API服务器已停止
2025-07-31 09:37:31 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-31 09:37:31 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-31 09:37:31 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-31 01:37:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-31 09:41:30 - APIServer - INFO - 网络访问控制服务初始化成功
2025-07-31 09:41:30 - APIServer - INFO - 用户行为监控服务初始化成功
2025-07-31 09:41:30 - APIServer - INFO - CORS已配置为允许局域网访问
2025-07-31 01:41:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-07-31 01:41:35 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:41:35 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-07-31 01:41:35 - APIServer - INFO - 返回 0 个有权限的文件夹
2025-07-31 01:41:36 - APIServer - INFO - 收到获取共享文件夹列表请求
2025-07-31 01:41:36 - APIServer - INFO - 文件服务返回结果: {'success': True, 'folders': []}
2025-07-31 01:41:36 - APIServer - INFO - 返回 0 个有权限的文件夹
2025-07-31 01:41:36 - APIServer - INFO - 客户端连接: 4-XshYWG6Np7ELUjAAAB
2025-07-31 01:41:36 - APIServer - INFO - 客户端连接: 3Jy1wtDZ38kcbnHrAAAD
2025-07-31 01:48:25 - APIServer - INFO - 客户端断开连接: 3Jy1wtDZ38kcbnHrAAAD
2025-07-31 01:48:25 - APIServer - INFO - 客户端断开连接: 4-XshYWG6Np7ELUjAAAB
2025-07-31 02:01:17 - APIServer - INFO - API服务器已停止
