#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库结构
"""

import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    
    # 数据库路径
    db_path = os.path.join('backend', 'data', 'file_share_system.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📊 数据库表结构:")
        print("=" * 50)
        
        for table_name in tables:
            table_name = table_name[0]
            print(f"\n📋 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for column in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = column
                pk_marker = " (PRIMARY KEY)" if primary_key else ""
                null_marker = " NOT NULL" if not_null else ""
                default_marker = f" DEFAULT {default_val}" if default_val else ""
                print(f"  - {col_name}: {col_type}{pk_marker}{null_marker}{default_marker}")
        
        # 特别检查共享文件夹表
        print("\n" + "=" * 50)
        print("🔍 共享文件夹数据:")
        
        try:
            cursor.execute("SELECT * FROM shared_folders LIMIT 5")
            folders = cursor.fetchall()
            
            if folders:
                # 获取列名
                cursor.execute("PRAGMA table_info(shared_folders)")
                columns = [col[1] for col in cursor.fetchall()]
                
                print(f"列名: {columns}")
                for folder in folders:
                    print(f"数据: {folder}")
            else:
                print("没有找到共享文件夹数据")
                
        except Exception as e:
            print(f"查询共享文件夹数据时出错: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时发生错误: {e}")

if __name__ == '__main__':
    check_database_structure()
