#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import os
from datetime import datetime, timedelta
from typing import Optional

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, server_instance):
        self.root = root
        self.server = server_instance
        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        self.start_status_update()

        # 自动启动服务器（延迟2秒）
        self.root.after(2000, self.auto_start_server)
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("企业级文件共享系统 - 服务端 v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 设置样式
        try:
            # 尝试使用ttkthemes提供的更好主题
            from ttkthemes import ThemedStyle
            style = ThemedStyle(self.root)

            # 优先选择的主题列表（按优先级排序）
            preferred_themes = ['vista', 'winnative', 'xpnative', 'adapta', 'arc', 'equilux', 'ubuntu']
            available_themes = style.theme_names()

            selected_theme = 'vista'  # 默认使用vista主题
            for theme in preferred_themes:
                if theme in available_themes:
                    selected_theme = theme
                    break

            style.theme_use(selected_theme)
            print(f"使用ttkthemes主题: {selected_theme}")

        except ImportError:
            # 如果ttkthemes不可用，使用标准ttk样式
            style = ttk.Style()
            available_themes = style.theme_names()

            # 优先选择的主题列表（按优先级排序）
            preferred_themes = ['vista', 'winnative', 'xpnative', 'default', 'clam']

            selected_theme = 'vista'
            for theme in preferred_themes:
                if theme in available_themes:
                    selected_theme = theme
                    break

            style.theme_use(selected_theme)
            print(f"使用标准主题: {selected_theme}")

        print(f"可用主题: {available_themes}")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_statusbar()
        
        # 创建主要内容区域
        self.create_main_content()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 服务器菜单
        server_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="服务器", menu=server_menu)
        server_menu.add_command(label="启动服务器", command=self.start_server)
        server_menu.add_command(label="停止服务器", command=self.stop_server)
        server_menu.add_command(label="重启服务器", command=self.restart_server)
        server_menu.add_separator()
        server_menu.add_command(label="服务器设置", command=self.open_server_settings)
        
        # 管理菜单
        manage_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="管理", menu=manage_menu)
        manage_menu.add_command(label="用户管理", command=self.open_user_management)
        manage_menu.add_command(label="文件管理", command=self.open_file_management)
        manage_menu.add_command(label="下载记录", command=self.open_download_records)
        manage_menu.add_command(label="权限管理", command=self.open_permission_management)
        manage_menu.add_command(label="活动日志", command=self.open_activity_log)
        manage_menu.add_separator()
        manage_menu.add_command(label="用户行为监控", command=self.open_user_behavior_monitoring)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据库备份", command=self.backup_database)
        tools_menu.add_command(label="数据库恢复", command=self.restore_database)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="系统诊断", command=self.system_diagnosis)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用手册", command=self.show_manual)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.main_frame)
        
        # 服务器控制按钮
        self.start_btn = ttk.Button(self.toolbar, text="启动服务器", 
                                   command=self.start_server)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(self.toolbar, text="停止服务器", 
                                  command=self.stop_server, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 管理按钮
        ttk.Button(self.toolbar, text="用户管理",
                  command=self.open_user_management).pack(side=tk.LEFT, padx=2)

        ttk.Button(self.toolbar, text="文件管理",
                  command=self.open_file_management).pack(side=tk.LEFT, padx=2)

        ttk.Button(self.toolbar, text="下载记录",
                  command=self.open_download_records).pack(side=tk.LEFT, padx=2)
        
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                            fill=tk.Y, padx=5)
        
        # 设置按钮
        ttk.Button(self.toolbar, text="设置", 
                  command=self.open_settings).pack(side=tk.LEFT, padx=2)
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = ttk.Frame(self.main_frame)
        
        # 服务器状态
        self.status_label = ttk.Label(self.statusbar, text="服务器已停止")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT, 
                                                              fill=tk.Y, padx=5)
        
        # 端口信息
        self.port_label = ttk.Label(self.statusbar, text="端口: --")
        self.port_label.pack(side=tk.LEFT, padx=5)

        ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT,
                                                              fill=tk.Y, padx=5)

        # 在线用户数 - 已移除
        # self.users_label = ttk.Label(self.statusbar, text="在线用户: 0")
        # self.users_label.pack(side=tk.LEFT, padx=5)

        # ttk.Separator(self.statusbar, orient=tk.VERTICAL).pack(side=tk.LEFT,
        #                                                       fill=tk.Y, padx=5)

        # 系统时间
        self.time_label = ttk.Label(self.statusbar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        # 更新端口信息
        self.update_port_info()
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 概览标签页
        self.create_overview_tab()
        
        # 实时监控标签页
        self.create_monitoring_tab()
        
        # 系统日志标签页
        self.create_log_tab()
        
        # 通知标签页
        self.create_notification_tab()
        
        # 滚动字幕标签页
        self.create_marquee_tab()
    
    def create_overview_tab(self):
        """创建概览标签页"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="系统概览")
        
        # 左侧统计信息
        left_frame = ttk.LabelFrame(overview_frame, text="系统统计")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 统计信息标签
        self.stats_labels = {}
        stats_items = [
            ("总用户数", "total_users"),
            # ("在线用户", "online_users"),  # 在线用户功能已被移除
            ("共享文件夹", "shared_folders"),
            ("总文件数", "total_files"),
            ("总文件大小", "total_size"),
            ("今日下载", "today_downloads"),
            ("今日上传", "today_uploads"),
            ("今日搜索", "today_searches")
        ]
        
        for i, (label, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            ttk.Label(left_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                        sticky=tk.W, padx=5, pady=2)
            self.stats_labels[key] = ttk.Label(left_frame, text="0")
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, 
                                       padx=5, pady=2)
        
        # 右侧快速操作
        right_frame = ttk.LabelFrame(overview_frame, text="快速操作")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=5, pady=5)
        
        quick_actions = [
            ("添加共享文件夹", self.add_shared_folder),
            ("创建用户", self.create_user),
            ("查看活动日志", self.open_activity_log),
            ("系统设置", self.open_settings),
            ("数据库备份", self.backup_database)
        ]
        
        for text, command in quick_actions:
            ttk.Button(right_frame, text=text, command=command, 
                      width=20).pack(pady=2, padx=5)
    
    def create_monitoring_tab(self):
        """创建监控标签页"""
        monitoring_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitoring_frame, text="实时监控")

        # 监控工具栏
        monitor_toolbar = ttk.Frame(monitoring_frame)
        monitor_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(monitor_toolbar, text="刷新数据", command=self.refresh_monitoring).pack(side=tk.LEFT, padx=(0, 5))

        # 系统状态框架
        status_frame = ttk.LabelFrame(monitoring_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        # 系统状态标签
        self.cpu_status_label = ttk.Label(status_frame, text="CPU使用率: --")
        self.cpu_status_label.pack(anchor=tk.W)

        self.memory_status_label = ttk.Label(status_frame, text="内存使用率: --")
        self.memory_status_label.pack(anchor=tk.W)

        self.server_status_label = ttk.Label(status_frame, text="服务器状态: --")
        self.server_status_label.pack(anchor=tk.W)

        # 在线用户列表 - 已移除
        # users_frame = ttk.LabelFrame(monitoring_frame, text="在线用户")
        # users_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 刷新按钮框架
        refresh_frame = ttk.Frame(monitoring_frame)
        refresh_frame.pack(fill=tk.X, padx=5, pady=5)

        # ttk.Button(refresh_frame, text="刷新在线用户",
        #           command=self.refresh_online_users).pack(side=tk.LEFT)

        ttk.Button(refresh_frame, text="刷新所有监控数据",
                  command=self.refresh_monitoring).pack(side=tk.LEFT, padx=(10, 0))

        # 创建树形视图 - 在线用户功能已被移除
        # columns = ("用户名", "IP地址", "登录时间", "活动状态", "在线时长")
        # self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings", height=8)

        # for col in columns:
        #     self.users_tree.heading(col, text=col)
        #     self.users_tree.column(col, width=120)

        # # 添加滚动条
        # users_scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL,
        #                                command=self.users_tree.yview)
        # self.users_tree.configure(yscrollcommand=users_scrollbar.set)

        # self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        # users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 开始监控更新
        self.start_monitoring_update()
    
    def create_log_tab(self):
        """创建日志标签页"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="系统日志")

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_toolbar, text="打开日志窗口", command=self.open_log_window).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="刷新", command=self.refresh_log_display).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_toolbar, text="清空显示", command=self.clear_log_display).pack(side=tk.LEFT, padx=(0, 5))

        # 日志级别过滤
        ttk.Label(log_toolbar, text="级别:").pack(side=tk.LEFT, padx=(10, 5))
        self.log_level_var = tk.StringVar(value="全部")
        log_level_combo = ttk.Combobox(log_toolbar, textvariable=self.log_level_var, width=10, state="readonly")
        log_level_combo['values'] = ("全部", "INFO", "WARNING", "ERROR", "CRITICAL")
        log_level_combo.pack(side=tk.LEFT, padx=(0, 5))
        log_level_combo.bind('<<ComboboxSelected>>', self.filter_log_display)

        # 日志显示区域
        log_display_frame = ttk.Frame(log_frame)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_display_frame, wrap=tk.WORD, state=tk.DISABLED, height=15)
        log_scrollbar = ttk.Scrollbar(log_display_frame, orient=tk.VERTICAL,
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置日志颜色
        self.log_text.tag_configure('INFO', foreground='blue')
        self.log_text.tag_configure('WARNING', foreground='orange')
        self.log_text.tag_configure('ERROR', foreground='red')
        self.log_text.tag_configure('CRITICAL', foreground='red', background='yellow')

        # 开始日志更新
        self.start_log_update()
    
    def create_notification_tab(self):
        """创建通知标签页"""
        notification_frame = ttk.Frame(self.notebook)
        self.notebook.add(notification_frame, text="系统通知")

        # 通知发送区域
        send_frame = ttk.LabelFrame(notification_frame, text="发送通知")
        send_frame.pack(fill=tk.X, padx=5, pady=5)

        # 通知类型选择
        type_frame = ttk.Frame(send_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(type_frame, text="通知类型:").pack(side=tk.LEFT)
        self.notification_type = ttk.Combobox(type_frame, values=['信息', '成功', '警告', '错误'], state='readonly')
        self.notification_type.set('信息')
        self.notification_type.pack(side=tk.LEFT, padx=5)

        # 接收范围选择
        ttk.Label(type_frame, text="接收范围:").pack(side=tk.LEFT, padx=(20, 5))
        self.notification_scope = ttk.Combobox(type_frame, values=['全体用户', '在线用户', '管理员'], state='readonly')
        self.notification_scope.set('全体用户')
        self.notification_scope.pack(side=tk.LEFT, padx=5)

        # 通知标题
        title_frame = ttk.Frame(send_frame)
        title_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(title_frame, text="通知标题:").pack(side=tk.LEFT)
        self.notification_title = ttk.Entry(title_frame, width=60)
        self.notification_title.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 通知内容
        content_frame = ttk.Frame(send_frame)
        content_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(content_frame, text="通知内容:").pack(anchor=tk.W)
        self.notification_content = tk.Text(content_frame, height=4, width=60)
        self.notification_content.pack(fill=tk.X, padx=(0, 5), pady=5)

        # 图片附件
        image_frame = ttk.Frame(send_frame)
        image_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(image_frame, text="图片附件:").pack(side=tk.LEFT)
        self.notification_image_path = tk.StringVar()
        self.image_path_entry = ttk.Entry(image_frame, textvariable=self.notification_image_path, width=40, state='readonly')
        self.image_path_entry.pack(side=tk.LEFT, padx=5)
        ttk.Button(image_frame, text="选择图片", command=self.select_notification_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(image_frame, text="清除图片", command=self.clear_notification_image).pack(side=tk.LEFT, padx=5)

        # 发送按钮
        button_frame = ttk.Frame(send_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(button_frame, text="发送通知", command=self.send_notification).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空内容", command=self.clear_notification_form).pack(side=tk.LEFT, padx=5)

        # 通知历史
        history_frame = ttk.LabelFrame(notification_frame, text="通知历史")
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview来显示通知历史
        columns = ('time', 'type', 'scope', 'title', 'content')
        self.notification_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=10)

        # 设置列标题
        self.notification_tree.heading('time', text='时间')
        self.notification_tree.heading('type', text='类型')
        self.notification_tree.heading('scope', text='范围')
        self.notification_tree.heading('title', text='标题')
        self.notification_tree.heading('content', text='内容')

        # 设置列宽
        self.notification_tree.column('time', width=120)
        self.notification_tree.column('type', width=60)
        self.notification_tree.column('scope', width=80)
        self.notification_tree.column('title', width=150)
        self.notification_tree.column('content', width=300)

        # 滚动条
        notification_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.notification_tree.yview)
        self.notification_tree.configure(yscrollcommand=notification_scrollbar.set)

        self.notification_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notification_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.toolbar.pack(fill=tk.X, pady=2)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.statusbar.pack(fill=tk.X, pady=2)
    
    def start_status_update(self):
        """启动状态更新线程"""
        def update_status():
            while True:
                try:
                    # 更新时间
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.time_label.config(text=current_time)

                    # 更新统计信息
                    self.update_statistics()

                    # 更新端口信息
                    self.update_port_info()

                    time.sleep(1)
                except:
                    break
        
        status_thread = threading.Thread(target=update_status, daemon=True)
        status_thread.start()
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            # 从数据库获取真实数据
            stats = self.get_real_statistics()

            for key, value in stats.items():
                if key in self.stats_labels:
                    self.stats_labels[key].config(text=value)
        except Exception as e:
            print(f"更新统计信息失败: {e}")

    def get_real_statistics(self):
        """获取真实的统计数据"""
        try:
            from datetime import datetime, date

            # 获取数据库管理器
            db_manager = self.server.db_manager if hasattr(self.server, 'db_manager') and self.server.db_manager else None

            if not db_manager:
                # 如果没有数据库管理器，返回默认值
                return {
                    "total_users": "0",
                    "online_users": "0",
                    "shared_folders": "0",
                    "total_files": "0",
                    "total_size": "0 MB",
                    "today_downloads": "0",
                    "today_uploads": "0",
                    "today_searches": "0"
                }

            with db_manager.get_session() as session:
                from models.user import User
                from models.file_share import SharedFolder, SharedFile
                from models.download_record import DownloadRecord
                from models.activity_log import ActivityLog

                # 总用户数
                total_users = session.query(User).count()

                # 在线用户数 - 功能已被移除
                # online_users_count = 0

                # 共享文件夹数
                shared_folders = session.query(SharedFolder).filter_by(is_active=True).count()

                # 总文件数和大小
                files_query = session.query(SharedFile)
                total_files = files_query.count()
                total_size_bytes = sum(f.file_size or 0 for f in files_query.all())
                total_size = self.format_file_size(total_size_bytes)

                # 今日统计
                today = date.today()
                today_start = datetime.combine(today, datetime.min.time()).isoformat()

                # 今日下载数
                today_downloads = session.query(DownloadRecord).filter(
                    DownloadRecord.downloaded_at >= today_start
                ).count()

                # 今日上传数（从活动日志获取）
                today_uploads = session.query(ActivityLog).filter(
                    ActivityLog.action == 'upload',
                    ActivityLog.created_at >= today_start
                ).count() if hasattr(ActivityLog, 'action') else 0

                # 今日搜索数（从活动日志获取）
                today_searches = session.query(ActivityLog).filter(
                    ActivityLog.action == 'search',
                    ActivityLog.created_at >= today_start
                ).count() if hasattr(ActivityLog, 'action') else 0

                return {
                    "total_users": str(total_users),
                    # "online_users": str(online_users_count),  # 在线用户功能已被移除
                    "shared_folders": str(shared_folders),
                    "total_files": str(total_files),
                    "total_size": total_size,
                    "today_downloads": str(today_downloads),
                    "today_uploads": str(today_uploads),
                    "today_searches": str(today_searches)
                }

        except Exception as e:
            print(f"获取统计数据失败: {e}")
            # 返回默认值
            return {
                "total_users": "0",
                "online_users": "0",
                "shared_folders": "0",
                "total_files": "0",
                "total_size": "0 MB",
                "today_downloads": "0",
                "today_uploads": "0",
                "today_searches": "0"
            }

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 MB"

        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def update_status(self, message: str, color: str = "black"):
        """更新状态栏"""
        self.status_label.config(text=message, foreground=color)
    
    # 菜单和按钮事件处理方法
    def start_server(self):
        """启动服务器"""
        self.server.start_server()
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
    
    def stop_server(self):
        """停止服务器"""
        self.server.stop_server()
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
    
    def restart_server(self):
        """重启服务器"""
        self.stop_server()
        time.sleep(1)
        self.start_server()

    def auto_start_server(self):
        """自动启动服务器"""
        try:
            if not self.server.running:
                self.start_server()
                print("✅ 服务器已自动启动")
        except Exception as e:
            print(f"❌ 自动启动服务器失败: {e}")
    
    # 功能实现方法
    def import_config(self):
        messagebox.showinfo("导入配置", "配置导入功能正在开发中...")

    def export_config(self):
        messagebox.showinfo("导出配置", "配置导出功能正在开发中...")

    def open_server_settings(self):
        messagebox.showinfo("服务器设置", "服务器设置功能正在开发中...")

    def open_user_management(self):
        """打开用户管理窗口"""
        try:
            from gui.user_management_window import UserManagementWindow
            user_mgmt = UserManagementWindow(self.root, self.server)
            user_mgmt.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开用户管理失败: {e}")

    def open_file_management(self):
        """打开文件管理窗口"""
        try:
            from gui.file_management_window import FileManagementWindow
            file_mgmt = FileManagementWindow(self.root, self.server)
            file_mgmt.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开文件管理失败: {e}")

    def open_download_records(self):
        """打开下载记录管理窗口"""
        try:
            from gui.download_records_window import DownloadRecordsWindow
            download_records = DownloadRecordsWindow(self.root, self.server)
            download_records.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开下载记录管理失败: {e}")

    def open_permission_management(self):
        messagebox.showinfo("权限管理", "权限管理功能正在开发中...")

    def open_activity_log(self):
        messagebox.showinfo("活动日志", "活动日志功能正在开发中...")

    def open_user_behavior_monitoring(self):
        """打开用户行为监控窗口"""
        try:
            from gui.user_behavior_window import UserBehaviorWindow
            behavior_window = UserBehaviorWindow(self.root, self.server)
            behavior_window.show()
        except Exception as e:
            messagebox.showerror("错误", f"打开用户行为监控失败: {e}")

    def open_monitoring(self):
        """打开监控窗口"""
        try:
            from gui.monitoring_window import MonitoringWindow
            if not hasattr(self, 'monitoring_window') or not self.monitoring_window.window:
                self.monitoring_window = MonitoringWindow(self.root, self.server)
            else:
                self.monitoring_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开监控窗口失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        try:
            from gui.settings_window import SettingsWindow
            if not hasattr(self, 'settings_window') or not self.settings_window.window:
                self.settings_window = SettingsWindow(self.root, self.server)
            else:
                self.settings_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开设置窗口失败: {e}")

    def open_log_window(self):
        """打开日志窗口"""
        try:
            from gui.log_window import LogWindow
            if not hasattr(self, 'log_window') or not self.log_window.window:
                self.log_window = LogWindow(self.root, self.server)
            else:
                self.log_window.window.lift()
        except Exception as e:
            messagebox.showerror("错误", f"打开日志窗口失败: {e}")

    def start_log_update(self):
        """开始日志更新"""
        self.update_log_display()
        # 每5秒更新一次日志
        self.root.after(5000, self.start_log_update)

    def update_log_display(self):
        """更新日志显示"""
        try:
            # 读取最新的日志文件
            log_dir = os.path.join(os.getcwd(), 'logs')
            if not os.path.exists(log_dir):
                return

            # 找到最新的日志文件
            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
            if not log_files:
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_dir, f)))
            log_file_path = os.path.join(log_dir, latest_log)

            # 读取日志内容（只读取最后100行）
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-100:] if len(lines) > 100 else lines

            # 更新显示
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)

            for line in recent_lines:
                line = line.strip()
                if not line:
                    continue

                # 解析日志级别
                level = 'INFO'
                if ' - ERROR - ' in line:
                    level = 'ERROR'
                elif ' - WARNING - ' in line:
                    level = 'WARNING'
                elif ' - CRITICAL - ' in line:
                    level = 'CRITICAL'

                # 应用过滤器
                filter_level = self.log_level_var.get()
                if filter_level != "全部" and level != filter_level:
                    continue

                # 插入文本并应用颜色
                self.log_text.insert(tk.END, line + '\n', level)

            self.log_text.config(state=tk.DISABLED)
            self.log_text.see(tk.END)  # 滚动到底部

        except Exception as e:
            print(f"更新日志显示失败: {e}")

    def refresh_log_display(self):
        """刷新日志显示"""
        self.update_log_display()

    def clear_log_display(self):
        """清空日志显示"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def filter_log_display(self, event=None):
        """过滤日志显示"""
        self.update_log_display()

    def start_monitoring_update(self):
        """开始监控更新"""
        self.update_monitoring_display()
        # 每10秒更新一次监控数据
        self.root.after(10000, self.start_monitoring_update)

    def update_monitoring_display(self):
        """更新监控显示"""
        try:
            # 更新系统状态
            import psutil

            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_status_label.config(text=f"CPU使用率: {cpu_percent:.1f}%")

            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_status_label.config(text=f"内存使用率: {memory.percent:.1f}%")

            # 服务器状态
            server_status = "运行中" if self.server.running else "已停止"
            self.server_status_label.config(text=f"服务器状态: {server_status}")

            # 更新在线用户 - 功能已被移除
            # self.update_online_users()

        except Exception as e:
            print(f"更新监控显示失败: {e}")

    def update_online_users(self):
        """更新在线用户列表 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def refresh_monitoring(self):
        """刷新监控数据"""
        self.update_monitoring_display()

    def refresh_online_users(self):
        """刷新在线用户数据 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def update_port_info(self):
        """更新端口信息显示"""
        try:
            if hasattr(self.server, 'settings'):
                api_port = self.server.settings.get('server.port', 8081)
                frontend_port = self.server.settings.get('server.frontend_port', 8082)

                # 检查API服务器是否运行以及实际端口
                if hasattr(self.server, 'api_server') and self.server.api_server:
                    if hasattr(self.server.api_server, 'running') and self.server.api_server.running:
                        self.port_label.config(
                            text=f"API端口: {api_port} | 前端端口: {frontend_port} (运行中)",
                            foreground="green"
                        )
                    else:
                        self.port_label.config(
                            text=f"API端口: {api_port} | 前端端口: {frontend_port} (已停止)",
                            foreground="red"
                        )
                else:
                    self.port_label.config(
                        text=f"API端口: {api_port} | 前端端口: {frontend_port} (未启动)",
                        foreground="orange"
                    )
            else:
                self.port_label.config(text="端口: --", foreground="gray")
        except Exception as e:
            print(f"更新端口信息失败: {e}")
            self.port_label.config(text="端口: --", foreground="gray")

    def backup_database(self):
        """备份数据库"""
        try:
            if self.server.db_manager:
                import os
                from datetime import datetime

                backup_dir = "./backup"
                os.makedirs(backup_dir, exist_ok=True)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"{backup_dir}/backup_{timestamp}.sql"

                self.server.db_manager.backup_database(backup_file)
                messagebox.showinfo("成功", f"数据库备份成功: {backup_file}")
            else:
                messagebox.showerror("错误", "数据库管理器不可用")
        except Exception as e:
            messagebox.showerror("错误", f"数据库备份失败: {e}")

    def restore_database(self):
        """恢复数据库"""
        try:
            from tkinter import filedialog

            if self.server.db_manager:
                backup_file = filedialog.askopenfilename(
                    title="选择备份文件",
                    filetypes=[("SQL文件", "*.sql"), ("所有文件", "*.*")]
                )

                if backup_file:
                    if messagebox.askyesno("确认", "确定要恢复数据库吗？\n此操作将覆盖现有数据！"):
                        self.server.db_manager.restore_database(backup_file)
                        messagebox.showinfo("成功", "数据库恢复成功")
            else:
                messagebox.showerror("错误", "数据库管理器不可用")
        except Exception as e:
            messagebox.showerror("错误", f"数据库恢复失败: {e}")

    def clear_cache(self):
        """清理缓存"""
        try:
            # 清理缩略图缓存
            thumbnail_service = self.server.services.get('thumbnail')
            if thumbnail_service:
                thumbnail_service.cleanup_orphaned_thumbnails()

            # 清理下载包缓存
            encryption_service = self.server.services.get('encryption')
            if encryption_service:
                encryption_service.cleanup_expired_packages()

            messagebox.showinfo("成功", "缓存清理完成")
        except Exception as e:
            messagebox.showerror("错误", f"清理缓存失败: {e}")

    def system_diagnosis(self):
        """系统诊断"""
        try:
            diagnosis_info = []

            # 检查服务状态
            diagnosis_info.append("=== 服务状态 ===")
            for service_name, service in self.server.services.items():
                status = "运行中" if service else "未运行"
                diagnosis_info.append(f"{service_name}: {status}")

            # 检查数据库连接
            diagnosis_info.append("\n=== 数据库状态 ===")
            if self.server.db_manager:
                try:
                    self.server.db_manager.test_connection()
                    diagnosis_info.append("数据库连接: 正常")
                except:
                    diagnosis_info.append("数据库连接: 异常")
            else:
                diagnosis_info.append("数据库连接: 不可用")

            # 显示诊断结果
            diagnosis_text = "\n".join(diagnosis_info)

            # 创建诊断窗口
            diag_window = tk.Toplevel(self.root)
            diag_window.title("系统诊断")
            diag_window.geometry("500x400")

            text_widget = tk.Text(diag_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, diagnosis_text)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"系统诊断失败: {e}")

    def show_manual(self):
        """显示使用手册"""
        try:
            import webbrowser
            import os

            manual_file = os.path.join(os.path.dirname(__file__), "..", "使用指南.md")
            if os.path.exists(manual_file):
                webbrowser.open(f"file://{os.path.abspath(manual_file)}")
            else:
                messagebox.showinfo("使用手册", "使用手册文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开使用手册失败: {e}")

    def show_about(self):
        """显示关于信息"""
        about_text = """企业级文件共享系统 v1.0.0

功能特性:
• 权限管理和用户控制
• 双搜索引擎（文本+图像）
• 缩略图支持
• 加密下载保护
• 实时监控和日志
• 滚动通知系统

技术架构:
• Python 3.7+
• SQLite 数据库
• Flask API服务
• tkinter GUI界面

开发团队: 系统开发团队
版权所有 © 2024"""

        messagebox.showinfo("关于", about_text)

    def add_shared_folder(self):
        """添加共享文件夹"""
        try:
            from tkinter import filedialog

            folder_path = filedialog.askdirectory(title="选择要共享的文件夹")
            if folder_path:
                # 这里应该调用文件服务添加共享文件夹
                file_service = self.server.services.get('file')
                if file_service:
                    folder_name = os.path.basename(folder_path)
                    result = file_service.create_shared_folder(folder_name, folder_path)

                    if result.get('success', False):
                        messagebox.showinfo("成功", f"共享文件夹添加成功: {folder_name}")
                    else:
                        messagebox.showerror("错误", f"添加共享文件夹失败: {result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "文件服务不可用")
        except Exception as e:
            messagebox.showerror("错误", f"添加共享文件夹失败: {e}")

    def create_user(self):
        """创建用户"""
        self.open_user_management()

    def select_notification_image(self):
        """选择通知图片"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择通知图片",
                filetypes=[
                    ("图片文件", "*.png *.jpg *.jpeg *.gif *.bmp *.webp"),
                    ("PNG文件", "*.png"),
                    ("JPEG文件", "*.jpg *.jpeg"),
                    ("所有文件", "*.*")
                ]
            )
            if file_path:
                self.notification_image_path.set(file_path)
        except Exception as e:
            messagebox.showerror("错误", f"选择图片失败: {e}")

    def clear_notification_image(self):
        """清除通知图片"""
        self.notification_image_path.set("")

    def clear_notification_form(self):
        """清空通知表单"""
        self.notification_title.delete(0, tk.END)
        self.notification_content.delete(1.0, tk.END)
        self.notification_image_path.set("")
        self.notification_type.set('信息')
        self.notification_scope.set('全体用户')

    def send_notification(self):
        """发送通知"""
        try:
            # 获取表单数据
            title = self.notification_title.get().strip()
            content = self.notification_content.get(1.0, tk.END).strip()
            image_path = self.notification_image_path.get().strip()
            notification_type = self.notification_type.get()
            scope = self.notification_scope.get()

            # 验证必填字段
            if not title and not content:
                messagebox.showwarning("警告", "通知标题和内容不能都为空")
                return

            # 如果没有标题，使用内容的前20个字符作为标题
            if not title:
                title = content[:20] + "..." if len(content) > 20 else content

            # 如果没有内容，使用标题作为内容
            if not content:
                content = title

            # 类型映射
            type_map = {'信息': 'info', '成功': 'success', '警告': 'warning', '错误': 'error'}
            notification_type_code = type_map.get(notification_type, 'info')

            # 处理图片
            image_data = None
            image_filename = None
            if image_path and os.path.exists(image_path):
                try:
                    import base64
                    with open(image_path, 'rb') as f:
                        image_data = base64.b64encode(f.read()).decode('utf-8')
                    image_filename = os.path.basename(image_path)
                except Exception as e:
                    messagebox.showwarning("警告", f"读取图片失败: {e}")

            # 构建通知数据
            notification_data = {
                'title': title,
                'content': content,
                'type': notification_type_code,
                'scope': scope,
                'image_data': image_data,
                'image_filename': image_filename,
                'timestamp': datetime.now().isoformat()
            }

            # 添加到通知历史
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            self.notification_tree.insert('', 0, values=(
                timestamp,
                notification_type,
                scope,
                title,
                content[:50] + "..." if len(content) > 50 else content
            ))

            # 如果有API服务器，广播通知
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.broadcast_enhanced_notification(notification_data)

            # 清空表单
            self.clear_notification_form()

            messagebox.showinfo("成功", f"通知发送成功！\n范围: {scope}\n类型: {notification_type}")

        except Exception as e:
            messagebox.showerror("错误", f"发送通知失败: {e}")

    def create_marquee_tab(self):
        """创建滚动字幕标签页"""
        marquee_frame = ttk.Frame(self.notebook)
        self.notebook.add(marquee_frame, text="滚动字幕")
        
        # 字幕控制
        control_frame = ttk.LabelFrame(marquee_frame, text="字幕设置")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 字幕内容
        ttk.Label(control_frame, text="字幕内容:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.marquee_text = tk.Text(control_frame, width=60, height=3)
        self.marquee_text.grid(row=0, column=1, columnspan=2, padx=5, pady=5)
        
        # 主题选择
        ttk.Label(control_frame, text="主题样式:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.marquee_theme = ttk.Combobox(control_frame, values=['默认', '信息', '成功', '警告', '错误'], state='readonly')
        self.marquee_theme.set('默认')
        self.marquee_theme.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # 启用/隐藏选项
        self.marquee_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="启用字幕", variable=self.marquee_enabled).grid(row=1, column=2, padx=5, pady=5)

        # 字体大小设置
        ttk.Label(control_frame, text="字体大小:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.font_size_var = tk.StringVar(value="14")
        font_size_frame = ttk.Frame(control_frame)
        font_size_frame.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.font_size_scale = ttk.Scale(font_size_frame, from_=10, to=24, orient=tk.HORIZONTAL,
                                        variable=self.font_size_var, length=120)
        self.font_size_scale.pack(side=tk.LEFT)
        self.font_size_label = ttk.Label(font_size_frame, text="14px", width=6)
        self.font_size_label.pack(side=tk.LEFT, padx=(5, 0))
        self.font_size_scale.configure(command=self.update_font_size_label)

        # 滚动速度设置
        ttk.Label(control_frame, text="滚动速度:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.scroll_speed_var = tk.StringVar(value="15")
        speed_frame = ttk.Frame(control_frame)
        speed_frame.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
        self.scroll_speed_scale = ttk.Scale(speed_frame, from_=5, to=30, orient=tk.HORIZONTAL,
                                           variable=self.scroll_speed_var, length=120)
        self.scroll_speed_scale.pack(side=tk.LEFT)
        self.speed_label = ttk.Label(speed_frame, text="15s", width=6)
        self.speed_label.pack(side=tk.LEFT, padx=(5, 0))
        self.scroll_speed_scale.configure(command=self.update_speed_label)
        
        # 按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=10)

        ttk.Button(button_frame, text="保存设置", command=self.update_marquee).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="应用字幕", command=self.apply_marquee).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="加载当前", command=self.load_current_marquee).pack(side=tk.LEFT, padx=5)
        
        # 预设消息
        preset_frame = ttk.LabelFrame(marquee_frame, text="预设消息")
        preset_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 预设消息管理区域
        preset_control_frame = ttk.Frame(preset_frame)
        preset_control_frame.pack(fill=tk.X, padx=5, pady=5)

        # 新增预设输入
        ttk.Label(preset_control_frame, text="新增预设:").pack(side=tk.LEFT, padx=(0, 5))
        self.new_preset_entry = ttk.Entry(preset_control_frame, width=40)
        self.new_preset_entry.pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_control_frame, text="添加", command=self.add_preset_message).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(preset_control_frame, text="删除选中", command=self.delete_selected_preset).pack(side=tk.LEFT)

        # 预设消息列表
        preset_list_frame = ttk.Frame(preset_frame)
        preset_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview来显示预设消息
        columns = ('message',)
        self.preset_tree = ttk.Treeview(preset_list_frame, columns=columns, show='headings', height=8)
        self.preset_tree.heading('message', text='预设消息内容')
        self.preset_tree.column('message', width=500)

        # 滚动条
        preset_scrollbar = ttk.Scrollbar(preset_list_frame, orient=tk.VERTICAL, command=self.preset_tree.yview)
        self.preset_tree.configure(yscrollcommand=preset_scrollbar.set)

        self.preset_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preset_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 双击事件
        self.preset_tree.bind('<Double-1>', self.on_preset_double_click)

        # 初始化预设消息
        self.preset_messages = []
        self.load_preset_messages()
        
        # 加载当前字幕设置
        self.load_current_marquee()

    def update_font_size_label(self, value):
        """更新字体大小标签"""
        size = int(float(value))
        self.font_size_label.config(text=f"{size}px")

    def update_speed_label(self, value):
        """更新滚动速度标签"""
        speed = int(float(value))
        self.speed_label.config(text=f"{speed}s")
    
    def update_marquee(self):
        """更新滚动字幕"""
        try:
            message = self.marquee_text.get(1.0, tk.END).strip()
            if not message:
                messagebox.showwarning("警告", "字幕内容不能为空")
                return

            theme_map = {'默认': '', '信息': 'info', '成功': 'success', '警告': 'warning', '错误': 'error'}
            theme = theme_map.get(self.marquee_theme.get(), '')
            enabled = self.marquee_enabled.get()
            font_size = int(float(self.font_size_var.get()))
            scroll_speed = int(float(self.scroll_speed_var.get()))

            # 更新配置
            if hasattr(self.server, 'settings'):
                self.server.settings.settings.setdefault('marquee', {})
                self.server.settings.settings['marquee'].update({
                    'message': message,
                    'theme': theme,
                    'enabled': enabled,
                    'hidden': not enabled,
                    'font_size': font_size,
                    'scroll_speed': scroll_speed
                })
                self.server.settings.save_settings()

            # 如果有API服务器，广播更新
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.socketio.emit('marquee_update', {
                    'message': message,
                    'theme': theme,
                    'enabled': enabled,
                    'hidden': not enabled,
                    'font_size': font_size,
                    'scroll_speed': scroll_speed
                })

            messagebox.showinfo("成功", "滚动字幕更新成功")

        except Exception as e:
            messagebox.showerror("错误", f"更新滚动字幕失败: {e}")

    def apply_marquee(self):
        """应用字幕设置（不保存到配置文件）"""
        try:
            message = self.marquee_text.get(1.0, tk.END).strip()
            if not message:
                messagebox.showwarning("警告", "字幕内容不能为空")
                return

            theme_map = {'默认': '', '信息': 'info', '成功': 'success', '警告': 'warning', '错误': 'error'}
            theme = theme_map.get(self.marquee_theme.get(), '')
            enabled = self.marquee_enabled.get()
            font_size = int(float(self.font_size_var.get()))
            scroll_speed = int(float(self.scroll_speed_var.get()))

            # 只广播更新，不保存到配置文件
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.socketio.emit('marquee_update', {
                    'message': message,
                    'theme': theme,
                    'enabled': enabled,
                    'hidden': not enabled,
                    'font_size': font_size,
                    'scroll_speed': scroll_speed
                })

            messagebox.showinfo("成功", "字幕已应用（未保存到配置）")

        except Exception as e:
            messagebox.showerror("错误", f"应用字幕失败: {e}")

    def load_current_marquee(self):
        """加载当前字幕设置"""
        try:
            if hasattr(self.server, 'settings'):
                marquee_config = self.server.settings.settings.get('marquee', {})

                # 设置文本
                message = marquee_config.get('message', '欢迎使用企业级文件共享系统！')
                self.marquee_text.delete(1.0, tk.END)
                self.marquee_text.insert(1.0, message)

                # 设置主题
                theme = marquee_config.get('theme', '')
                theme_map = {'': '默认', 'info': '信息', 'success': '成功', 'warning': '警告', 'error': '错误'}
                self.marquee_theme.set(theme_map.get(theme, '默认'))

                # 设置启用状态
                enabled = marquee_config.get('enabled', True)
                self.marquee_enabled.set(enabled)

                # 设置字体大小
                font_size = marquee_config.get('font_size', 14)
                self.font_size_var.set(str(font_size))
                self.font_size_label.config(text=f"{font_size}px")

                # 设置滚动速度
                scroll_speed = marquee_config.get('scroll_speed', 15)
                self.scroll_speed_var.set(str(scroll_speed))
                self.speed_label.config(text=f"{scroll_speed}s")

        except Exception as e:
            messagebox.showerror("错误", f"加载字幕设置失败: {e}")
    
    def hide_marquee(self):
        """隐藏滚动字幕"""
        try:
            # 更新配置
            if hasattr(self.server, 'settings'):
                self.server.settings.settings.setdefault('marquee', {})
                self.server.settings.settings['marquee']['hidden'] = True
                self.server.settings.save_settings()
            
            # 广播隐藏
            if hasattr(self.server, 'api_server') and self.server.api_server:
                self.server.api_server.socketio.emit('marquee_update', {
                    'hidden': True
                })
            
            messagebox.showinfo("成功", "滚动字幕已隐藏")
            
        except Exception as e:
            messagebox.showerror("错误", f"隐藏字幕失败: {e}")
    
    def load_preset_messages(self):
        """加载预设消息"""
        try:
            if hasattr(self.server, 'settings'):
                preset_messages = self.server.settings.settings.get('marquee_presets', [
                    "欢迎使用企业级文件共享系统！",
                    "系统维护通知：今晚22:00-24:00进行系统升级维护",
                    "新功能上线：支持PSD、AI等专业格式预览",
                    "重要提醒：请及时清理个人下载记录",
                    "系统公告：为保障系统安全，请勿下载可疑文件"
                ])
                self.preset_messages = preset_messages
            else:
                self.preset_messages = [
                    "欢迎使用企业级文件共享系统！",
                    "系统维护通知：今晚22:00-24:00进行系统升级维护",
                    "新功能上线：支持PSD、AI等专业格式预览",
                    "重要提醒：请及时清理个人下载记录",
                    "系统公告：为保障系统安全，请勿下载可疑文件"
                ]

            self.refresh_preset_list()

        except Exception as e:
            messagebox.showerror("错误", f"加载预设消息失败: {e}")

    def refresh_preset_list(self):
        """刷新预设消息列表"""
        # 清空现有项目
        for item in self.preset_tree.get_children():
            self.preset_tree.delete(item)

        # 添加预设消息
        for i, message in enumerate(self.preset_messages):
            self.preset_tree.insert('', 'end', values=(message,))

    def add_preset_message(self):
        """添加预设消息"""
        message = self.new_preset_entry.get().strip()
        if not message:
            messagebox.showwarning("警告", "预设消息内容不能为空")
            return

        if message in self.preset_messages:
            messagebox.showwarning("警告", "该预设消息已存在")
            return

        try:
            self.preset_messages.append(message)
            self.save_preset_messages()
            self.refresh_preset_list()
            self.new_preset_entry.delete(0, tk.END)
            messagebox.showinfo("成功", "预设消息添加成功")

        except Exception as e:
            messagebox.showerror("错误", f"添加预设消息失败: {e}")

    def delete_selected_preset(self):
        """删除选中的预设消息"""
        selection = self.preset_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的预设消息")
            return

        try:
            for item in selection:
                values = self.preset_tree.item(item)['values']
                if values:
                    message = values[0]
                    if message in self.preset_messages:
                        self.preset_messages.remove(message)

            self.save_preset_messages()
            self.refresh_preset_list()
            messagebox.showinfo("成功", "预设消息删除成功")

        except Exception as e:
            messagebox.showerror("错误", f"删除预设消息失败: {e}")

    def on_preset_double_click(self, event):
        """预设消息双击事件"""
        selection = self.preset_tree.selection()
        if selection:
            item = selection[0]
            values = self.preset_tree.item(item)['values']
            if values:
                message = values[0]
                self.set_preset_message(message)

    def save_preset_messages(self):
        """保存预设消息到配置"""
        try:
            if hasattr(self.server, 'settings'):
                self.server.settings.settings['marquee_presets'] = self.preset_messages
                self.server.settings.save_settings()
        except Exception as e:
            raise Exception(f"保存预设消息失败: {e}")

    def set_preset_message(self, message):
        """设置预设消息"""
        self.marquee_text.delete(1.0, tk.END)
        self.marquee_text.insert(1.0, message)
