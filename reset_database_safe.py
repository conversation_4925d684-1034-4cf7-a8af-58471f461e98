#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全重置数据库脚本
"""

import os
import sys
import time
import sqlite3
import shutil
import hashlib
import secrets
from pathlib import Path
from datetime import datetime

def safe_reset_database():
    """安全重置数据库"""
    print("🔄 开始安全重置数据库...")
    
    # 数据库路径
    db_path = Path("backend/data/file_share_system.db")
    backup_path = Path("backend/data/file_share_system_backup.db")
    
    print(f"📁 数据库路径: {db_path}")
    print(f"📁 备份路径: {backup_path}")
    
    # 检查数据库是否存在
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        # 步骤1：创建备份
        print("📦 创建数据库备份...")
        if backup_path.exists():
            backup_path.unlink()
        shutil.copy2(db_path, backup_path)
        print("✅ 备份创建成功")
        
        # 步骤2：尝试获取数据库独占锁
        print("🔒 尝试获取数据库独占锁...")
        
        # 使用临时连接测试是否可以获取独占锁
        temp_conn = None
        try:
            temp_conn = sqlite3.connect(str(db_path), timeout=5)
            temp_conn.execute("BEGIN EXCLUSIVE")
            print("✅ 获取独占锁成功")
            
            # 关闭连接释放锁
            temp_conn.rollback()
            temp_conn.close()
            temp_conn = None
            
        except sqlite3.OperationalError as e:
            if temp_conn:
                temp_conn.close()
            print(f"❌ 无法获取独占锁: {e}")
            print("💡 请先停止服务器，然后重试")
            return False
        
        # 步骤3：等待一段时间确保连接释放
        print("⏳ 等待连接释放...")
        time.sleep(2)
        
        # 步骤4：删除原数据库文件
        print("🗑️ 删除原数据库文件...")
        try:
            db_path.unlink()
            print("✅ 原数据库文件删除成功")
        except PermissionError:
            # 如果无法删除，尝试重命名
            old_path = db_path.with_suffix('.db.old')
            if old_path.exists():
                old_path.unlink()
            db_path.rename(old_path)
            print(f"⚠️ 无法删除，已重命名为: {old_path}")
        
        # 步骤5：重新创建数据库
        print("🔨 重新创建数据库...")
        if create_new_database(str(db_path)):
            print("✅ 数据库重置成功！")
            print("\n📋 默认管理员账户:")
            print("   用户名: admin")
            print("   密码: admin123")
            print("\n💡 请重新启动服务器")
            return True
        else:
            print("❌ 重新创建数据库失败")
            # 恢复备份
            if backup_path.exists():
                shutil.copy2(backup_path, db_path)
                print("🔄 已恢复备份数据库")
            return False
            
    except Exception as e:
        print(f"❌ 重置数据库时发生错误: {e}")
        
        # 尝试恢复备份
        if backup_path.exists() and not db_path.exists():
            try:
                shutil.copy2(backup_path, db_path)
                print("🔄 已恢复备份数据库")
            except Exception as restore_error:
                print(f"❌ 恢复备份失败: {restore_error}")
        
        return False
    
    finally:
        # 清理备份文件（可选）
        # if backup_path.exists():
        #     backup_path.unlink()
        pass

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    
    db_path = Path("backend/data/file_share_system.db")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(str(db_path), timeout=1)
        cursor = conn.cursor()
        
        # 检查是否可以访问
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        
        print(f"✅ 数据库可访问，包含 {table_count} 个表")
        
        # 检查用户表
        try:
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"👥 用户数量: {user_count}")
        except:
            print("❌ 用户表不存在或无法访问")
        
        conn.close()
        
    except sqlite3.OperationalError as e:
        print(f"❌ 数据库被锁定或无法访问: {e}")
    except Exception as e:
        print(f"❌ 检查数据库时发生错误: {e}")

def create_new_database(db_path):
    """创建新的数据库"""
    try:
        print("📊 使用DatabaseManager创建数据库...")

        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

        # 使用DatabaseManager创建数据库
        backend_path = os.path.join(os.getcwd(), 'backend')
        if backend_path not in sys.path:
            sys.path.insert(0, backend_path)
        from config.database import DatabaseManager

        # 创建数据库管理器
        config = {'database_path': db_path}
        db_manager = DatabaseManager(config)

        print("✅ 数据库表创建成功")

        # 创建默认管理员用户
        print("👤 创建默认管理员用户...")
        create_default_admin(db_path)

        # 关闭数据库连接
        db_manager.close()

        return True

    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_default_admin(db_path):
    """创建默认管理员用户"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 生成密码哈希
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256(('admin123' + salt).encode()).hexdigest()

        # 插入管理员用户
        cursor.execute("""
            INSERT OR REPLACE INTO users
            (username, password_hash, salt, email, full_name, user_group,
             is_active, is_admin, login_count, failed_login_attempts,
             download_count, upload_count, search_count, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'admin', password_hash, salt, '<EMAIL>',
            '系统管理员', 'admin', 1, 1, 0, 0, 0, 0, 0,
            datetime.now(), datetime.now()
        ))

        conn.commit()
        conn.close()

        print("✅ 默认管理员用户创建成功")

    except Exception as e:
        print(f"❌ 创建默认用户失败: {e}")
        raise

if __name__ == "__main__":
    print("🚀 数据库重置工具")
    print("=" * 50)
    
    # 检查当前状态
    check_database_status()
    
    print("\n" + "=" * 50)
    
    # 确认操作
    confirm = input("⚠️ 确定要重置数据库吗？这将删除所有数据！(输入 'RESET' 确认): ")

    if confirm == "RESET":
        success = safe_reset_database()
        if success:
            print("\n🎉 数据库重置完成！")
        else:
            print("\n💥 数据库重置失败！")
    else:
        print("❌ 操作已取消")
