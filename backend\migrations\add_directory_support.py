#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移：添加目录支持
"""

import sqlite3
import os
from datetime import datetime

def migrate_database(db_path: str):
    """执行数据库迁移"""
    print(f"开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. 创建 shared_directories 表
        print("创建 shared_directories 表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shared_directories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                folder_id INTEGER NOT NULL,
                parent_id INTEGER,
                name VARCHAR(255) NOT NULL,
                relative_path TEXT NOT NULL,
                file_count INTEGER DEFAULT 0,
                total_file_count INTEGER DEFAULT 0,
                directory_count INTEGER DEFAULT 0,
                total_size BIGINT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_scanned DATETIME,
                FOREIGN KEY (folder_id) REFERENCES shared_folders (id) ON DELETE CASCADE,
                FOREIGN KEY (parent_id) REFERENCES shared_directories (id) ON DELETE CASCADE
            )
        ''')
        
        # 2. 检查 shared_files 表是否已有 directory_id 字段
        cursor.execute("PRAGMA table_info(shared_files)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'directory_id' not in columns:
            print("添加 directory_id 字段到 shared_files 表...")
            cursor.execute('''
                ALTER TABLE shared_files 
                ADD COLUMN directory_id INTEGER 
                REFERENCES shared_directories(id) ON DELETE CASCADE
            ''')
        else:
            print("directory_id 字段已存在，跳过添加")
        
        # 3. 创建索引
        print("创建索引...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_shared_directories_folder_id 
            ON shared_directories(folder_id)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_shared_directories_parent_id 
            ON shared_directories(parent_id)
        ''')
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_shared_files_directory_id 
            ON shared_files(directory_id)
        ''')
        
        # 4. 创建触发器来维护 updated_at 字段
        print("创建触发器...")
        cursor.execute('''
            CREATE TRIGGER IF NOT EXISTS update_shared_directories_updated_at
            AFTER UPDATE ON shared_directories
            FOR EACH ROW
            BEGIN
                UPDATE shared_directories 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = NEW.id;
            END
        ''')
        
        conn.commit()
        print("数据库迁移完成！")
        
        # 5. 验证迁移结果
        print("\n验证迁移结果:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='shared_directories'")
        if cursor.fetchone():
            print("✅ shared_directories 表创建成功")
        else:
            print("❌ shared_directories 表创建失败")
        
        cursor.execute("PRAGMA table_info(shared_files)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'directory_id' in columns:
            print("✅ shared_files.directory_id 字段添加成功")
        else:
            print("❌ shared_files.directory_id 字段添加失败")
        
        # 显示表结构
        print("\n当前表结构:")
        cursor.execute("PRAGMA table_info(shared_directories)")
        print("shared_directories 表字段:")
        for column in cursor.fetchall():
            print(f"  - {column[1]} ({column[2]})")
        
        cursor.execute("PRAGMA table_info(shared_files)")
        print("\nshared_files 表字段:")
        for column in cursor.fetchall():
            print(f"  - {column[1]} ({column[2]})")
            
    except Exception as e:
        print(f"迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def main():
    """主函数"""
    # 数据库路径 - 使用绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    db_path = os.path.join(project_root, "data", "file_share_system.db")

    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print(f"数据库路径: {db_path}")

    if not os.path.exists(os.path.dirname(db_path)):
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        print(f"创建数据目录: {os.path.dirname(db_path)}")

    migrate_database(db_path)

if __name__ == "__main__":
    main()
