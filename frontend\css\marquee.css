/* 滚动字幕样式 */
.marquee-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 0;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 100;
}

.marquee-container.hidden {
    display: none;
}

.marquee-content {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding-right: 40px; /* 为关闭按钮留空间 */
    height: 32px;
    display: flex;
    align-items: center;
}

.marquee-text {
    display: inline-block;
    white-space: nowrap;
    font-size: var(--marquee-font-size, 14px);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: marqueeMove var(--marquee-duration, 15s) linear infinite;
    /* 文字从右侧开始滚动 */
    min-width: max-content;
}

.marquee-text:hover {
    animation-play-state: paused;
}

.marquee-close {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.marquee-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

/* 滚动动画 - 从右边向左边滚动 */
@keyframes marqueeMove {
    0% {
        transform: translateX(100vw);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* 备用滚动动画 */
@keyframes marqueeScroll {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* 不同主题样式 */
.marquee-container.theme-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.marquee-container.theme-success {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.marquee-container.theme-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.marquee-container.theme-error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .marquee-text {
        font-size: 13px;
        animation-duration: 15s;
    }
    
    .marquee-close {
        width: 20px;
        height: 20px;
        font-size: 10px;
        right: 8px;
    }
}

/* 暂停动画的类 */
.marquee-paused .marquee-text {
    animation-play-state: paused;
}

/* 淡入淡出效果 */
.marquee-container {
    transition: opacity 0.3s ease;
}

.marquee-container.fade-out {
    opacity: 0;
}

/* 带图标的字幕样式 */
.marquee-text-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

.marquee-text-with-icon i {
    font-size: 16px;
    flex-shrink: 0;
} 