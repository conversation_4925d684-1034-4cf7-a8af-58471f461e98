#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缩略图生成服务
"""

import os
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import threading
import queue
import time

from utils.logger import setup_logger

# 可选导入图像处理库
try:
    from PIL import Image, ImageOps
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

try:
    import cv2
    import numpy as np
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

# 专业图形文件处理库
try:
    from psd_tools import PSDImage
    HAS_PSD_TOOLS = True
except ImportError:
    HAS_PSD_TOOLS = False

try:
    from wand.image import Image as WandImage
    from wand.exceptions import WandException
    HAS_WAND = True
except ImportError:
    HAS_WAND = False

class ThumbnailService:
    """缩略图生成服务"""
    
    def __init__(self, thumbnail_dir: str = "./data/thumbnails"):
        self.thumbnail_dir = Path(thumbnail_dir)
        self.thumbnail_dir.mkdir(parents=True, exist_ok=True)
        self.logger = setup_logger("ThumbnailService")
        
        # 支持的图像格式
        self.supported_formats = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'
        }

        # 专业图形文件格式
        self.professional_formats = {
            '.psd',  # Photoshop文档
            '.ai',   # Adobe Illustrator
            '.eps'   # Encapsulated PostScript
        }

        # 所有支持的格式
        self.all_supported_formats = self.supported_formats | self.professional_formats
        
        # 缩略图尺寸配置
        self.thumbnail_sizes = {
            'small': (150, 150),
            'medium': (300, 300),
            'large': (600, 600),
            'xlarge': (1200, 1200)
        }

        # 设置 Ghostscript 路径
        self._setup_ghostscript_path()

        # 任务队列
        self.task_queue = queue.Queue()
        self.worker_threads = []
        self.running = False

        # 启动工作线程
        self.start_workers()

    def _setup_ghostscript_path(self):
        """设置 Ghostscript 路径"""
        try:
            import glob

            # 查找 Ghostscript 安装路径
            gs_paths = [
                r"C:\Program Files\gs\gs*\bin",
                r"C:\Program Files (x86)\gs\gs*\bin"
            ]

            for pattern in gs_paths:
                matches = glob.glob(pattern)
                if matches:
                    # 选择最新版本（按路径排序）
                    gs_bin_path = sorted(matches)[-1]

                    # 检查 gswin64c.exe 是否存在
                    gswin64c_path = os.path.join(gs_bin_path, "gswin64c.exe")
                    if os.path.exists(gswin64c_path):
                        # 添加到 PATH 环境变量
                        current_path = os.environ.get('PATH', '')
                        if gs_bin_path not in current_path:
                            os.environ['PATH'] = gs_bin_path + os.pathsep + current_path
                            self.logger.info(f"已添加 Ghostscript 路径到 PATH: {gs_bin_path}")
                        return True

            self.logger.warning("未找到 Ghostscript 安装，EPS/AI 文件将使用占位符")
            return False

        except Exception as e:
            self.logger.error(f"设置 Ghostscript 路径失败: {e}")
            return False

    def start_workers(self, num_workers: int = 2):
        """启动工作线程"""
        self.running = True
        
        for i in range(num_workers):
            worker = threading.Thread(target=self._worker_loop, daemon=True)
            worker.start()
            self.worker_threads.append(worker)
        
        self.logger.info(f"缩略图服务启动，工作线程数: {num_workers}")
    
    def stop_workers(self):
        """停止工作线程"""
        self.running = False
        
        # 添加停止信号到队列
        for _ in self.worker_threads:
            self.task_queue.put(None)
        
        # 等待线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5)
        
        self.logger.info("缩略图服务已停止")
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.running:
            try:
                task = self.task_queue.get(timeout=1)
                if task is None:  # 停止信号
                    break
                
                self._process_task(task)
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理缩略图任务失败: {e}")
    
    def _process_task(self, task: Dict[str, Any]):
        """处理缩略图生成任务"""
        try:
            file_path = task['file_path']
            sizes = task.get('sizes', ['medium'])
            callback = task.get('callback')
            
            results = {}
            
            for size in sizes:
                thumbnail_path = self.generate_thumbnail(file_path, size)
                if thumbnail_path:
                    results[size] = thumbnail_path
            
            if callback:
                callback(file_path, results)
                
        except Exception as e:
            self.logger.error(f"处理缩略图任务失败: {e}")
    
    def generate_thumbnail_async(self, file_path: str, sizes: list = None, 
                                callback=None) -> bool:
        """异步生成缩略图"""
        try:
            if not self.is_supported_format(file_path):
                return False
            
            task = {
                'file_path': file_path,
                'sizes': sizes or ['medium'],
                'callback': callback
            }
            
            self.task_queue.put(task)
            return True
            
        except Exception as e:
            self.logger.error(f"添加缩略图任务失败: {e}")
            return False
    
    def generate_thumbnail(self, file_path: str, size: str = 'medium') -> Optional[str]:
        """生成缩略图"""
        try:
            if not os.path.exists(file_path):
                return None
            
            if not self.is_supported_format(file_path):
                return None
            
            # 生成缩略图文件名
            thumbnail_path = self._get_thumbnail_path(file_path, size)
            
            # 如果缩略图已存在且较新，直接返回
            if os.path.exists(thumbnail_path):
                if os.path.getmtime(thumbnail_path) >= os.path.getmtime(file_path):
                    return thumbnail_path
            
            # 获取目标尺寸
            target_size = self.thumbnail_sizes.get(size, (300, 300))
            
            # 获取文件扩展名
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()

            # 根据文件类型选择处理方法
            success = False
            if ext in self.professional_formats:
                # 专业图形文件
                success = self._generate_professional_thumbnail(file_path, thumbnail_path, target_size, ext)
            elif ext in self.supported_formats:
                # 普通图像文件
                if HAS_PIL:
                    success = self._generate_with_pil(file_path, thumbnail_path, target_size)
                elif HAS_OPENCV:
                    success = self._generate_with_opencv(file_path, thumbnail_path, target_size)
                else:
                    self.logger.warning("没有可用的图像处理库")
                    return None
            else:
                self.logger.warning(f"不支持的文件格式: {ext}")
                return None
            
            if success:
                self.logger.debug(f"缩略图生成成功: {thumbnail_path}")
                return thumbnail_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"生成缩略图失败 {file_path}: {e}")
            return None
    
    def _generate_with_pil(self, file_path: str, thumbnail_path: str, 
                          target_size: Tuple[int, int]) -> bool:
        """使用PIL生成缩略图"""
        try:
            with Image.open(file_path) as image:
                # 转换为RGB模式（处理RGBA等格式）
                if image.mode in ('RGBA', 'LA', 'P'):
                    # 创建白色背景
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'P':
                        image = image.convert('RGBA')
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                elif image.mode != 'RGB':
                    image = image.convert('RGB')
                
                # 生成缩略图（保持宽高比）
                image.thumbnail(target_size, Image.Resampling.LANCZOS)
                
                # 创建目标目录
                os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
                
                # 保存缩略图
                image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)
                
                return True
                
        except Exception as e:
            self.logger.error(f"PIL生成缩略图失败: {e}")
            return False
    
    def _generate_with_opencv(self, file_path: str, thumbnail_path: str, 
                             target_size: Tuple[int, int]) -> bool:
        """使用OpenCV生成缩略图"""
        try:
            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                return False
            
            # 获取原始尺寸
            height, width = image.shape[:2]
            
            # 计算缩放比例（保持宽高比）
            scale = min(target_size[0] / width, target_size[1] / height)
            
            # 计算新尺寸
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # 缩放图像
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 创建目标目录
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, resized, [cv2.IMWRITE_JPEG_QUALITY, 85])
            
            return True
            
        except Exception as e:
            self.logger.error(f"OpenCV生成缩略图失败: {e}")
            return False
    
    def _get_thumbnail_path(self, file_path: str, size: str) -> str:
        """获取缩略图路径"""
        # 生成文件哈希作为文件名
        file_hash = self._get_file_hash(file_path)
        
        # 构建缩略图路径
        thumbnail_filename = f"{file_hash}_{size}.jpg"
        return str(self.thumbnail_dir / size / thumbnail_filename)
    
    def _get_file_hash(self, file_path: str) -> str:
        """获取文件哈希"""
        try:
            # 使用文件路径和修改时间生成哈希
            stat = os.stat(file_path)
            hash_input = f"{file_path}_{stat.st_mtime}_{stat.st_size}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except:
            # 如果获取文件信息失败，使用文件路径
            return hashlib.md5(file_path.encode()).hexdigest()
    
    def is_supported_format(self, file_path: str) -> bool:
        """检查是否支持的格式"""
        _, ext = os.path.splitext(file_path)
        return ext.lower() in self.all_supported_formats
    
    def get_thumbnail_path(self, file_path: str, size: str = 'medium') -> Optional[str]:
        """获取缩略图路径（如果存在）"""
        thumbnail_path = self._get_thumbnail_path(file_path, size)
        return thumbnail_path if os.path.exists(thumbnail_path) else None

    def get_thumbnail(self, file_path: str, size: str = 'medium') -> Optional[str]:
        """获取缩略图路径，如果不存在则生成"""
        try:
            # 首先检查是否已存在
            thumbnail_path = self.get_thumbnail_path(file_path, size)
            if thumbnail_path:
                return thumbnail_path

            # 如果不存在，尝试生成
            thumbnail_path = self.generate_thumbnail(file_path, size)
            return thumbnail_path

        except Exception as e:
            self.logger.error(f"获取缩略图失败: {e}")
            return None
    
    def delete_thumbnails(self, file_path: str) -> bool:
        """删除文件的所有缩略图"""
        try:
            deleted_count = 0
            
            for size in self.thumbnail_sizes.keys():
                thumbnail_path = self._get_thumbnail_path(file_path, size)
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)
                    deleted_count += 1
            
            if deleted_count > 0:
                self.logger.debug(f"删除缩略图: {file_path}, 数量: {deleted_count}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除缩略图失败: {e}")
            return False
    
    def cleanup_orphaned_thumbnails(self) -> int:
        """清理孤立的缩略图"""
        try:
            cleaned_count = 0
            
            for size_dir in self.thumbnail_dir.iterdir():
                if size_dir.is_dir() and size_dir.name in self.thumbnail_sizes:
                    for thumbnail_file in size_dir.glob("*.jpg"):
                        # 这里可以添加逻辑检查原文件是否还存在
                        # 由于没有反向映射，暂时跳过
                        pass
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理孤立缩略图失败: {e}")
            return 0
    
    def get_thumbnail_statistics(self) -> Dict[str, Any]:
        """获取缩略图统计信息"""
        try:
            stats = {
                'total_thumbnails': 0,
                'total_size': 0,
                'sizes': {}
            }
            
            for size in self.thumbnail_sizes.keys():
                size_dir = self.thumbnail_dir / size
                if size_dir.exists():
                    size_count = 0
                    size_total = 0
                    
                    for thumbnail_file in size_dir.glob("*.jpg"):
                        size_count += 1
                        size_total += thumbnail_file.stat().st_size
                    
                    stats['sizes'][size] = {
                        'count': size_count,
                        'total_size': size_total
                    }
                    
                    stats['total_thumbnails'] += size_count
                    stats['total_size'] += size_total
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取缩略图统计失败: {e}")
            return {}
    
    def batch_generate_thumbnails(self, file_paths: list, sizes: list = None) -> Dict[str, Any]:
        """批量生成缩略图"""
        try:
            results = {
                'success': 0,
                'failed': 0,
                'skipped': 0,
                'details': {}
            }
            
            sizes = sizes or ['medium']
            
            for file_path in file_paths:
                if not self.is_supported_format(file_path):
                    results['skipped'] += 1
                    continue
                
                file_results = {}
                success = True
                
                for size in sizes:
                    thumbnail_path = self.generate_thumbnail(file_path, size)
                    if thumbnail_path:
                        file_results[size] = thumbnail_path
                    else:
                        success = False
                
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                
                results['details'][file_path] = file_results
            
            self.logger.info(f"批量生成缩略图完成: 成功 {results['success']}, 失败 {results['failed']}, 跳过 {results['skipped']}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量生成缩略图失败: {e}")
            return {'success': 0, 'failed': len(file_paths), 'skipped': 0, 'details': {}}

    def _generate_professional_thumbnail(self, file_path: str, thumbnail_path: str,
                                       target_size: Tuple[int, int], ext: str) -> bool:
        """生成专业图形文件的缩略图"""
        try:
            if ext == '.psd':
                return self._generate_psd_thumbnail(file_path, thumbnail_path, target_size)
            elif ext in ['.ai', '.eps']:
                return self._generate_vector_thumbnail(file_path, thumbnail_path, target_size)
            else:
                self.logger.warning(f"不支持的专业图形格式: {ext}")
                return False

        except Exception as e:
            self.logger.error(f"生成专业图形文件缩略图失败 {file_path}: {e}")
            return False

    def _generate_psd_thumbnail(self, file_path: str, thumbnail_path: str,
                               target_size: Tuple[int, int]) -> bool:
        """生成PSD文件缩略图"""
        try:
            if not HAS_PSD_TOOLS:
                self.logger.warning("psd-tools库未安装，无法处理PSD文件")
                return False

            # 使用psd-tools读取PSD文件
            psd = PSDImage.open(file_path)

            # 获取合成图像
            pil_image = psd.composite()

            if pil_image is None:
                self.logger.warning(f"无法从PSD文件获取图像: {file_path}")
                return False

            # 转换为RGB模式
            if pil_image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', pil_image.size, (255, 255, 255))
                if pil_image.mode == 'P':
                    pil_image = pil_image.convert('RGBA')
                background.paste(pil_image, mask=pil_image.split()[-1] if pil_image.mode == 'RGBA' else None)
                pil_image = background
            elif pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            # 生成缩略图
            pil_image.thumbnail(target_size, Image.Resampling.LANCZOS)

            # 创建目标目录
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)

            # 保存缩略图
            pil_image.save(thumbnail_path, 'JPEG', quality=85, optimize=True)

            return True

        except Exception as e:
            self.logger.error(f"PSD缩略图生成失败: {e}")
            return False

    def _generate_vector_thumbnail(self, file_path: str, thumbnail_path: str,
                                  target_size: Tuple[int, int]) -> bool:
        """生成矢量图形文件（AI、EPS）缩略图"""
        try:
            # 首先尝试使用 ImageMagick 命令行工具
            if self._generate_with_imagemagick_cli(file_path, thumbnail_path, target_size):
                return True

            # 如果命令行失败，尝试 Wand 库
            if HAS_WAND:
                return self._generate_with_wand(file_path, thumbnail_path, target_size)

            # 如果都失败，创建占位符
            self.logger.warning("ImageMagick和Wand都不可用，创建占位符缩略图")
            return self._create_placeholder_thumbnail(file_path, thumbnail_path, target_size, "矢量图形")

        except Exception as e:
            self.logger.error(f"矢量图形缩略图生成失败: {e}")
            return self._create_placeholder_thumbnail(file_path, thumbnail_path, target_size, "矢量图形")

    def _generate_with_imagemagick_cli(self, file_path: str, thumbnail_path: str,
                                     target_size: Tuple[int, int]) -> bool:
        """使用 ImageMagick 命令行工具生成缩略图"""
        try:
            import subprocess

            # 创建目标目录
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)

            # 构建 ImageMagick 命令
            # 使用 magick 命令转换文件
            cmd = [
                'magick',
                '-density', '150',  # 设置分辨率
                file_path + '[0]',  # 只取第一页
                '-background', 'white',
                '-flatten',  # 合并图层
                '-resize', f'{target_size[0]}x{target_size[1]}',  # 调整大小
                '-quality', '85',  # JPEG 质量
                thumbnail_path
            ]

            self.logger.info(f"执行 ImageMagick 命令: {' '.join(cmd)}")

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                if os.path.exists(thumbnail_path):
                    self.logger.info(f"ImageMagick CLI 生成缩略图成功: {thumbnail_path}")
                    return True
                else:
                    self.logger.warning("ImageMagick 命令执行成功但文件未生成")
                    return False
            else:
                self.logger.warning(f"ImageMagick 命令执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error("ImageMagick 命令执行超时")
            return False
        except FileNotFoundError:
            self.logger.warning("ImageMagick 命令未找到")
            return False
        except Exception as e:
            self.logger.error(f"ImageMagick CLI 处理失败: {e}")
            return False

    def _generate_with_wand(self, file_path: str, thumbnail_path: str,
                           target_size: Tuple[int, int]) -> bool:
        """使用 Wand 库生成缩略图"""
        try:
            # 使用Wand（ImageMagick）处理矢量文件
            with WandImage(filename=file_path, resolution=150) as img:
                # 转换为RGB
                img.format = 'jpeg'
                img.background_color = 'white'
                img.alpha_channel = 'remove'

                # 计算缩放比例（保持宽高比）
                original_width = img.width
                original_height = img.height
                scale = min(target_size[0] / original_width, target_size[1] / original_height)

                # 计算新尺寸
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                # 调整大小
                img.resize(new_width, new_height)

                # 创建目标目录
                os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)

                # 保存缩略图
                img.compression_quality = 85
                img.save(filename=thumbnail_path)

            return True

        except Exception as e:
            self.logger.error(f"Wand处理矢量文件失败: {e}")
            return False

    def _create_placeholder_thumbnail(self, file_path: str, thumbnail_path: str,
                                    target_size: Tuple[int, int], file_type: str) -> bool:
        """创建占位符缩略图"""
        try:
            if not HAS_PIL:
                return False

            # 获取文件扩展名
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()

            # 创建占位符图像
            img = Image.new('RGB', target_size, color='#f8f9fa')

            try:
                from PIL import ImageDraw, ImageFont
                draw = ImageDraw.Draw(img)

                # 绘制边框
                border_color = '#dee2e6'
                draw.rectangle([0, 0, target_size[0]-1, target_size[1]-1], outline=border_color, width=2)

                # 绘制文件类型图标区域
                icon_size = min(target_size[0], target_size[1]) // 3
                icon_x = (target_size[0] - icon_size) // 2
                icon_y = (target_size[1] - icon_size) // 2 - 20

                # 根据文件类型选择颜色
                if ext == '.ai':
                    icon_color = '#ff6b35'  # AI橙色
                    type_text = 'AI'
                elif ext == '.eps':
                    icon_color = '#6f42c1'  # EPS紫色
                    type_text = 'EPS'
                else:
                    icon_color = '#6c757d'  # 默认灰色
                    type_text = ext.upper().replace('.', '')

                # 绘制文件图标（简单的矩形）
                draw.rectangle([icon_x, icon_y, icon_x + icon_size, icon_y + icon_size],
                             fill=icon_color, outline='#ffffff', width=3)

                # 尝试使用默认字体绘制文字
                try:
                    # 尝试使用系统字体
                    font_size = max(16, target_size[0] // 20)
                    font = ImageFont.truetype("arial.ttf", font_size)
                except:
                    # 如果没有找到字体，使用默认字体
                    font = ImageFont.load_default()

                # 绘制文件类型文字
                text_y = icon_y + icon_size + 10

                # 获取文字尺寸
                bbox = draw.textbbox((0, 0), type_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = (target_size[0] - text_width) // 2

                draw.text((text_x, text_y), type_text, fill='#495057', font=font)

                # 绘制"预览不可用"提示
                hint_text = "预览不可用"
                bbox = draw.textbbox((0, 0), hint_text, font=font)
                hint_width = bbox[2] - bbox[0]
                hint_x = (target_size[0] - hint_width) // 2
                hint_y = text_y + 30

                draw.text((hint_x, hint_y), hint_text, fill='#6c757d', font=font)

            except Exception as e:
                self.logger.warning(f"绘制占位符图标失败，使用简单背景: {e}")
                # 如果绘制失败，至少填充一个有颜色的背景
                draw = ImageDraw.Draw(img)
                draw.rectangle([10, 10, target_size[0]-10, target_size[1]-10], fill='#e9ecef')

            # 创建目标目录
            os.makedirs(os.path.dirname(thumbnail_path), exist_ok=True)

            # 保存占位符
            img.save(thumbnail_path, 'JPEG', quality=85, optimize=True)

            self.logger.info(f"创建{file_type}占位符缩略图: {thumbnail_path}")
            return True

        except Exception as e:
            self.logger.error(f"创建占位符缩略图失败: {e}")
            return False
