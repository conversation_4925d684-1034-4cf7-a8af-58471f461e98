#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络访问控制服务
"""

import ipaddress
from typing import Dict, Any, List, Optional
from utils.logger import setup_logger

class NetworkAccessService:
    """网络访问控制服务"""
    
    def __init__(self, settings):
        self.settings = settings
        self.logger = setup_logger("NetworkAccessService")
        
        # 内网网段定义
        self.internal_networks = [
            ipaddress.ip_network("***********/16"),  # 私有网络A类
            ipaddress.ip_network("10.0.0.0/8"),      # 私有网络B类
            ipaddress.ip_network("**********/12"),   # 私有网络C类
            ipaddress.ip_network("*********/8"),     # 本地回环
            ipaddress.ip_network("***********/16"),  # 链路本地地址
            ipaddress.ip_network("**********/15"),   # 测试网络（RFC 2544）
        ]
        
        # 从配置加载自定义内网网段
        custom_networks = self.settings.get('network.internal_networks', [])
        for network_str in custom_networks:
            try:
                network = ipaddress.ip_network(network_str)
                if network not in self.internal_networks:
                    self.internal_networks.append(network)
            except ValueError as e:
                self.logger.warning(f"无效的网络配置: {network_str}, 错误: {e}")
    
    def is_internal_ip(self, ip_address: str) -> bool:
        """判断IP地址是否为内网地址"""
        try:
            ip = ipaddress.ip_address(ip_address)
            
            # 检查是否在任何内网网段中
            for network in self.internal_networks:
                if ip in network:
                    return True
            
            return False
            
        except ValueError:
            self.logger.warning(f"无效的IP地址: {ip_address}")
            return False
    
    def is_external_ip(self, ip_address: str) -> bool:
        """判断IP地址是否为外网地址"""
        return not self.is_internal_ip(ip_address)
    
    def check_global_access_permission(self, ip_address: str) -> Dict[str, Any]:
        """检查全局网络访问权限"""
        try:
            is_internal = self.is_internal_ip(ip_address)
            
            # 获取全局访问设置
            enable_internal = self.settings.get('network.enable_internal_access', True)
            enable_external = self.settings.get('network.enable_external_access', False)
            
            if is_internal:
                allowed = enable_internal
                access_type = "internal"
                reason = "内网访问" if allowed else "内网访问已被禁用"
            else:
                allowed = enable_external
                access_type = "external"
                reason = "外网访问" if allowed else "外网访问已被禁用"
            
            return {
                "allowed": allowed,
                "access_type": access_type,
                "is_internal": is_internal,
                "reason": reason,
                "ip_address": ip_address
            }
            
        except Exception as e:
            self.logger.error(f"检查全局访问权限失败: {e}")
            return {
                "allowed": False,
                "access_type": "unknown",
                "is_internal": False,
                "reason": f"权限检查失败: {e}",
                "ip_address": ip_address
            }
    
    def check_folder_access_permission(self, ip_address: str, folder_permissions: Dict[str, Any]) -> Dict[str, Any]:
        """检查文件夹级别的网络访问权限"""
        try:
            # 判断是否为内网IP
            is_internal = self.is_internal_ip(ip_address)

            # 获取文件夹的网络访问设置
            network_access = folder_permissions.get('network_access', {})
            allow_internal = network_access.get('internal', True)   # 默认为True，允许内网访问
            allow_external = network_access.get('external', False)  # 默认为False，需要明确允许

            if is_internal:
                folder_allowed = allow_internal
                access_type = "internal"
                reason = "文件夹允许内网访问" if folder_allowed else "文件夹禁止内网访问"
            else:
                folder_allowed = allow_external
                access_type = "external"
                reason = "文件夹允许外网访问" if folder_allowed else "文件夹禁止外网访问"

            return {
                "allowed": folder_allowed,
                "access_type": access_type,
                "is_internal": is_internal,
                "reason": reason,
                "ip_address": ip_address,
                "folder_settings": {
                    "allow_internal": allow_internal,
                    "allow_external": allow_external
                }
            }
            
        except Exception as e:
            self.logger.error(f"检查文件夹访问权限失败: {e}")
            return {
                "allowed": False,
                "access_type": "unknown",
                "is_internal": False,
                "reason": f"权限检查失败: {e}",
                "ip_address": ip_address
            }
    
    def get_client_ip(self, request) -> str:
        """从请求中获取客户端真实IP地址"""
        try:
            # 检查代理头
            forwarded_ips = request.headers.get('X-Forwarded-For')
            if forwarded_ips:
                # 取第一个IP（客户端真实IP）
                return forwarded_ips.split(',')[0].strip()
            
            # 检查其他代理头
            real_ip = request.headers.get('X-Real-IP')
            if real_ip:
                return real_ip.strip()
            
            # 使用远程地址
            return request.remote_addr or '127.0.0.1'
            
        except Exception as e:
            self.logger.warning(f"获取客户端IP失败: {e}")
            return '127.0.0.1'
    
    def log_access_attempt(self, ip_address: str, access_result: Dict[str, Any], 
                          resource: str = None, user_id: int = None):
        """记录访问尝试"""
        try:
            log_data = {
                "ip_address": ip_address,
                "access_type": access_result.get("access_type"),
                "allowed": access_result.get("allowed"),
                "reason": access_result.get("reason"),
                "resource": resource,
                "user_id": user_id
            }
            
            if access_result.get("allowed"):
                self.logger.info(f"访问允许: {log_data}")
            else:
                self.logger.warning(f"访问拒绝: {log_data}")
                
        except Exception as e:
            self.logger.error(f"记录访问日志失败: {e}")
    
    def get_network_info(self, ip_address: str) -> Dict[str, Any]:
        """获取IP地址的网络信息"""
        try:
            ip = ipaddress.ip_address(ip_address)
            
            # 判断IP类型
            ip_type = "IPv4" if ip.version == 4 else "IPv6"
            
            # 判断是否为私有地址
            is_private = ip.is_private
            
            # 判断是否为回环地址
            is_loopback = ip.is_loopback
            
            # 判断是否为链路本地地址
            is_link_local = ip.is_link_local
            
            # 判断网络类型
            network_type = "unknown"
            if is_loopback:
                network_type = "loopback"
            elif is_link_local:
                network_type = "link_local"
            elif is_private:
                network_type = "private"
            else:
                network_type = "public"
            
            return {
                "ip_address": ip_address,
                "ip_type": ip_type,
                "network_type": network_type,
                "is_private": is_private,
                "is_loopback": is_loopback,
                "is_link_local": is_link_local,
                "is_internal": self.is_internal_ip(ip_address)
            }
            
        except ValueError:
            return {
                "ip_address": ip_address,
                "ip_type": "invalid",
                "network_type": "invalid",
                "is_private": False,
                "is_loopback": False,
                "is_link_local": False,
                "is_internal": False
            }
