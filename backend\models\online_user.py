#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线用户监控模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from config.database import Base
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

class OnlineUser(Base):
    """在线用户模型"""
    
    __tablename__ = 'online_users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID，NULL表示匿名用户')
    session_id = Column(String(100), nullable=False, unique=True, comment='会话ID')
    
    # 连接信息
    ip_address = Column(String(45), nullable=True, comment='IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    browser_info = Column(JSON, nullable=True, comment='浏览器信息')
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment='是否活跃')
    current_page = Column(String(255), nullable=True, comment='当前页面')
    last_activity = Column(DateTime, default=func.now(), comment='最后活动时间')
    
    # 统计信息
    page_views = Column(Integer, default=0, comment='页面浏览次数')
    search_count = Column(Integer, default=0, comment='搜索次数')
    download_count = Column(Integer, default=0, comment='下载次数')
    
    # 时间戳
    login_time = Column(DateTime, default=func.now(), comment='登录时间')
    logout_time = Column(DateTime, nullable=True, comment='登出时间')
    
    # 关联关系
    user = relationship("User", backref="online_sessions")
    
    def __init__(self, session_id: str, **kwargs):
        self.session_id = session_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def update_activity(self, page: str = None):
        """更新活动状态"""
        self.last_activity = datetime.now()
        self.is_active = True
        if page:
            self.current_page = page
            if self.page_views is None:
                self.page_views = 0
            self.page_views += 1
    
    def add_search(self):
        """增加搜索次数"""
        if self.search_count is None:
            self.search_count = 0
        self.search_count += 1
        self.update_activity()
    
    def add_download(self):
        """增加下载次数"""
        if self.download_count is None:
            self.download_count = 0
        self.download_count += 1
        self.update_activity()
    
    def logout(self):
        """用户登出"""
        self.is_active = False
        self.logout_time = datetime.now()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """检查会话是否过期"""
        if not self.last_activity:
            return True
        return datetime.now() - self.last_activity > timedelta(minutes=timeout_minutes)
    
    def get_online_duration(self) -> timedelta:
        """获取在线时长"""
        end_time = self.logout_time or datetime.now()
        return end_time - self.login_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'browser_info': self.browser_info,
            'is_active': self.is_active,
            'current_page': self.current_page,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'page_views': self.page_views,
            'search_count': self.search_count,
            'download_count': self.download_count,
            'login_time': self.login_time.isoformat() if self.login_time else None,
            'logout_time': self.logout_time.isoformat() if self.logout_time else None,
            'online_duration_seconds': int(self.get_online_duration().total_seconds())
        }

class UserSession(Base):
    """用户会话历史模型"""
    
    __tablename__ = 'user_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID')
    session_id = Column(String(100), nullable=False, comment='会话ID')
    
    # 会话信息
    ip_address = Column(String(45), nullable=True, comment='IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    login_method = Column(String(20), default='web', comment='登录方式: web/api/mobile')
    
    # 活动统计
    total_page_views = Column(Integer, default=0, comment='总页面浏览次数')
    total_searches = Column(Integer, default=0, comment='总搜索次数')
    total_downloads = Column(Integer, default=0, comment='总下载次数')
    
    # 时间信息
    login_time = Column(DateTime, default=func.now(), comment='登录时间')
    logout_time = Column(DateTime, nullable=True, comment='登出时间')
    last_activity = Column(DateTime, default=func.now(), comment='最后活动时间')
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment='是否活跃')
    logout_reason = Column(String(50), nullable=True, comment='登出原因: manual/timeout/forced')
    
    # 关联关系
    user = relationship("User", backref="session_history")
    
    def __init__(self, session_id: str, **kwargs):
        self.session_id = session_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def end_session(self, reason: str = 'manual'):
        """结束会话"""
        self.is_active = False
        self.logout_time = datetime.now()
        self.logout_reason = reason
    
    def get_session_duration(self) -> timedelta:
        """获取会话时长"""
        end_time = self.logout_time or datetime.now()
        return end_time - self.login_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'login_method': self.login_method,
            'total_page_views': self.total_page_views,
            'total_searches': self.total_searches,
            'total_downloads': self.total_downloads,
            'login_time': self.login_time.isoformat() if self.login_time else None,
            'logout_time': self.logout_time.isoformat() if self.logout_time else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'is_active': self.is_active,
            'logout_reason': self.logout_reason,
            'session_duration_seconds': int(self.get_session_duration().total_seconds())
        }

class UserRanking(Base):
    """用户排行榜模型"""
    
    __tablename__ = 'user_rankings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')
    ranking_type = Column(String(20), nullable=False, comment='排行类型: search/download/activity')
    ranking_period = Column(String(20), default='daily', comment='排行周期: daily/weekly/monthly/all')
    
    # 排行数据
    score = Column(Integer, default=0, comment='得分')
    rank_position = Column(Integer, nullable=True, comment='排名位置')
    
    # 详细统计
    search_count = Column(Integer, default=0, comment='搜索次数')
    download_count = Column(Integer, default=0, comment='下载次数')
    download_size = Column(Integer, default=0, comment='下载大小')
    activity_score = Column(Integer, default=0, comment='活跃度得分')
    
    # 时间信息
    period_start = Column(DateTime, nullable=False, comment='统计周期开始时间')
    period_end = Column(DateTime, nullable=False, comment='统计周期结束时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    user = relationship("User", backref="rankings")
    
    def __init__(self, user_id: int, ranking_type: str, **kwargs):
        self.user_id = user_id
        self.ranking_type = ranking_type
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def calculate_score(self):
        """计算综合得分"""
        if self.ranking_type == 'search':
            self.score = self.search_count or 0
        elif self.ranking_type == 'download':
            self.score = (self.download_count or 0) * 10 + (self.download_size or 0) // 1024 // 1024  # MB
        elif self.ranking_type == 'activity':
            self.score = (self.search_count or 0) + (self.download_count or 0) * 2 + (self.activity_score or 0)
        else:
            self.score = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'ranking_type': self.ranking_type,
            'ranking_period': self.ranking_period,
            'score': self.score,
            'rank_position': self.rank_position,
            'search_count': self.search_count,
            'download_count': self.download_count,
            'download_size': self.download_size,
            'activity_score': self.activity_score,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
