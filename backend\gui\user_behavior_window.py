#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为监控管理窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime, timedelta, timezone
import threading
from typing import Dict, List, Any
from gui.blocked_keyword_dialog import BlockedKeywordDialog, BatchImportDialog
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.time_utils import TimeUtils

class UserBehaviorWindow:
    """用户行为监控管理窗口类"""
    
    def __init__(self, parent, server_instance):
        self.parent = parent
        self.server = server_instance
        self.window = None
        self.behavior_service = None
        
        # 界面组件
        self.notebook = None
        # self.online_users_tree = None  # 在线用户功能已被移除
        self.search_records_tree = None
        self.download_records_tree = None
        self.rankings_tree = None
        self.sensitive_files_tree = None
        self.blocked_keywords_tree = None
        # self.user_quotas_tree = None  # 用户配额功能已被移除
        self.user_bans_tree = None
        
        # 数据
        # self.current_online_users = []  # 在线用户功能已被移除
        self.current_search_records = []
        self.current_download_records = []
        self.current_rankings = []
        
        # 自动刷新
        self.auto_refresh_enabled = True
        self.refresh_interval = 5000  # 5秒

    def format_china_time(self, time_str):
        """格式化为中国时区时间"""
        if not time_str:
            return ""

        try:
            # 如果是字符串，尝试解析
            if isinstance(time_str, str):
                # 处理不同的时间格式
                if 'T' in time_str:
                    # ISO格式: 2025-06-28T19:07:52.123456
                    if '.' in time_str:
                        dt = datetime.fromisoformat(time_str.split('.')[0])
                    else:
                        dt = datetime.fromisoformat(time_str.replace('Z', ''))
                else:
                    # 标准格式: 2025-06-28 19:07:52
                    dt = datetime.strptime(time_str[:19], '%Y-%m-%d %H:%M:%S')
            else:
                dt = time_str

            # 如果没有时区信息，假设是UTC时间，转换为中国时区
            if dt.tzinfo is None:
                # 假设是UTC时间，转换为中国时区 (UTC+8)
                china_tz = timezone(timedelta(hours=8))
                dt = dt.replace(tzinfo=timezone.utc).astimezone(china_tz)

            # 返回格式化的中国时间
            return dt.strftime('%Y-%m-%d %H:%M:%S')

        except Exception as e:
            # 如果解析失败，返回原始字符串的前19个字符
            return str(time_str)[:19] if time_str else ""

    def get_ban_status_text(self, ban_dict):
        """获取封禁状态文本"""
        if not ban_dict.get('is_active'):
            return "已解封"

        # 检查是否永久封禁
        if ban_dict.get('is_permanent'):
            return "永久封禁"

        # 检查是否过期
        expires_at = ban_dict.get('expires_at')
        if expires_at:
            remaining_time = TimeUtils.get_remaining_time_china(expires_at)
            if remaining_time == "已过期":
                return "已过期"
            elif remaining_time == "无效时间":
                return "生效"
            else:
                return f"剩余{remaining_time}"

        return "生效"

    def show(self):
        """显示用户行为监控窗口"""
        if self.window:
            self.window.lift()
            return
        
        # 获取用户行为服务
        self.behavior_service = self.server.services.get('user_behavior')
        if not self.behavior_service:
            messagebox.showerror("错误", "用户行为监控服务未启用")
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("用户行为监控管理")
        self.window.geometry("1600x900")
        self.window.transient(self.parent)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.create_widgets()
        self.load_all_data()
        self.start_auto_refresh()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 创建各个选项卡
        # self.create_online_users_tab()  # 在线用户功能已被移除
        self.create_search_records_tab()
        self.create_download_records_tab()
        self.create_rankings_tab()
        self.create_sensitive_files_tab()
        self.create_blocked_keywords_tab()
        # self.create_user_quotas_tab()  # 用户配额功能已被移除
        self.create_user_bans_tab()
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 刷新按钮
        ttk.Button(toolbar, text="刷新数据", command=self.load_all_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出按钮
        ttk.Button(toolbar, text="导出报告", command=self.export_report).pack(side=tk.LEFT, padx=(0, 5))
        
        # 清理按钮
        ttk.Button(toolbar, text="清理旧数据", command=self.cleanup_old_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)
        
        # 自动刷新开关
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=(0, 5))
        
        # 刷新间隔
        ttk.Label(toolbar, text="刷新间隔(秒):").pack(side=tk.LEFT, padx=(10, 5))
        self.refresh_interval_var = tk.StringVar(value="5")
        interval_spinbox = ttk.Spinbox(toolbar, from_=1, to=60, width=5, 
                                      textvariable=self.refresh_interval_var,
                                      command=self.update_refresh_interval)
        interval_spinbox.pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(toolbar, text="就绪")
        self.status_label.pack(side=tk.RIGHT)
    
    def create_online_users_tab(self):
        """创建在线用户选项卡 - 已禁用"""
        # 在线用户功能已被移除
        pass

    
    def create_search_records_tab(self):
        """创建搜索记录选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="搜索记录")
        
        # 过滤器框架
        filter_frame = ttk.LabelFrame(frame, text="过滤条件", padding=10)
        filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        filter_row1 = ttk.Frame(filter_frame)
        filter_row1.pack(fill=tk.X, pady=(0, 5))
        
        # 用户过滤
        ttk.Label(filter_row1, text="用户:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_user_var = tk.StringVar()
        self.search_user_combo = ttk.Combobox(filter_row1, textvariable=self.search_user_var, width=15)
        self.search_user_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 加载用户列表
        self.load_user_list_for_filter()
        
        # 时间过滤
        ttk.Label(filter_row1, text="时间范围:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_time_var = tk.StringVar(value="今日")
        time_combo = ttk.Combobox(filter_row1, textvariable=self.search_time_var, width=10,
                                 values=["今日", "本周", "本月", "全部"])
        time_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 敏感内容过滤
        self.search_sensitive_var = tk.BooleanVar()
        ttk.Checkbutton(filter_row1, text="仅显示敏感搜索", 
                       variable=self.search_sensitive_var).pack(side=tk.LEFT, padx=(0, 10))
        
        # 屏蔽内容过滤
        self.search_blocked_var = tk.BooleanVar()
        ttk.Checkbutton(filter_row1, text="仅显示被屏蔽搜索", 
                       variable=self.search_blocked_var).pack(side=tk.LEFT, padx=(0, 10))
        
        # 应用过滤按钮
        ttk.Button(filter_row1, text="应用过滤", command=self.apply_search_filter).pack(side=tk.LEFT, padx=(10, 0))
        
        # 搜索记录列表
        list_frame = ttk.LabelFrame(frame, text="搜索记录", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建树形视图
        columns = ("ID", "用户", "搜索内容", "结果数", "敏感", "屏蔽", "屏蔽原因", "IP地址", "搜索时间")
        self.search_records_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {
            "ID": 60, "用户": 100, "搜索内容": 300, "结果数": 80,
            "敏感": 60, "屏蔽": 60, "屏蔽原因": 200, "IP地址": 120, "搜索时间": 150
        }
        
        for col in columns:
            self.search_records_tree.heading(col, text=col)
            self.search_records_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        search_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.search_records_tree.yview)
        self.search_records_tree.configure(yscrollcommand=search_scrollbar.set)
        
        self.search_records_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        search_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.create_search_records_context_menu()
    
    def create_download_records_tab(self):
        """创建下载记录选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="下载记录")
        
        # 这里可以复用现有的下载记录窗口组件
        # 或者创建一个简化版本专门用于行为监控
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(frame, text="下载统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.download_stats_labels = {}
        stats_row = ttk.Frame(stats_frame)
        stats_row.pack(fill=tk.X)
        
        for i, (key, label) in enumerate([
            ('total_downloads', '总下载次数'),
            ('today_downloads', '今日下载'),
            ('total_size', '总下载大小'),
            ('avg_size', '平均文件大小')
        ]):
            col_frame = ttk.Frame(stats_row)
            col_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            ttk.Label(col_frame, text=f"{label}:").pack()
            self.download_stats_labels[key] = ttk.Label(col_frame, text="0", font=('Arial', 12, 'bold'))
            self.download_stats_labels[key].pack()
        
        # 下载记录列表（简化版）
        list_frame = ttk.LabelFrame(frame, text="最近下载记录", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ("ID", "用户", "下载类型", "文件ID", "文件大小", "加密", "状态", "IP地址", "下载时间")
        self.download_records_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        column_widths = {
            "ID": 60, "用户": 100, "下载类型": 80, "文件ID": 80, "文件大小": 100,
            "加密": 60, "状态": 80, "IP地址": 120, "下载时间": 150
        }
        
        for col in columns:
            self.download_records_tree.heading(col, text=col)
            self.download_records_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        download_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.download_records_tree.yview)
        self.download_records_tree.configure(yscrollcommand=download_scrollbar.set)
        
        self.download_records_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        download_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_rankings_tab(self):
        """创建排行榜选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="用户排行")
        
        # 排行榜类型选择
        type_frame = ttk.LabelFrame(frame, text="排行榜类型", padding=10)
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        type_row = ttk.Frame(type_frame)
        type_row.pack(fill=tk.X)
        
        ttk.Label(type_row, text="排行类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.ranking_type_var = tk.StringVar(value="综合活跃度")
        type_combo = ttk.Combobox(type_row, textvariable=self.ranking_type_var, width=15,
                                 values=["综合活跃度", "搜索排行", "下载排行"])
        type_combo.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(type_row, text="时间周期:").pack(side=tk.LEFT, padx=(0, 5))
        self.ranking_period_var = tk.StringVar(value="今日")
        period_combo = ttk.Combobox(type_row, textvariable=self.ranking_period_var, width=10,
                                   values=["今日", "本周", "本月", "全部"])
        period_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(type_row, text="更新排行", command=self.update_rankings).pack(side=tk.LEFT, padx=(10, 0))
        
        # 排行榜列表
        list_frame = ttk.LabelFrame(frame, text="排行榜", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        columns = ("排名", "用户", "搜索次数", "下载次数", "下载大小", "活跃度得分")
        self.rankings_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        column_widths = {
            "排名": 60, "用户": 120, "搜索次数": 100, "下载次数": 100,
            "下载大小": 120, "活跃度得分": 120
        }
        
        for col in columns:
            self.rankings_tree.heading(col, text=col)
            self.rankings_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        rankings_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.rankings_tree.yview)
        self.rankings_tree.configure(yscrollcommand=rankings_scrollbar.set)
        
        self.rankings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rankings_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_sensitive_files_tab(self):
        """创建敏感文件选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="敏感文件")

        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="添加敏感文件", command=self.add_sensitive_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="编辑", command=self.edit_sensitive_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除", command=self.delete_sensitive_file).pack(side=tk.LEFT, padx=(0, 5))

        # 敏感文件列表
        list_frame = ttk.LabelFrame(frame, text="敏感文件列表", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        columns = ("ID", "文件路径", "敏感原因", "严重程度", "自动检测", "创建时间")
        self.sensitive_files_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        column_widths = {
            "ID": 60, "文件路径": 300, "敏感原因": 200, "严重程度": 100,
            "自动检测": 80, "创建时间": 150
        }

        for col in columns:
            self.sensitive_files_tree.heading(col, text=col)
            self.sensitive_files_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # 添加滚动条
        sensitive_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.sensitive_files_tree.yview)
        self.sensitive_files_tree.configure(yscrollcommand=sensitive_scrollbar.set)

        self.sensitive_files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sensitive_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_blocked_keywords_tab(self):
        """创建屏蔽关键词选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="屏蔽关键词")

        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="添加关键词", command=self.add_blocked_keyword).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="编辑", command=self.edit_blocked_keyword).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除", command=self.delete_blocked_keyword).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="批量导入", command=self.import_blocked_keywords).pack(side=tk.LEFT, padx=(0, 5))

        # 屏蔽关键词列表
        list_frame = ttk.LabelFrame(frame, text="屏蔽关键词列表", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        columns = ("ID", "关键词", "类型", "正则", "严重程度", "启用", "命中次数", "创建时间")
        self.blocked_keywords_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        column_widths = {
            "ID": 60, "关键词": 200, "类型": 100, "正则": 60,
            "严重程度": 100, "启用": 60, "命中次数": 80, "创建时间": 150
        }

        for col in columns:
            self.blocked_keywords_tree.heading(col, text=col)
            self.blocked_keywords_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # 添加滚动条
        keywords_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.blocked_keywords_tree.yview)
        self.blocked_keywords_tree.configure(yscrollcommand=keywords_scrollbar.set)

        self.blocked_keywords_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keywords_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.create_blocked_keywords_context_menu()

    def create_user_quotas_tab(self):
        """创建用户配额选项卡 - 已禁用"""
        # 用户配额功能已被移除
        pass

    def create_user_bans_tab(self):
        """创建用户封禁选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="用户封禁")

        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="封禁用户", command=self.ban_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="解封用户", command=self.unban_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="查看详情", command=self.view_ban_details).pack(side=tk.LEFT, padx=(0, 5))

        # 用户封禁列表
        list_frame = ttk.LabelFrame(frame, text="用户封禁列表", padding=5)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        columns = ("ID", "用户ID", "用户名", "封禁类型", "原因", "状态", "封禁时间", "到期时间")
        self.user_bans_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        column_widths = {
            "ID": 60, "用户ID": 80, "用户名": 120, "封禁类型": 80, "原因": 250,
            "状态": 60, "封禁时间": 150, "到期时间": 150
        }

        for col in columns:
            self.user_bans_tree.heading(col, text=col)
            self.user_bans_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # 添加滚动条
        bans_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_bans_tree.yview)
        self.user_bans_tree.configure(yscrollcommand=bans_scrollbar.set)

        self.user_bans_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        bans_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # ==================== 数据加载方法 ====================

    def load_all_data(self):
        """加载所有数据"""
        self.status_label.config(text="正在加载数据...")

        # 在后台线程中加载数据
        threading.Thread(target=self._load_data_thread, daemon=True).start()

    def _load_data_thread(self):
        """后台加载数据线程"""
        try:
            # 加载在线用户
            self.load_online_users()

            # 加载搜索记录
            self.load_search_records()

            # 加载下载记录
            self.load_download_records()

            # 加载排行榜
            self.load_rankings()

            # 加载敏感文件
            self.load_sensitive_files()

            # 加载屏蔽关键词
            self.load_blocked_keywords()

            # 加载用户配额 - 功能已被移除
            # self.load_user_quotas()

            # 加载用户封禁
            self.load_user_bans()

            # 更新状态
            self.window.after(0, lambda: self.status_label.config(text=f"数据加载完成 - {datetime.now().strftime('%H:%M:%S')}"))

        except Exception as e:
            self.window.after(0, lambda: self.status_label.config(text=f"加载失败: {str(e)}"))

    def load_online_users(self):
        """加载在线用户数据 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def _update_online_users_ui(self):
        """更新在线用户UI - 已禁用"""
        # 在线用户功能已被移除
        pass

    def load_search_records(self):
        """加载搜索记录"""
        try:
            if not self.behavior_service:
                return

            # 获取搜索记录
            search_records = self.behavior_service.get_search_records(limit=100)
            self.current_search_records = search_records
            self.window.after(0, self._update_search_records_ui)

        except Exception as e:
            print(f"加载搜索记录失败: {e}")
            self.current_search_records = []
            self.window.after(0, self._update_search_records_ui)

    def _update_search_records_ui(self):
        """更新搜索记录UI"""
        # 清空现有数据
        for item in self.search_records_tree.get_children():
            self.search_records_tree.delete(item)

        # 添加搜索记录数据
        for record in self.current_search_records:
            values = (
                record.get('id', ''),
                record.get('username', '匿名'),
                record.get('search_query', ''),
                record.get('results_count', 0),
                '是' if record.get('is_sensitive') else '否',
                '是' if record.get('is_blocked') else '否',
                record.get('block_reason', ''),
                record.get('ip_address', ''),
                record.get('created_at', '')
            )
            self.search_records_tree.insert('', 'end', values=values)

    def load_download_records(self):
        """加载下载记录"""
        try:
            if not self.behavior_service:
                return

            # 获取下载记录
            download_records = self.behavior_service.get_download_records(limit=100)
            self.current_download_records = download_records
            self.window.after(0, self._update_download_records_ui)

        except Exception as e:
            print(f"加载下载记录失败: {e}")
            self.current_download_records = []
            self.window.after(0, self._update_download_records_ui)

    def _update_download_records_ui(self):
        """更新下载记录UI"""
        # 清空现有数据
        for item in self.download_records_tree.get_children():
            self.download_records_tree.delete(item)

        # 添加下载记录数据
        total_size = 0
        today_count = 0
        today = datetime.now().date()

        for record in self.current_download_records:
            values = (
                record.get('id', ''),
                record.get('username', '匿名'),
                record.get('download_type', ''),
                record.get('file_id', ''),
                f"{record.get('file_size', 0) / 1024 / 1024:.2f} MB" if record.get('file_size') else '0 MB',
                '是' if record.get('is_encrypted') else '否',
                record.get('download_status', ''),
                record.get('ip_address', ''),
                record.get('created_at', '')
            )
            self.download_records_tree.insert('', 'end', values=values)

            # 统计数据
            total_size += record.get('file_size', 0)
            if record.get('created_at'):
                try:
                    record_date = datetime.strptime(record['created_at'], '%Y-%m-%d %H:%M:%S').date()
                    if record_date == today:
                        today_count += 1
                except:
                    pass

        # 更新统计信息
        total_count = len(self.current_download_records)
        avg_size = total_size / total_count if total_count > 0 else 0

        self.download_stats_labels['total_downloads'].config(text=str(total_count))
        self.download_stats_labels['today_downloads'].config(text=str(today_count))
        self.download_stats_labels['total_size'].config(text=f"{total_size / 1024 / 1024:.2f} MB")
        self.download_stats_labels['avg_size'].config(text=f"{avg_size / 1024 / 1024:.2f} MB")

    def load_rankings(self):
        """加载排行榜"""
        try:
            if not self.behavior_service:
                return

            # 将中文选项映射为英文值
            ranking_type_map = {
                "综合活跃度": "activity",
                "搜索排行": "search",
                "下载排行": "download"
            }
            period_map = {
                "今日": "daily",
                "本周": "weekly",
                "本月": "monthly",
                "全部": "all"
            }

            ranking_type = ranking_type_map.get(self.ranking_type_var.get(), "activity")
            period = period_map.get(self.ranking_period_var.get(), "daily")

            rankings = self.behavior_service.get_user_rankings(ranking_type, period, 20)
            self.current_rankings = rankings

            self.window.after(0, self._update_rankings_ui)

        except Exception as e:
            print(f"加载排行榜失败: {e}")

    def _update_rankings_ui(self):
        """更新排行榜UI"""
        # 清空现有数据
        for item in self.rankings_tree.get_children():
            self.rankings_tree.delete(item)

        # 添加排行榜数据
        for ranking in self.current_rankings:
            values = (
                ranking.get('rank', ''),
                ranking.get('username', ''),
                ranking.get('search_count', 0),
                ranking.get('download_count', 0),
                self.format_file_size(ranking.get('download_size', 0)),
                ranking.get('activity_score', 0)
            )
            self.rankings_tree.insert('', 'end', values=values)

    def load_sensitive_files(self):
        """加载敏感文件"""
        try:
            if not self.behavior_service:
                return

            # 获取敏感文件
            sensitive_files = self.behavior_service.get_sensitive_files(limit=100)
            self.current_sensitive_files = sensitive_files
            self.window.after(0, self._update_sensitive_files_ui)

        except Exception as e:
            print(f"加载敏感文件失败: {e}")
            self.current_sensitive_files = []
            self.window.after(0, self._update_sensitive_files_ui)

    def _update_sensitive_files_ui(self):
        """更新敏感文件UI"""
        # 清空现有数据
        for item in self.sensitive_files_tree.get_children():
            self.sensitive_files_tree.delete(item)

        # 添加敏感文件数据
        for sf in getattr(self, 'current_sensitive_files', []):
            values = (
                sf.get('id', ''),
                sf.get('file_path', ''),
                sf.get('reason', ''),
                sf.get('severity', ''),
                '是' if sf.get('auto_detected') else '否',
                sf.get('created_at', '')
            )
            self.sensitive_files_tree.insert('', 'end', values=values)

    def load_blocked_keywords(self):
        """加载屏蔽关键词"""
        try:
            if not self.behavior_service:
                return

            # 获取屏蔽关键词
            blocked_keywords = self.behavior_service.get_blocked_keywords(limit=100)
            self.current_blocked_keywords = blocked_keywords
            self.window.after(0, self._update_blocked_keywords_ui)

        except Exception as e:
            print(f"加载屏蔽关键词失败: {e}")
            self.current_blocked_keywords = []
            self.window.after(0, self._update_blocked_keywords_ui)

    def _update_blocked_keywords_ui(self):
        """更新屏蔽关键词UI"""
        # 清空现有数据
        for item in self.blocked_keywords_tree.get_children():
            self.blocked_keywords_tree.delete(item)

        # 英文到中文的映射
        keyword_type_map = {
            "exact": "精确匹配",
            "partial": "部分匹配",
            "regex": "正则表达式"
        }

        severity_map = {
            "low": "低",
            "medium": "中等",
            "high": "高",
            "critical": "严重"
        }

        # 添加屏蔽关键词数据
        for kw in getattr(self, 'current_blocked_keywords', []):
            values = (
                kw.get('id', ''),
                kw.get('keyword', ''),
                keyword_type_map.get(kw.get('keyword_type', ''), kw.get('keyword_type', '')),
                '是' if kw.get('is_regex') else '否',
                severity_map.get(kw.get('severity', ''), kw.get('severity', '')),
                '是' if kw.get('is_active') else '否',
                kw.get('hit_count', 0),
                kw.get('created_at', '')
            )
            self.blocked_keywords_tree.insert('', 'end', values=values)

    def load_user_quotas(self):
        """加载用户配额 - 已禁用"""
        # 用户配额功能已被移除
        pass

    def _update_user_quotas_ui(self):
        """更新用户配额UI - 已禁用"""
        # 用户配额功能已被移除
        pass

    def load_user_bans(self):
        """加载用户封禁"""
        try:
            if not self.behavior_service:
                return

            # 获取用户封禁
            user_bans = self.behavior_service.get_user_bans(limit=100)
            self.current_user_bans = user_bans
            self.window.after(0, self._update_user_bans_ui)

        except Exception as e:
            print(f"加载用户封禁失败: {e}")
            self.current_user_bans = []
            self.window.after(0, self._update_user_bans_ui)

    def _update_user_bans_ui(self):
        """更新用户封禁UI"""
        # 清空现有数据
        for item in self.user_bans_tree.get_children():
            self.user_bans_tree.delete(item)

        # 封禁类型中文映射
        ban_type_map = {
            'login': '登录封禁',
            'search': '搜索封禁',
            'download': '下载封禁',
            'all': '全部封禁'
        }

        # 添加用户封禁数据
        for ban in getattr(self, 'current_user_bans', []):
            # 转换封禁类型为中文
            ban_type_cn = ban_type_map.get(ban.get('ban_type', ''), ban.get('ban_type', ''))

            # 计算状态
            status = self.get_ban_status_text(ban)

            # 格式化时间显示为中国时区
            banned_at = self.format_china_time(ban.get('banned_at', ''))
            expires_at = self.format_china_time(ban.get('expires_at', '')) if ban.get('expires_at') else '永久'

            # 匹配列定义: ("ID", "用户ID", "用户名", "封禁类型", "原因", "状态", "封禁时间", "到期时间")
            values = (
                ban.get('id', ''),
                ban.get('user_id', ''),
                ban.get('username', ''),
                ban_type_cn,
                ban.get('reason', ''),
                status,
                banned_at,
                expires_at
            )
            self.user_bans_tree.insert('', 'end', values=values)

    # ==================== 管理功能方法 ====================

    def add_sensitive_file(self):
        """添加敏感文件"""
        dialog = SensitiveFileDialog(self.window, "添加敏感文件")
        if dialog.result:
            try:
                result = self.behavior_service.add_sensitive_file(
                    file_path=dialog.result['file_path'],
                    reason=dialog.result['reason'],
                    severity=dialog.result['severity']
                )
                if result:
                    messagebox.showinfo("成功", "敏感文件添加成功")
                    self.load_sensitive_files()
                else:
                    messagebox.showerror("错误", "添加敏感文件失败")
            except Exception as e:
                messagebox.showerror("错误", f"添加敏感文件失败: {e}")

    def edit_sensitive_file(self):
        """编辑敏感文件"""
        selection = self.sensitive_files_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的敏感文件")
            return

        # 获取选中项的数据
        item = self.sensitive_files_tree.item(selection[0])
        values = item['values']

        dialog = SensitiveFileDialog(self.window, "编辑敏感文件", values)
        if dialog.result:
            try:
                # 这里应该调用服务更新敏感文件
                messagebox.showinfo("成功", "敏感文件更新成功")
                self.load_sensitive_files()
            except Exception as e:
                messagebox.showerror("错误", f"更新敏感文件失败: {e}")

    def delete_sensitive_file(self):
        """删除敏感文件"""
        selection = self.sensitive_files_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的敏感文件")
            return

        item = self.sensitive_files_tree.item(selection[0])
        values = item['values']
        file_id = values[0]

        result = messagebox.askyesno("确认", "确定要删除选中的敏感文件吗？")
        if result:
            try:
                success = self.behavior_service.delete_sensitive_file(int(file_id))
                if success:
                    messagebox.showinfo("成功", "敏感文件删除成功")
                    self.load_sensitive_files()
                else:
                    messagebox.showerror("错误", "删除敏感文件失败")
            except Exception as e:
                messagebox.showerror("错误", f"删除敏感文件失败: {e}")

    def add_blocked_keyword(self):
        """添加屏蔽关键词"""
        print("🔧 开始添加屏蔽关键词...")
        print(f"📋 检查self.window: {self.window}")

        if not self.window:
            print("❌ self.window为None，无法创建对话框")
            messagebox.showerror("错误", "窗口未正确初始化")
            return

        try:
            print("📋 创建BlockedKeywordDialog实例...")
            dialog = BlockedKeywordDialog(self.window, "添加屏蔽关键词")
            print(f"✅ 对话框创建完成，结果: {dialog.result}")
            print(f"📋 对话框对象: {dialog}")
            print(f"📋 对话框类型: {type(dialog)}")

            # 检查对话框的属性
            if hasattr(dialog, 'result'):
                print(f"📋 对话框有result属性: {dialog.result}")
            else:
                print("❌ 对话框没有result属性")

            if hasattr(dialog, 'dialog'):
                print(f"📋 对话框有dialog属性: {dialog.dialog}")
            else:
                print("❌ 对话框没有dialog属性")

            if dialog.result:
                print(f"📋 对话框返回结果: {dialog.result}")

                print("📤 调用behavior_service.add_blocked_keyword...")
                result = self.behavior_service.add_blocked_keyword(
                    keyword=dialog.result['keyword'],
                    keyword_type=dialog.result['keyword_type'],
                    is_regex=dialog.result['is_regex'],
                    severity=dialog.result['severity'],
                    is_active=dialog.result['is_active'],
                    description=dialog.result.get('description', ''),
                    block_search=dialog.result.get('block_search', True),
                    block_download=dialog.result.get('block_download', False)
                )

                print(f"📋 添加结果: {result}")
                if result:
                    messagebox.showinfo("成功", "屏蔽关键词添加成功")
                    self.load_blocked_keywords()
                else:
                    messagebox.showerror("错误", "添加屏蔽关键词失败（可能已存在）")
            else:
                print("对话框被取消或没有返回结果")
        except Exception as e:
            print(f"❌ 添加屏蔽关键词异常: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"添加屏蔽关键词失败: {e}")

    def edit_blocked_keyword(self):
        """编辑屏蔽关键词"""
        selection = self.blocked_keywords_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的屏蔽关键词")
            return

        item = self.blocked_keywords_tree.item(selection[0])
        values = item['values']
        keyword_id = values[0]

        dialog = BlockedKeywordDialog(self.window, "编辑屏蔽关键词", values)
        if dialog.result:
            try:
                success = self.behavior_service.update_blocked_keyword(
                    int(keyword_id),
                    **dialog.result
                )
                if success:
                    messagebox.showinfo("成功", "屏蔽关键词更新成功")
                    self.load_blocked_keywords()
                else:
                    messagebox.showerror("错误", "更新屏蔽关键词失败")
            except Exception as e:
                messagebox.showerror("错误", f"更新屏蔽关键词失败: {e}")

    def delete_blocked_keyword(self):
        """删除屏蔽关键词"""
        selection = self.blocked_keywords_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的屏蔽关键词")
            return

        item = self.blocked_keywords_tree.item(selection[0])
        values = item['values']
        keyword_id = values[0]

        result = messagebox.askyesno("确认", "确定要删除选中的屏蔽关键词吗？")
        if result:
            try:
                success = self.behavior_service.delete_blocked_keyword(int(keyword_id))
                if success:
                    messagebox.showinfo("成功", "屏蔽关键词删除成功")
                    self.load_blocked_keywords()
                else:
                    messagebox.showerror("错误", "删除屏蔽关键词失败")
            except Exception as e:
                messagebox.showerror("错误", f"删除屏蔽关键词失败: {e}")

    def import_blocked_keywords(self):
        """批量导入屏蔽关键词"""
        dialog = BatchImportDialog(self.window)
        if dialog.result:
            try:
                success_count = 0
                error_count = 0

                for keyword_data in dialog.result:
                    try:
                        success = self.behavior_service.add_blocked_keyword(**keyword_data)
                        if success:
                            success_count += 1
                        else:
                            error_count += 1
                    except Exception as e:
                        print(f"导入关键词失败: {keyword_data['keyword']} - {e}")
                        error_count += 1

                message = f"导入完成！\n成功: {success_count} 个\n失败: {error_count} 个"
                messagebox.showinfo("导入结果", message)

                if success_count > 0:
                    self.load_blocked_keywords()

            except Exception as e:
                messagebox.showerror("错误", f"批量导入失败: {e}")

    def create_blocked_keywords_context_menu(self):
        """创建屏蔽关键词右键菜单"""
        self.keywords_context_menu = tk.Menu(self.window, tearoff=0)
        self.keywords_context_menu.add_command(label="编辑", command=self.edit_blocked_keyword)
        self.keywords_context_menu.add_command(label="删除", command=self.delete_blocked_keyword)
        self.keywords_context_menu.add_separator()
        self.keywords_context_menu.add_command(label="查看相关搜索记录", command=self.view_keyword_search_records)
        self.keywords_context_menu.add_command(label="启用/禁用", command=self.toggle_keyword_status)

        # 绑定右键事件
        self.blocked_keywords_tree.bind("<Button-3>", self.show_keywords_context_menu)

    def show_keywords_context_menu(self, event):
        """显示屏蔽关键词右键菜单"""
        try:
            # 选中右键点击的项
            item = self.blocked_keywords_tree.identify_row(event.y)
            if item:
                self.blocked_keywords_tree.selection_set(item)
                self.keywords_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"显示屏蔽关键词右键菜单失败: {e}")

    def view_keyword_search_records(self):
        """查看关键词相关的搜索记录"""
        selection = self.blocked_keywords_tree.selection()
        if not selection:
            return

        item = self.blocked_keywords_tree.item(selection[0])
        values = item['values']
        keyword = values[1]

        # 切换到搜索记录选项卡并过滤
        self.notebook.select(1)  # 搜索记录是第二个选项卡

        # 加载包含该关键词的搜索记录
        try:
            if self.behavior_service:
                search_records = self.behavior_service.get_search_records_filtered(
                    limit=100,
                    search_query=keyword
                )
                self.current_search_records = search_records
                self.window.after(0, self._update_search_records_ui)

                messagebox.showinfo("信息", f"已切换到搜索记录，显示包含关键词 '{keyword}' 的搜索记录")
        except Exception as e:
            messagebox.showerror("错误", f"查看相关搜索记录失败: {e}")

    def toggle_keyword_status(self):
        """切换关键词启用状态"""
        selection = self.blocked_keywords_tree.selection()
        if not selection:
            return

        item = self.blocked_keywords_tree.item(selection[0])
        values = item['values']
        keyword_id = values[0]
        current_status = values[5] == '是'

        try:
            success = self.behavior_service.update_blocked_keyword(
                int(keyword_id),
                is_active=not current_status
            )
            if success:
                status_text = "禁用" if current_status else "启用"
                messagebox.showinfo("成功", f"关键词已{status_text}")
                self.load_blocked_keywords()
            else:
                messagebox.showerror("错误", "更新关键词状态失败")
        except Exception as e:
            messagebox.showerror("错误", f"更新关键词状态失败: {e}")

    def import_blocked_keywords(self):
        """批量导入屏蔽关键词"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择关键词文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 这里应该实现批量导入逻辑
                messagebox.showinfo("成功", "屏蔽关键词导入成功")
                self.load_blocked_keywords()
            except Exception as e:
                messagebox.showerror("错误", f"导入屏蔽关键词失败: {e}")

    def set_user_quota(self):
        """设置用户配额 - 已禁用"""
        # 用户配额功能已被移除
        pass

    def reset_user_quota(self):
        """重置用户配额 - 已禁用"""
        # 用户配额功能已被移除
        pass

    def batch_set_quotas(self):
        """批量设置配额 - 已禁用"""
        # 用户配额功能已被移除
        pass

    # ==================== 事件处理方法 ====================

    def create_online_users_context_menu(self):
        """创建在线用户右键菜单 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def show_online_users_menu(self, event):
        """显示在线用户右键菜单 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def view_user_details(self):
        """查看用户详情 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def force_logout_user(self):
        """强制用户下线 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def ban_selected_user(self):
        """封禁选中的用户 - 已禁用"""
        # 在线用户功能已被移除
        pass

    def show_ban_dialog(self, user_id, username):
        """显示封禁对话框"""
        ban_window = tk.Toplevel(self.window)
        ban_window.title(f"封禁用户 - {username}")
        ban_window.geometry("400x300")
        ban_window.transient(self.window)

        # 封禁类型
        ttk.Label(ban_window, text="封禁类型:").pack(pady=5)
        ban_type_var = tk.StringVar(value="login")
        ban_type_frame = ttk.Frame(ban_window)
        ban_type_frame.pack(pady=5)

        ttk.Radiobutton(ban_type_frame, text="禁止登录", variable=ban_type_var, value="login").pack(side=tk.LEFT)
        ttk.Radiobutton(ban_type_frame, text="禁止搜索", variable=ban_type_var, value="search").pack(side=tk.LEFT)
        ttk.Radiobutton(ban_type_frame, text="禁止下载", variable=ban_type_var, value="download").pack(side=tk.LEFT)
        ttk.Radiobutton(ban_type_frame, text="全部禁止", variable=ban_type_var, value="all").pack(side=tk.LEFT)

        # 封禁时长
        ttk.Label(ban_window, text="封禁时长(小时):").pack(pady=5)
        duration_var = tk.StringVar(value="24")
        duration_spinbox = ttk.Spinbox(ban_window, from_=1, to=8760, textvariable=duration_var, width=10)
        duration_spinbox.pack(pady=5)

        # 封禁原因
        ttk.Label(ban_window, text="封禁原因:").pack(pady=5)
        reason_text = tk.Text(ban_window, height=5, width=40)
        reason_text.pack(pady=5, padx=10, fill=tk.X)

        # 按钮
        button_frame = ttk.Frame(ban_window)
        button_frame.pack(pady=10)

        def confirm_ban():
            try:
                ban_type = ban_type_var.get()
                duration = int(duration_var.get())
                reason = reason_text.get(1.0, tk.END).strip()

                if self.behavior_service:
                    result = self.behavior_service.ban_user(
                        user_id=int(user_id),
                        ban_type=ban_type,
                        duration_hours=duration,
                        reason=reason
                    )

                    if result.get('success'):
                        messagebox.showinfo("成功", f"用户 {username} 已被封禁")
                        ban_window.destroy()
                        self.load_user_bans()
                        self.load_online_users()
                    else:
                        messagebox.showerror("错误", f"封禁失败: {result.get('error')}")
                else:
                    messagebox.showerror("错误", "用户行为服务不可用")

            except Exception as e:
                messagebox.showerror("错误", f"封禁失败: {e}")

        ttk.Button(button_frame, text="确认封禁", command=confirm_ban).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=ban_window.destroy).pack(side=tk.LEFT, padx=5)

    def apply_search_filter(self):
        """应用搜索过滤"""
        try:
            # 获取过滤条件
            user_filter = getattr(self, 'search_user_var', None)
            time_filter = getattr(self, 'search_time_var', None)
            sensitive_filter = getattr(self, 'search_sensitive_var', None)
            blocked_filter = getattr(self, 'search_blocked_var', None)

            # 构建过滤参数
            filter_params = {}

            if user_filter and user_filter.get().strip():
                filter_params['username'] = user_filter.get().strip()

            if time_filter and time_filter.get() != '全部':
                # 将中文时间选项映射为英文值
                time_map = {
                    "今日": "today",
                    "本周": "week",
                    "本月": "month",
                    "全部": "all"
                }
                filter_params['date_filter'] = time_map.get(time_filter.get(), "today")

            if sensitive_filter and sensitive_filter.get():
                filter_params['only_sensitive'] = True

            if blocked_filter and blocked_filter.get():
                filter_params['only_blocked'] = True

            # 加载过滤后的搜索记录
            self.load_search_records_with_filter(filter_params)

        except Exception as e:
            print(f"应用搜索过滤失败: {e}")
            self.load_search_records()

    def load_search_records_with_filter(self, filter_params: dict):
        """加载带过滤条件的搜索记录"""
        try:
            if not self.behavior_service:
                return

            # 获取过滤后的搜索记录
            search_records = self.behavior_service.get_search_records_filtered(
                limit=100,
                **filter_params
            )
            self.current_search_records = search_records
            self.window.after(0, self._update_search_records_ui)

        except Exception as e:
            print(f"加载过滤搜索记录失败: {e}")
            # 如果过滤失败，回退到普通加载
            self.load_search_records()

    def load_user_list_for_filter(self):
        """加载用户列表用于过滤"""
        try:
            if not self.behavior_service:
                return

            # 获取用户服务
            user_service = self.server.services.get('user')
            if user_service:
                result = user_service.get_user_list(page=1, page_size=1000)
                if result.get('success', False):
                    users = result.get('users', [])
                    usernames = [''] + [user.get('username', '') for user in users]
                    self.search_user_combo['values'] = usernames
                else:
                    # 如果用户服务失败，直接从数据库获取
                    self.load_user_list_from_db()
            else:
                self.load_user_list_from_db()

        except Exception as e:
            print(f"加载用户列表失败: {e}")
            self.load_user_list_from_db()

    def load_user_list_from_db(self):
        """直接从数据库加载用户列表"""
        try:
            import sqlite3
            import os

            db_path = os.path.join('data', 'file_share_system.db')
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute('SELECT username FROM users ORDER BY username')
                users = cursor.fetchall()
                conn.close()

                usernames = [''] + [user[0] for user in users]
                self.search_user_combo['values'] = usernames

        except Exception as e:
            print(f"从数据库加载用户列表失败: {e}")
            self.search_user_combo['values'] = ['']

    def create_search_records_context_menu(self):
        """创建搜索记录右键菜单"""
        self.search_context_menu = tk.Menu(self.window, tearoff=0)
        self.search_context_menu.add_command(label="查看详情", command=self.view_search_record_details)
        self.search_context_menu.add_command(label="标记为敏感", command=self.mark_search_as_sensitive)
        self.search_context_menu.add_command(label="添加到屏蔽词", command=self.add_to_blocked_keywords)
        self.search_context_menu.add_separator()
        self.search_context_menu.add_command(label="删除记录", command=self.delete_search_record)

        # 绑定右键事件
        self.search_records_tree.bind("<Button-3>", self.show_search_context_menu)

    def show_search_context_menu(self, event):
        """显示搜索记录右键菜单"""
        try:
            # 选中右键点击的项
            item = self.search_records_tree.identify_row(event.y)
            if item:
                self.search_records_tree.selection_set(item)
                self.search_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"显示搜索记录右键菜单失败: {e}")

    def view_search_record_details(self):
        """查看搜索记录详情"""
        selection = self.search_records_tree.selection()
        if not selection:
            return

        item = self.search_records_tree.item(selection[0])
        values = item['values']

        # 创建详情窗口
        detail_window = tk.Toplevel(self.window)
        detail_window.title("搜索记录详情")
        detail_window.geometry("500x400")
        detail_window.transient(self.window)

        # 创建详情内容
        main_frame = ttk.Frame(detail_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 详情信息
        details = [
            ("记录ID", values[0]),
            ("用户", values[1]),
            ("搜索内容", values[2]),
            ("结果数量", values[3]),
            ("是否敏感", values[4]),
            ("是否屏蔽", values[5]),
            ("屏蔽原因", values[6]),
            ("IP地址", values[7]),
            ("搜索时间", values[8])
        ]

        for i, (label, value) in enumerate(details):
            ttk.Label(main_frame, text=f"{label}:", font=("Arial", 10, "bold")).grid(
                row=i, column=0, sticky=tk.W, pady=5, padx=(0, 10))
            ttk.Label(main_frame, text=str(value), wraplength=300).grid(
                row=i, column=1, sticky=tk.W, pady=5)

        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=detail_window.destroy).grid(
            row=len(details), column=0, columnspan=2, pady=20)

    def mark_search_as_sensitive(self):
        """标记搜索为敏感"""
        selection = self.search_records_tree.selection()
        if not selection:
            return

        item = self.search_records_tree.item(selection[0])
        values = item['values']
        record_id = values[0]

        try:
            # 这里可以调用服务方法标记为敏感
            messagebox.showinfo("成功", f"搜索记录 {record_id} 已标记为敏感")
            self.load_search_records()  # 刷新列表
        except Exception as e:
            messagebox.showerror("错误", f"标记失败: {e}")

    def add_to_blocked_keywords(self):
        """添加到屏蔽关键词"""
        selection = self.search_records_tree.selection()
        if not selection:
            return

        item = self.search_records_tree.item(selection[0])
        values = item['values']
        search_query = values[2]

        # 创建添加屏蔽关键词对话框，预填充搜索内容
        dialog = BlockedKeywordDialog(self.window, "添加屏蔽关键词")
        dialog.keyword_var.set(search_query)  # 预填充关键词

        if dialog.result:
            try:
                success = self.behavior_service.add_blocked_keyword(**dialog.result)
                if success:
                    messagebox.showinfo("成功", f"'{search_query}' 已添加到屏蔽关键词")
                    self.load_blocked_keywords()  # 刷新屏蔽关键词列表
                else:
                    messagebox.showerror("错误", "添加屏蔽关键词失败（可能已存在）")
            except Exception as e:
                messagebox.showerror("错误", f"添加失败: {e}")

    def delete_search_record(self):
        """删除搜索记录"""
        selection = self.search_records_tree.selection()
        if not selection:
            return

        item = self.search_records_tree.item(selection[0])
        values = item['values']
        record_id = values[0]
        search_query = values[2]

        result = messagebox.askyesno("确认", f"确定要删除搜索记录 '{search_query}' 吗？\n此操作不可撤销！")
        if result:
            try:
                # 这里可以调用服务方法删除记录
                messagebox.showinfo("成功", "搜索记录已删除")
                self.load_search_records()  # 刷新列表
            except Exception as e:
                messagebox.showerror("错误", f"删除失败: {e}")

    def update_rankings(self):
        """更新排行榜"""
        self.load_rankings()

    def toggle_auto_refresh(self):
        """切换自动刷新"""
        self.auto_refresh_enabled = self.auto_refresh_var.get()
        if self.auto_refresh_enabled:
            self.start_auto_refresh()

    def update_refresh_interval(self):
        """更新刷新间隔"""
        try:
            interval = int(self.refresh_interval_var.get())
            self.refresh_interval = interval * 1000  # 转换为毫秒
        except ValueError:
            pass

    def start_auto_refresh(self):
        """开始自动刷新"""
        if self.auto_refresh_enabled and self.window:
            # 在线用户功能已被移除，不再自动刷新
            self.window.after(self.refresh_interval, self.start_auto_refresh)

    # ==================== 管理功能方法 ====================

    def add_sensitive_file(self):
        """添加敏感文件"""
        messagebox.showinfo("提示", "添加敏感文件功能正在开发中...")

    def edit_sensitive_file(self):
        """编辑敏感文件"""
        messagebox.showinfo("提示", "编辑敏感文件功能正在开发中...")

    def delete_sensitive_file(self):
        """删除敏感文件"""
        messagebox.showinfo("提示", "删除敏感文件功能正在开发中...")



    # 用户配额相关方法已被移除

    def ban_user(self):
        """封禁用户"""
        self.show_ban_user_dialog()

    def unban_user(self):
        """解封用户"""
        selection = self.user_bans_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要解封的用户")
            return

        item = self.user_bans_tree.item(selection[0])
        values = item['values']
        ban_id = values[0]
        username = values[2]
        ban_type = values[3]

        # 检查封禁记录状态
        status = values[5]  # 状态列
        if status == "已解封":
            messagebox.showwarning("警告", "该封禁记录已经解封")
            return

        # 确认解封
        if messagebox.askyesno("确认", f"确定要解封用户 {username} 的 {ban_type} 权限吗？"):
            try:
                if self.behavior_service:
                    result = self.behavior_service.unban_user(
                        user_id=int(values[1]),
                        ban_type=ban_type
                    )

                    if result.get('success'):
                        messagebox.showinfo("成功", f"用户 {username} 已解封")
                        self.load_user_bans()
                    else:
                        messagebox.showerror("错误", f"解封失败: {result.get('error')}")
                else:
                    messagebox.showerror("错误", "用户行为服务不可用")
            except Exception as e:
                messagebox.showerror("错误", f"解封失败: {e}")

    def view_ban_details(self):
        """查看封禁详情"""
        selection = self.user_bans_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要查看的封禁记录")
            return

        item = self.user_bans_tree.item(selection[0])
        values = item['values']
        ban_id = values[0]

        self.show_ban_details_dialog(ban_id)

    def export_report(self):
        """导出报告"""
        messagebox.showinfo("提示", "导出报告功能正在开发中...")

    def show_ban_user_dialog(self):
        """显示封禁用户对话框"""
        ban_window = tk.Toplevel(self.window)
        ban_window.title("封禁用户")
        ban_window.geometry("500x600")
        ban_window.transient(self.window)
        ban_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(ban_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 用户选择
        ttk.Label(main_frame, text="用户ID或用户名:").pack(anchor=tk.W, pady=(0, 5))
        user_var = tk.StringVar()
        user_entry = ttk.Entry(main_frame, textvariable=user_var, width=40)
        user_entry.pack(fill=tk.X, pady=(0, 10))

        # 封禁类型
        ttk.Label(main_frame, text="封禁类型:").pack(anchor=tk.W, pady=(0, 5))
        ban_type_var = tk.StringVar(value="login")
        ban_type_frame = ttk.Frame(main_frame)
        ban_type_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Radiobutton(ban_type_frame, text="禁止登录", variable=ban_type_var, value="login").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ban_type_frame, text="禁止搜索", variable=ban_type_var, value="search").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ban_type_frame, text="禁止下载", variable=ban_type_var, value="download").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(ban_type_frame, text="全部禁止", variable=ban_type_var, value="all").pack(side=tk.LEFT)

        # 封禁时长
        duration_frame = ttk.LabelFrame(main_frame, text="封禁时长", padding=10)
        duration_frame.pack(fill=tk.X, pady=(0, 10))

        is_permanent_var = tk.BooleanVar()
        permanent_check = ttk.Checkbutton(duration_frame, text="永久封禁", variable=is_permanent_var)
        permanent_check.pack(anchor=tk.W, pady=(0, 10))

        duration_var = tk.StringVar(value="24")
        duration_label = ttk.Label(duration_frame, text="封禁时长(小时):")
        duration_label.pack(anchor=tk.W)
        duration_spinbox = ttk.Spinbox(duration_frame, from_=1, to=8760, textvariable=duration_var, width=10)
        duration_spinbox.pack(anchor=tk.W, pady=(5, 0))

        def on_permanent_change():
            if is_permanent_var.get():
                duration_label.config(state='disabled')
                duration_spinbox.config(state='disabled')
            else:
                duration_label.config(state='normal')
                duration_spinbox.config(state='normal')

        permanent_check.config(command=on_permanent_change)

        # 封禁原因
        ttk.Label(main_frame, text="封禁原因:").pack(anchor=tk.W, pady=(0, 5))
        reason_text = tk.Text(main_frame, height=4, width=50)
        reason_text.pack(fill=tk.X, pady=(0, 10))

        # 管理员备注
        ttk.Label(main_frame, text="管理员备注:").pack(anchor=tk.W, pady=(0, 5))
        note_text = tk.Text(main_frame, height=3, width=50)
        note_text.pack(fill=tk.X, pady=(0, 20))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def confirm_ban():
            try:
                user_input = user_var.get().strip()
                if not user_input:
                    messagebox.showerror("错误", "请输入用户ID或用户名")
                    return

                # 尝试解析为用户ID，如果失败则作为用户名处理
                try:
                    user_id = int(user_input)
                except ValueError:
                    # 根据用户名查找用户ID
                    user_id = self.get_user_id_by_username(user_input)
                    if not user_id:
                        messagebox.showerror("错误", f"用户 {user_input} 不存在")
                        return

                ban_type = ban_type_var.get()
                is_permanent = is_permanent_var.get()
                duration = 0 if is_permanent else int(duration_var.get())
                reason = reason_text.get(1.0, tk.END).strip()
                admin_note = note_text.get(1.0, tk.END).strip()

                if self.behavior_service:
                    result = self.behavior_service.ban_user(
                        user_id=user_id,
                        ban_type=ban_type,
                        duration_hours=duration,
                        reason=reason,
                        is_permanent=is_permanent,
                        admin_note=admin_note
                    )

                    if result.get('success'):
                        messagebox.showinfo("成功", "用户封禁成功")
                        ban_window.destroy()
                        self.load_user_bans()
                    else:
                        messagebox.showerror("错误", f"封禁失败: {result.get('error')}")
                else:
                    messagebox.showerror("错误", "用户行为服务不可用")

            except Exception as e:
                messagebox.showerror("错误", f"封禁失败: {e}")

        ttk.Button(button_frame, text="确认封禁", command=confirm_ban).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=ban_window.destroy).pack(side=tk.LEFT)

    def get_user_id_by_username(self, username: str) -> int:
        """根据用户名获取用户ID"""
        try:
            if hasattr(self.server, 'db_manager') and self.server.db_manager:
                with self.server.db_manager.get_session() as session:
                    from models.user import User
                    user = session.query(User).filter(User.username == username).first()
                    return user.id if user else None
        except Exception as e:
            self.logger.error(f"查找用户失败: {e}")
            return None

    def show_ban_details_dialog(self, ban_id: int):
        """显示封禁详情对话框"""
        try:
            # 获取封禁详情
            if not self.behavior_service:
                messagebox.showerror("错误", "用户行为服务不可用")
                return

            with self.server.db_manager.get_session() as session:
                from models.user_quota import UserBan
                ban = session.query(UserBan).filter(UserBan.id == ban_id).first()

                if not ban:
                    messagebox.showerror("错误", "封禁记录不存在")
                    return

                # 创建详情窗口
                details_window = tk.Toplevel(self.window)
                details_window.title(f"封禁详情 - ID: {ban_id}")
                details_window.geometry("600x500")
                details_window.transient(self.window)
                details_window.grab_set()

                # 居中显示
                details_window.update_idletasks()
                x = (details_window.winfo_screenwidth() // 2) - (600 // 2)
                y = (details_window.winfo_screenheight() // 2) - (500 // 2)
                details_window.geometry(f"600x500+{x}+{y}")

                # 主框架
                main_frame = ttk.Frame(details_window)
                main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

                # 基本信息
                info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
                info_frame.pack(fill=tk.X, pady=(0, 10))

                # 封禁类型中文映射
                ban_type_map = {
                    'login': '登录封禁',
                    'search': '搜索封禁',
                    'download': '下载封禁',
                    'all': '全部封禁'
                }
                ban_type_cn = ban_type_map.get(ban.ban_type, ban.ban_type)

                ttk.Label(info_frame, text=f"封禁ID: {ban.id}").pack(anchor=tk.W)
                ttk.Label(info_frame, text=f"用户ID: {ban.user_id}").pack(anchor=tk.W)
                ttk.Label(info_frame, text=f"用户名: {ban.user.username if ban.user else '未知'}").pack(anchor=tk.W)
                ttk.Label(info_frame, text=f"封禁类型: {ban_type_cn}").pack(anchor=tk.W)
                ttk.Label(info_frame, text=f"状态: {ban.get_ban_status_text()}").pack(anchor=tk.W)

                # 时间信息
                time_frame = ttk.LabelFrame(main_frame, text="时间信息", padding=10)
                time_frame.pack(fill=tk.X, pady=(0, 10))

                ttk.Label(time_frame, text=f"封禁时间: {ban.banned_at.strftime('%Y-%m-%d %H:%M:%S') if ban.banned_at else '未知'}").pack(anchor=tk.W)

                if ban.is_permanent:
                    ttk.Label(time_frame, text="到期时间: 永久封禁").pack(anchor=tk.W)
                else:
                    ttk.Label(time_frame, text=f"到期时间: {ban.expires_at.strftime('%Y-%m-%d %H:%M:%S') if ban.expires_at else '未知'}").pack(anchor=tk.W)

                if ban.unbanned_at:
                    ttk.Label(time_frame, text=f"解封时间: {ban.unbanned_at.strftime('%Y-%m-%d %H:%M:%S')}").pack(anchor=tk.W)

                # 原因和备注
                reason_frame = ttk.LabelFrame(main_frame, text="原因和备注", padding=10)
                reason_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

                ttk.Label(reason_frame, text="封禁原因:").pack(anchor=tk.W)
                reason_text = tk.Text(reason_frame, height=4, state='disabled')
                reason_text.pack(fill=tk.X, pady=(5, 10))
                reason_text.config(state='normal')
                reason_text.insert(1.0, ban.reason or '无')
                reason_text.config(state='disabled')

                ttk.Label(reason_frame, text="管理员备注:").pack(anchor=tk.W)
                note_text = tk.Text(reason_frame, height=3, state='disabled')
                note_text.pack(fill=tk.X, pady=(5, 0))
                note_text.config(state='normal')
                note_text.insert(1.0, ban.admin_note or '无')
                note_text.config(state='disabled')

                # 操作按钮
                button_frame = ttk.Frame(main_frame)
                button_frame.pack(fill=tk.X)

                if ban.is_active and not ban.is_expired():
                    if not ban.is_permanent:
                        ttk.Button(button_frame, text="延长封禁",
                                 command=lambda: self.show_extend_ban_dialog(ban_id, details_window)).pack(side=tk.LEFT, padx=(0, 10))
                        ttk.Button(button_frame, text="设为永久",
                                 command=lambda: self.make_ban_permanent(ban_id, details_window)).pack(side=tk.LEFT, padx=(0, 10))

                    ttk.Button(button_frame, text="立即解封",
                             command=lambda: self.unban_immediately(ban.user_id, ban.ban_type, details_window)).pack(side=tk.LEFT, padx=(0, 10))

                ttk.Button(button_frame, text="关闭", command=details_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"获取封禁详情失败: {e}")

    def show_extend_ban_dialog(self, ban_id: int, parent_window):
        """显示延长封禁对话框"""
        extend_window = tk.Toplevel(parent_window)
        extend_window.title("延长封禁时间")
        extend_window.geometry("300x200")
        extend_window.transient(parent_window)
        extend_window.grab_set()

        # 居中显示
        extend_window.update_idletasks()
        x = (extend_window.winfo_screenwidth() // 2) - (300 // 2)
        y = (extend_window.winfo_screenheight() // 2) - (200 // 2)
        extend_window.geometry(f"300x200+{x}+{y}")

        main_frame = ttk.Frame(extend_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        ttk.Label(main_frame, text="延长时间(小时):").pack(anchor=tk.W, pady=(0, 10))
        hours_var = tk.StringVar(value="24")
        hours_spinbox = ttk.Spinbox(main_frame, from_=1, to=8760, textvariable=hours_var, width=10)
        hours_spinbox.pack(anchor=tk.W, pady=(0, 20))

        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def confirm_extend():
            try:
                additional_hours = int(hours_var.get())
                result = self.behavior_service.extend_user_ban(ban_id, additional_hours)

                if result.get('success'):
                    messagebox.showinfo("成功", f"封禁时间已延长{additional_hours}小时")
                    extend_window.destroy()
                    parent_window.destroy()
                    self.load_user_bans()
                else:
                    messagebox.showerror("错误", f"延长失败: {result.get('error')}")
            except Exception as e:
                messagebox.showerror("错误", f"延长失败: {e}")

        ttk.Button(button_frame, text="确认", command=confirm_extend).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=extend_window.destroy).pack(side=tk.LEFT)

    def make_ban_permanent(self, ban_id: int, parent_window):
        """设为永久封禁"""
        if messagebox.askyesno("确认", "确定要将此封禁设为永久吗？"):
            try:
                result = self.behavior_service.make_ban_permanent(ban_id)

                if result.get('success'):
                    messagebox.showinfo("成功", "已设为永久封禁")
                    parent_window.destroy()
                    self.load_user_bans()
                else:
                    messagebox.showerror("错误", f"设置失败: {result.get('error')}")
            except Exception as e:
                messagebox.showerror("错误", f"设置失败: {e}")

    def unban_immediately(self, user_id: int, ban_type: str, parent_window):
        """立即解封"""
        if messagebox.askyesno("确认", f"确定要立即解封该用户的{ban_type}权限吗？"):
            try:
                result = self.behavior_service.unban_user(user_id, ban_type)

                if result.get('success'):
                    messagebox.showinfo("成功", "用户已解封")
                    parent_window.destroy()
                    self.load_user_bans()
                else:
                    messagebox.showerror("错误", f"解封失败: {result.get('error')}")
            except Exception as e:
                messagebox.showerror("错误", f"解封失败: {e}")

    def cleanup_old_data(self):
        """清理旧数据"""
        result = messagebox.askyesno("确认", "确定要清理30天前的旧数据吗？\n此操作不可撤销！")
        if result:
            try:
                if self.behavior_service:
                    cleanup_result = self.behavior_service.cleanup_old_records(30)
                    if cleanup_result.get('success'):
                        deleted_counts = cleanup_result.get('deleted_counts', {})
                        message = "数据清理完成:\n"
                        for key, count in deleted_counts.items():
                            message += f"- {key}: {count} 条记录\n"
                        messagebox.showinfo("成功", message)
                        self.load_all_data()
                    else:
                        messagebox.showerror("错误", f"清理失败: {cleanup_result.get('error')}")
                else:
                    messagebox.showerror("错误", "用户行为服务不可用")
            except Exception as e:
                messagebox.showerror("错误", f"清理失败: {e}")

    # ==================== 工具方法 ====================

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def on_closing(self):
        """窗口关闭事件"""
        self.auto_refresh_enabled = False
        self.window.destroy()
        self.window = None


# ==================== 对话框类 ====================

class BaseDialog:
    """基础对话框类"""

    def __init__(self, parent, title, data=None):
        self.parent = parent
        self.result = None
        self.data = data or {}

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("400x300")
        self.center_window()

        self.create_widgets()

    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def create_widgets(self):
        """创建控件 - 子类重写"""
        pass

    def ok_clicked(self):
        """确定按钮点击"""
        self.result = self.get_result()
        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()

    def get_result(self):
        """获取结果 - 子类重写"""
        return {}


class SensitiveFileDialog(BaseDialog):
    """敏感文件对话框"""

    def create_widgets(self):
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 文件路径
        ttk.Label(main_frame, text="文件路径:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.file_path_var = tk.StringVar(value=self.data.get('file_path', ''))
        ttk.Entry(main_frame, textvariable=self.file_path_var, width=40).grid(row=0, column=1, pady=5)

        # 敏感原因
        ttk.Label(main_frame, text="敏感原因:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.reason_var = tk.StringVar(value=self.data.get('reason', ''))
        ttk.Entry(main_frame, textvariable=self.reason_var, width=40).grid(row=1, column=1, pady=5)

        # 严重程度
        ttk.Label(main_frame, text="严重程度:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.severity_var = tk.StringVar(value=self.data.get('severity', 'medium'))
        severity_combo = ttk.Combobox(main_frame, textvariable=self.severity_var,
                                     values=['low', 'medium', 'high', 'critical'])
        severity_combo.grid(row=2, column=1, pady=5)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)

    def get_result(self):
        return {
            'file_path': self.file_path_var.get(),
            'reason': self.reason_var.get(),
            'severity': self.severity_var.get()
        }





# 用户配额对话框类已被移除
