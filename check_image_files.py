#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的图像文件记录
"""

import os
import sys

# 添加backend目录到Python路径
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def check_image_files():
    """检查数据库中的图像文件记录"""
    
    print("🔍 检查数据库中的图像文件记录")
    print("=" * 50)
    
    try:
        # 导入数据库管理器和模型
        from utils.database_manager import DatabaseManager
        from models.file_share import SharedFile, SharedFolder
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        with db_manager.get_session() as session:
            # 获取所有文件记录
            files = session.query(SharedFile).all()
            
            print(f"📊 数据库中共有 {len(files)} 个文件记录")
            print()
            
            for file_record in files:
                print(f"📄 文件: {file_record.filename}")
                print(f"  - ID: {file_record.id}")
                print(f"  - 扩展名: {file_record.extension}")
                print(f"  - 是否为图像: {file_record.is_image}")
                print(f"  - 是否为视频: {file_record.is_video}")
                print(f"  - 是否为文档: {file_record.is_document}")
                print(f"  - 文件大小: {file_record.file_size} 字节")
                print(f"  - 相对路径: {file_record.relative_path}")
                
                # 检查是否有图像特征
                if hasattr(file_record, 'has_image_features'):
                    print(f"  - 有图像特征: {file_record.has_image_features}")
                else:
                    print(f"  - 图像特征属性: 不存在")
                
                print()
            
            # 统计图像文件
            image_files = session.query(SharedFile).filter_by(is_image=True).all()
            print(f"🖼️ 图像文件数量: {len(image_files)}")
            
            if image_files:
                print("\n图像文件列表:")
                for img_file in image_files:
                    print(f"  - {img_file.filename} (扩展名: {img_file.extension})")
            
            # 检查图像特征索引
            try:
                from models.image_features import ImageFeatureIndex
                features = session.query(ImageFeatureIndex).all()
                print(f"\n🔬 图像特征索引数量: {len(features)}")
                
                if features:
                    print("\n特征索引列表:")
                    for feature in features:
                        print(f"  - 文件ID: {feature.file_id}, 特征哈希: {feature.feature_hash[:16]}...")
                        
            except Exception as e:
                print(f"\n❌ 检查图像特征索引失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_type_detection():
    """测试文件类型检测"""
    
    print("\n" + "=" * 50)
    print("🧪 测试文件类型检测")
    print("=" * 50)
    
    try:
        from models.file_share import SharedFile
        
        # 测试文件
        test_files = [
            "乡村振兴22.jpg",
            "test.png", 
            "document.pdf",
            "video.mp4",
            "image.jpeg",
            "photo.gif"
        ]
        
        for filename in test_files:
            # 创建临时文件记录
            temp_file = SharedFile(
                folder_id=1,
                filename=filename,
                relative_path=filename
            )
            
            # 手动设置扩展名和类型
            _, ext = os.path.splitext(filename)
            temp_file.extension = ext.lower()
            
            # 判断文件类型
            image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.psd', '.ai', '.eps', '.svg'}
            video_exts = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'}
            doc_exts = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}

            temp_file.is_image = temp_file.extension in image_exts
            temp_file.is_video = temp_file.extension in video_exts
            temp_file.is_document = temp_file.extension in doc_exts
            
            print(f"📄 {filename}:")
            print(f"  - 扩展名: {temp_file.extension}")
            print(f"  - 是否为图像: {temp_file.is_image}")
            print(f"  - 是否为视频: {temp_file.is_video}")
            print(f"  - 是否为文档: {temp_file.is_document}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🔍 开始检查图像文件记录")
    
    # 检查数据库记录
    success1 = check_image_files()
    
    # 测试文件类型检测
    success2 = test_file_type_detection()
    
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    print(f"  - 数据库记录检查: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  - 文件类型检测测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    input("\n按回车键退出...")
