#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像特征提取服务
"""

import os
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import threading
import queue

from models.image_features import ImageFeatures
from utils.logger import setup_logger

# 可选导入图像处理库
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False

try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

try:
    from PIL import Image
    HAS_PIL = True
except ImportError:
    HAS_PIL = False

class FeatureExtractionService:
    """图像特征提取服务"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = setup_logger("FeatureExtractionService")
        
        # 默认配置
        self.config = {
            'max_image_size': 800,  # 处理前将图像缩放到的最大尺寸
            'color_histogram_bins': 256,  # 颜色直方图的柱数
            'color_histogram_bins_reduced': 64,  # 降维后的颜色直方图柱数
            'hsv_histogram_bins': [50, 60, 60],  # HSV直方图的柱数 [H, S, V]
            'texture_lbp_points': 24,  # LBP纹理特征的采样点数
            'texture_lbp_radius': 3,  # LBP纹理特征的半径
            'edge_threshold1': 100,  # Canny边缘检测的低阈值
            'edge_threshold2': 200,  # Canny边缘检测的高阈值
            'feature_version': '1.0',  # 特征版本号
            'temp_directory': './temp/features',  # 临时文件目录
            'max_workers': 4,  # 最大工作线程数
            'supported_formats': {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'}
        }
        
        if config:
            self.config.update(config)
        
        # 创建临时目录
        self.temp_dir = Path(self.config['temp_directory'])
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查依赖
        self._check_dependencies()
        
        # 初始化异步处理队列和线程
        self.extraction_queue = queue.Queue()
        self.worker_threads = []
        self._initialize_workers()
        
        self.logger.info("特征提取服务初始化完成")
    
    def _check_dependencies(self):
        """检查依赖库"""
        missing_deps = []
        
        if not HAS_NUMPY:
            missing_deps.append("numpy")
        if not HAS_OPENCV:
            missing_deps.append("opencv-python")
        if not HAS_PIL:
            missing_deps.append("Pillow")
        
        if missing_deps:
            self.logger.warning(f"缺少依赖库: {', '.join(missing_deps)}")
            self.logger.warning("特征提取功能将受限，请安装缺少的依赖库")
        else:
            self.logger.info("所有依赖库检查通过")
    
    def _initialize_workers(self):
        """初始化工作线程"""
        num_workers = min(self.config['max_workers'], os.cpu_count() or 2)
        
        for i in range(num_workers):
            thread = threading.Thread(
                target=self._worker_loop,
                name=f"FeatureExtractor-{i}",
                daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)
        
        self.logger.info(f"初始化 {num_workers} 个特征提取工作线程")
    
    def _worker_loop(self):
        """工作线程循环"""
        while True:
            try:
                # 从队列获取任务
                task = self.extraction_queue.get()
                
                if task is None:  # 退出信号
                    break
                
                image_path, callback = task
                
                try:
                    # 提取特征
                    features = self.extract_multiple_features(image_path)
                    
                    # 调用回调函数
                    if callback and features:
                        callback(features, None)
                    elif callback:
                        callback(None, "特征提取失败")
                        
                except Exception as e:
                    self.logger.error(f"特征提取失败: {e}")
                    if callback:
                        callback(None, str(e))
                
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
            finally:
                self.extraction_queue.task_done()
    
    def extract_features_async(self, image_path: str, callback=None):
        """异步提取图像特征"""
        try:
            self.extraction_queue.put((image_path, callback))
            return True
        except Exception as e:
            self.logger.error(f"添加异步提取任务失败: {e}")
            return False
    
    def extract_multiple_features(self, image_path: str) -> Optional[ImageFeatures]:
        """提取多种图像特征"""
        try:
            # 验证图像文件
            if not self._validate_image(image_path):
                self.logger.warning(f"无效的图像文件: {image_path}")
                return None
            
            # 加载图像
            image = self._load_image(image_path)
            if image is None:
                return None
            
            # 创建特征对象
            features = ImageFeatures(
                file_id=0,  # 临时ID，实际使用时会被替换
                extraction_time=datetime.now(),
                feature_version=self.config['feature_version']
            )
            
            # 提取颜色特征
            features.color_histogram = self.extract_color_histogram(image)
            
            # 提取纹理特征
            features.texture_features = self.extract_texture_features(image)
            
            # 提取边缘特征
            features.edge_features = self.extract_edge_features(image)
            
            # 计算感知哈希
            features.image_hash = self.calculate_perceptual_hash(image)
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取多种特征失败: {e}")
            return None
    
    def _validate_image(self, image_path: str) -> bool:
        """验证图像文件"""
        try:
            if not os.path.exists(image_path):
                return False
            
            # 检查文件扩展名
            _, ext = os.path.splitext(image_path)
            if ext.lower() not in self.config['supported_formats']:
                return False
            
            # 检查文件大小（避免处理过大的文件）
            file_size = os.path.getsize(image_path)
            if file_size > 50 * 1024 * 1024:  # 50MB限制
                self.logger.warning(f"图像文件过大: {image_path} ({file_size} bytes)")
                return False
            
            # 尝试打开图像验证格式
            if HAS_PIL:
                try:
                    with Image.open(image_path) as img:
                        img.verify()
                    return True
                except Exception:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"图像验证失败: {e}")
            return False
    
    def _load_image(self, image_path: str) -> Optional[np.ndarray]:
        """加载图像并预处理"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                self.logger.error("缺少OpenCV或NumPy库，无法加载图像")
                return None

            # 规范化路径
            image_path = os.path.normpath(image_path)

            # 使用OpenCV读取图像（处理中文路径）
            image = None
            try:
                # 方法1：直接读取
                image = cv2.imread(image_path)
            except:
                pass

            if image is None:
                try:
                    # 方法2：使用numpy处理中文路径
                    image_data = np.fromfile(image_path, dtype=np.uint8)
                    image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
                except Exception as e:
                    self.logger.warning(f"使用numpy方法读取图像失败: {e}")

            if image is None:
                try:
                    # 方法3：使用PIL作为备选
                    from PIL import Image as PILImage
                    pil_image = PILImage.open(image_path)
                    # 转换为RGB
                    if pil_image.mode != 'RGB':
                        pil_image = pil_image.convert('RGB')
                    # 转换为numpy数组
                    image = np.array(pil_image)
                    # PIL使用RGB，OpenCV使用BGR，所以需要转换
                    image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                except Exception as e:
                    self.logger.warning(f"使用PIL方法读取图像失败: {e}")

            if image is None:
                self.logger.warning(f"无法读取图像: {image_path}")
                return None

            # 转换为RGB（OpenCV默认是BGR）
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 调整图像大小
            max_size = self.config['max_image_size']
            h, w = image.shape[:2]

            if max(h, w) > max_size:
                # 保持宽高比
                if h > w:
                    new_h, new_w = max_size, int(w * max_size / h)
                else:
                    new_h, new_w = int(h * max_size / w), max_size

                image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)

            return image

        except Exception as e:
            self.logger.error(f"加载图像失败: {e}")
            return None
    
    def extract_color_histogram(self, image: np.ndarray) -> Optional[np.ndarray]:
        """提取RGB颜色直方图特征"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return None
            
            # 获取配置的柱数
            bins = self.config['color_histogram_bins_reduced']
            
            # 计算每个通道的直方图
            hist_r = cv2.calcHist([image], [0], None, [bins], [0, 256])
            hist_g = cv2.calcHist([image], [1], None, [bins], [0, 256])
            hist_b = cv2.calcHist([image], [2], None, [bins], [0, 256])
            
            # 归一化直方图
            cv2.normalize(hist_r, hist_r, 0, 1, cv2.NORM_MINMAX)
            cv2.normalize(hist_g, hist_g, 0, 1, cv2.NORM_MINMAX)
            cv2.normalize(hist_b, hist_b, 0, 1, cv2.NORM_MINMAX)
            
            # 合并直方图
            hist = np.concatenate([hist_r, hist_g, hist_b])
            
            return hist.flatten().astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"提取RGB颜色直方图失败: {e}")
            return None
    
    def extract_hsv_color_histogram(self, image: np.ndarray) -> Optional[np.ndarray]:
        """提取HSV颜色直方图特征"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return None
            
            # 转换为HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            
            # 获取HSV直方图的柱数配置
            h_bins, s_bins, v_bins = self.config['hsv_histogram_bins']
            
            # 计算HSV直方图
            hist_h = cv2.calcHist([hsv], [0], None, [h_bins], [0, 180])  # H: 0-179
            hist_s = cv2.calcHist([hsv], [1], None, [s_bins], [0, 256])  # S: 0-255
            hist_v = cv2.calcHist([hsv], [2], None, [v_bins], [0, 256])  # V: 0-255
            
            # 归一化直方图
            cv2.normalize(hist_h, hist_h, 0, 1, cv2.NORM_MINMAX)
            cv2.normalize(hist_s, hist_s, 0, 1, cv2.NORM_MINMAX)
            cv2.normalize(hist_v, hist_v, 0, 1, cv2.NORM_MINMAX)
            
            # 合并直方图
            hist = np.concatenate([hist_h, hist_s, hist_v])
            
            return hist.flatten().astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"提取HSV颜色直方图失败: {e}")
            return None
    
    def extract_dominant_colors(self, image: np.ndarray, k: int = 5) -> Optional[np.ndarray]:
        """提取主要颜色特征（使用K-means聚类）"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return None
            
            # 将图像重塑为像素数组
            pixels = image.reshape((-1, 3))
            pixels = np.float32(pixels)
            
            # 使用K-means聚类找到主要颜色
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(pixels, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
            
            # 计算每个颜色的比例
            unique, counts = np.unique(labels, return_counts=True)
            proportions = counts / len(labels)
            
            # 按比例排序
            sorted_indices = np.argsort(proportions)[::-1]
            dominant_colors = centers[sorted_indices]
            color_proportions = proportions[sorted_indices]
            
            # 组合颜色和比例信息
            color_features = []
            for i in range(k):
                color_features.extend(dominant_colors[i])  # RGB值
                color_features.append(color_proportions[i])  # 比例
            
            return np.array(color_features, dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"提取主要颜色失败: {e}")
            return None
    
    def extract_color_moments(self, image: np.ndarray) -> Optional[np.ndarray]:
        """提取颜色矩特征（均值、标准差、偏度）"""
        try:
            if not HAS_NUMPY:
                return None
            
            # 计算每个通道的颜色矩
            moments = []
            
            for channel in range(3):  # RGB三个通道
                channel_data = image[:, :, channel].flatten()
                
                # 计算一阶矩（均值）
                mean = np.mean(channel_data)
                moments.append(mean)
                
                # 计算二阶矩（标准差）
                std = np.std(channel_data)
                moments.append(std)
                
                # 计算三阶矩（偏度）
                if std > 0:
                    skewness = np.mean(((channel_data - mean) / std) ** 3)
                else:
                    skewness = 0
                moments.append(skewness)
            
            return np.array(moments, dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"提取颜色矩失败: {e}")
            return None
    
    def extract_texture_features(self, image: np.ndarray) -> Optional[np.ndarray]:
        """提取纹理特征（简化版本）"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return None
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 计算灰度共生矩阵特征（简化版本）
            # 这里使用简单的统计特征代替完整的GLCM
            mean = np.mean(gray)
            std = np.std(gray)
            
            # 计算局部二值模式（LBP）特征（简化版本）
            # 完整的LBP实现较为复杂，这里使用简化版本
            texture_features = np.array([mean, std], dtype=np.float32)
            
            return texture_features
            
        except Exception as e:
            self.logger.error(f"提取纹理特征失败: {e}")
            return None
    
    def extract_edge_features(self, image: np.ndarray) -> Optional[np.ndarray]:
        """提取边缘特征"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return None
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 应用Canny边缘检测
            threshold1 = self.config['edge_threshold1']
            threshold2 = self.config['edge_threshold2']
            edges = cv2.Canny(gray, threshold1, threshold2)
            
            # 计算边缘密度（边缘像素数量/总像素数量）
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            # 计算边缘方向直方图（简化版本）
            # 将边缘分为8个方向区间
            edge_features = np.array([edge_density], dtype=np.float32)
            
            return edge_features
            
        except Exception as e:
            self.logger.error(f"提取边缘特征失败: {e}")
            return None
    
    def calculate_perceptual_hash(self, image: np.ndarray) -> str:
        """计算感知哈希"""
        try:
            if not HAS_OPENCV or not HAS_NUMPY:
                return ""
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 调整大小为8x8
            small = cv2.resize(gray, (8, 8), interpolation=cv2.INTER_AREA)
            
            # 计算DCT变换
            dct = cv2.dct(np.float32(small))
            
            # 使用左上角的8x8区域（低频信息）
            dct_low = dct[:8, :8]
            
            # 计算均值（不包括第一个直流分量）
            mean = np.mean(dct_low[1:])
            
            # 生成哈希值
            hash_str = ''
            for i in range(8):
                for j in range(8):
                    if dct_low[i, j] > mean:
                        hash_str += '1'
                    else:
                        hash_str += '0'
            
            return hash_str
            
        except Exception as e:
            self.logger.error(f"计算感知哈希失败: {e}")
            return ""
    
    def shutdown(self):
        """关闭服务"""
        # 发送退出信号给所有工作线程
        for _ in self.worker_threads:
            self.extraction_queue.put(None)
        
        # 等待所有线程结束
        for thread in self.worker_threads:
            thread.join(timeout=1.0)
        
        self.logger.info("特征提取服务已关闭")