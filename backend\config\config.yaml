database:
  database_path: backend/data/file_share_system.db
download:
  enable_batch_download: true
  enable_folder_download: true
  enable_single_download: true
  encryption_after_downloads: 3
  max_batch_files: 100
  max_package_size: 524288000
  password_request_limit: 5
file_share:
  allowed_extensions:
  - .jpg
  - .jpeg
  - .png
  - .gif
  - .bmp
  - .tiff
  - .tif
  - .psd
  - .ai
  - .eps
  - .pdf
  - .doc
  - .docx
  - .xls
  - .xlsx
  - .ppt
  - .pptx
  - .txt
  - .zip
  - .rar
  - .7z
  max_file_size: 1073741824
  shared_folders: []
  thumbnail_sizes:
    large:
    - 600
    - 600
    medium:
    - 300
    - 300
    small:
    - 150
    - 150
    xlarge:
    - 1200
    - 1200
marquee:
  animation_duration: 20
  enabled: true
  font_size: 24
  hidden: false
  message: 重要提醒：请及时清理个人下载记录
  scroll_speed: 30
  theme: info
marquee_presets:
- 欢迎使用企业级文件共享系统！
- 系统维护通知：今晚22:00-24:00进行系统升级维护
- 新功能上线：支持PSD、AI等专业格式预览
- 重要提醒：请及时清理个人下载记录
- 系统公告：为保障系统安全，请勿下载可疑文件
- 新增预设：支持动态添加和删除预设消息
- 测试预设：字体大小和滚动速度可调节
monitoring:
  alert_thresholds:
    max_concurrent_users: 100
    max_download_speed: 10485760
    max_search_per_minute: 60
  enable_activity_log: true
  enable_real_time_monitor: true
  log_retention_days: 90
network:
  enable_external_access: false
  enable_internal_access: true
  internal_networks:
  - 192.168.0.0/16
  - 10.0.0.0/8
  - 172.16.0.0/12
  rate_limit:
    downloads_per_hour: 100
    requests_per_minute: 60
notifications:
  enable_rolling_notifications: true
  enable_screenshots: true
  max_notifications: 10
  notification_duration: 5000
permissions:
  admin_permissions:
  - read
  - write
  - delete
  - admin
  default_user_permissions:
  - read
  guest_permissions:
  - read
search:
  enable_image_search: true
  enable_text_search: true
  enable_image_upload_search: true
  index_path: ./data/search_index
  max_search_results: 1000
  image_search_threshold: 0.7
  max_upload_image_size: 10485760
security:
  ban_duration: 300
  enable_registration: false
  max_login_attempts: 5
  require_license_key: true
  sensitive_file_patterns:
  - '*secret*'
  - '*private*'
  - '*confidential*'
  session_timeout: 3600
server:
  debug: false
  frontend_port: 8084
  host: 0.0.0.0
  max_workers: 10
  port: 8086
  timeout: 300
system:
  backup_directory: ./backup
  data_directory: ./data
  language: zh_CN
  log_directory: ./logs
  temp_directory: ./temp
  timezone: Asia/Shanghai
