#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import sqlite3
from datetime import datetime

def check_image_features():
    """检查图像特征提取结果"""
    
    db_path = "backend/data/file_share_system.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 检查图像特征提取结果")
        print("=" * 50)
        
        # 检查文件表
        cursor.execute("SELECT COUNT(*) FROM shared_files WHERE is_image = 1")
        image_count = cursor.fetchone()[0]
        print(f"📊 图像文件总数: {image_count}")

        # 检查特征表
        cursor.execute("SELECT COUNT(*) FROM image_features")
        feature_count = cursor.fetchone()[0]
        print(f"🎯 已提取特征的图像数: {feature_count}")

        # 检查具体的特征数据
        cursor.execute("""
            SELECT f.filename, f.relative_path, if_.feature_hash, if_.image_hash,
                   if_.keypoint_count, if_.extraction_time, if_.feature_version
            FROM shared_files f
            LEFT JOIN image_features if_ ON f.id = if_.file_id
            WHERE f.is_image = 1
            ORDER BY f.filename
        """)
        
        results = cursor.fetchall()
        
        print("\n📋 详细特征信息:")
        print("-" * 80)
        
        for row in results:
            filename, relative_path, feature_hash, image_hash, keypoint_count, extraction_time, feature_version = row

            print(f"📁 文件: {filename}")
            print(f"   路径: {relative_path}")
            
            if feature_hash:
                print(f"   ✅ 特征已提取")
                print(f"   🔑 特征哈希: {feature_hash[:16]}...")
                print(f"   🖼️ 图像哈希: {image_hash[:16] if image_hash else 'N/A'}...")
                print(f"   🎯 关键点数量: {keypoint_count}")
                print(f"   ⏰ 提取时间: {extraction_time}")
                print(f"   📦 特征版本: {feature_version}")
            else:
                print(f"   ❌ 特征未提取")
            
            print()
        
        # 检查特征文件是否存在
        print("🗂️ 检查特征文件存储:")
        print("-" * 50)
        
        cursor.execute("""
            SELECT filename, descriptors_path
            FROM shared_files f
            JOIN image_features if_ ON f.id = if_.file_id
            WHERE if_.descriptors_path IS NOT NULL
        """)
        
        descriptor_files = cursor.fetchall()
        
        for filename, desc_path in descriptor_files:
            if desc_path and os.path.exists(desc_path):
                file_size = os.path.getsize(desc_path)
                print(f"✅ {filename}: {desc_path} ({file_size} bytes)")
            else:
                print(f"❌ {filename}: 特征文件不存在 - {desc_path}")
        
        conn.close()
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 总结:")
        print(f"   图像文件: {image_count}")
        print(f"   已提取特征: {feature_count}")
        print(f"   提取成功率: {(feature_count/image_count*100) if image_count > 0 else 0:.1f}%")
        
        if feature_count > 0:
            print("✅ 图像搜索功能可以使用！")
        else:
            print("❌ 需要重新扫描文件夹以提取特征")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_image_features()
