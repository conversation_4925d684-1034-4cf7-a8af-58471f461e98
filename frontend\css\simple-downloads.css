/* 增强的下载记录样式 */
.download-records-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.download-records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.download-records-header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
}

.download-records-header i {
    margin-right: 10px;
    color: #007bff;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 筛选面板 */
.filter-panel {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

/* 统计信息 */
.download-stats {
    display: flex;
    gap: 30px;
    background: #e3f2fd;
    padding: 15px 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #1976d2;
}

/* 加载和错误状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 16px;
}

.loading i {
    margin-right: 8px;
    color: #007bff;
}

.error {
    text-align: center;
    padding: 40px;
    font-size: 16px;
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    margin: 20px 0;
}

/* 记录列表 */
.record-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
    background: white;
    border-radius: 6px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.record-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.record-info {
    flex: 1;
}

.record-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.record-name i {
    margin-right: 8px;
    color: #007bff;
}

.record-meta {
    color: #666;
    font-size: 14px;
}

.encrypted-badge {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-left: 8px;
}

/* 分页控件 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #dee2e6;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.pagination-controls button:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

#page-info {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.empty {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 2px dashed #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .download-records-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .filter-row {
        flex-direction: column;
        gap: 15px;
    }

    .filter-group {
        min-width: 100%;
    }

    .filter-actions {
        margin-left: 0;
        width: 100%;
    }

    .download-stats {
        flex-direction: column;
        gap: 15px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
    }
}